﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.BusinessTypesRow>;
using MyRow = ReconnBooks.Default.BusinessTypesRow;

namespace ReconnBooks.Default;

public interface IBusinessTypesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessTypesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessTypesRetrieveHandler
{
    public BusinessTypesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}