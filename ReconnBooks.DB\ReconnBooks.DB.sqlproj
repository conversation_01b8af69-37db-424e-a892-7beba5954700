﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>ReconnBooks.DB</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{b85d0a56-cf50-41aa-b2f9-6456c151c19a}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql160DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>ReconnBooks.DB</RootNamespace>
    <AssemblyName>ReconnBooks.DB</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="Tables" />
    <Folder Include="Tables\Administration" />
    <Folder Include="Tables\MasterSettings" />
    <Folder Include="Tables\MasterSettings\General" />
    <Folder Include="Tables\MasterSettings\TaxReturns" />
    <Folder Include="Tables\MasterSettings\Financial" />
    <Folder Include="Tables\Sales" />
    <Folder Include="Tables\Sales\Quotations" />
    <Folder Include="Tables\Sales\Customers" />
    <Folder Include="Tables\Sales\SalesOrders" />
    <Folder Include="Tables\Products And Services" />
    <Folder Include="Tables\Sales\DeliveryChallan" />
    <Folder Include="Tables\Sales\Proforma Invoice" />
    <Folder Include="Tables\Sales\Invoices" />
    <Folder Include="Tables\Purchase" />
    <Folder Include="Tables\Purchase\PurchaseOrders" />
    <Folder Include="Tables\Accounts" />
    <Folder Include="Tables\Sales\SalesReturns" />
    <Folder Include="Tables\Purchase\POAmendments" />
    <Folder Include="Tables\Purchase\GoodsReceivedNotes" />
    <Folder Include="Tables\Purchase\VendorBillsReceived" />
    <Folder Include="Tables\Purchase\VendorPayments" />
    <Folder Include="Tables\Purchase\DebitNotes" />
    <Folder Include="Tables\Stores &amp; Inventory" />
    <Folder Include="Tables\Purchase\Purchase Returns" />
    <Folder Include="Tables\Sales\CreditNotes" />
    <Folder Include="Tables\Sales\SalesRegister" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="Tables\Administration\BusinessCategories.sql" />
    <Build Include="Tables\Administration\BusinessGroups.sql" />
    <Build Include="Tables\Administration\BusinessTypes.sql" />
    <Build Include="Tables\Administration\Clients.sql" />
    <Build Include="Tables\Administration\ClientUsers.sql" />
    <Build Include="Tables\Administration\Consultants.sql" />
    <Build Include="Tables\Administration\ConsultantUsers.sql" />
    <Build Include="Tables\Administration\DragDropSample.sql" />
    <Build Include="Tables\Administration\Employees.sql" />
    <Build Include="Tables\Administration\Exceptions.sql" />
    <Build Include="Tables\Administration\RolePermissions.sql" />
    <Build Include="Tables\Administration\Roles.sql" />
    <Build Include="Tables\Administration\UserPermissions.sql" />
    <Build Include="Tables\Administration\UserPreferences.sql" />
    <Build Include="Tables\Administration\UserRoles.sql" />
    <Build Include="Tables\Administration\Users.sql" />
    <Build Include="Tables\Administration\UserTypes.sql" />
    <Build Include="Tables\Administration\VersionInfo.sql" />
    <Build Include="Tables\MasterSettings\Financial\PaymentTerms.sql" />
    <Build Include="Tables\MasterSettings\Financial\Narrations.sql" />
    <Build Include="Tables\MasterSettings\Financial\ModeOfPayments.sql" />
    <Build Include="Tables\MasterSettings\Financial\HeaderNote.sql" />
    <Build Include="Tables\MasterSettings\Financial\FootNotes.sql" />
    <Build Include="Tables\MasterSettings\Financial\FinancialYears.sql" />
    <Build Include="Tables\MasterSettings\General\AddressedTo.sql" />
    <Build Include="Tables\MasterSettings\General\Banks.sql" />
    <Build Include="Tables\MasterSettings\General\Cities.sql" />
    <Build Include="Tables\MasterSettings\General\Countries.sql" />
    <Build Include="Tables\MasterSettings\General\Departments.sql" />
    <Build Include="Tables\MasterSettings\General\Designations.sql" />
    <Build Include="Tables\MasterSettings\General\Districts.sql" />
    <Build Include="Tables\MasterSettings\General\Documents.sql" />
    <Build Include="Tables\MasterSettings\General\Instruments.sql" />
    <Build Include="Tables\MasterSettings\General\Notes.sql" />
    <Build Include="Tables\MasterSettings\General\Positions.sql" />
    <Build Include="Tables\MasterSettings\General\RejectionReasons.sql" />
    <Build Include="Tables\MasterSettings\General\ReplacementMethods.sql" />
    <Build Include="Tables\MasterSettings\General\States.sql" />
    <Build Include="Tables\MasterSettings\General\Titles.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\GSTRates.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\GstReturns.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\HSNSACCodes.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\NatureOfSupply.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\SupplyTypes.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\ReturnsFilingForms.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\TCSRates.sql" />
    <Build Include="Tables\MasterSettings\TaxReturns\TDSRates.sql" />
    <Build Include="Tables\Sales\Customers\Customers.sql" />
    <Build Include="Tables\Sales\Customers\CustomerDetails.sql" />
    <Build Include="Tables\Sales\Customers\CustomerContacts.sql" />
    <Build Include="Tables\Sales\Customers\CustomerRepresentatives.sql" />
    <Build Include="Tables\Sales\Quotations\Quotations.sql" />
    <Build Include="Tables\Sales\Quotations\QuotationDetails.sql" />
    <Build Include="Tables\Sales\SalesOrders\SalesOrders.sql" />
    <Build Include="Tables\Sales\SalesOrders\SalesOrderDetails.sql" />
    <Build Include="Tables\Products And Services\ItemLog.sql" />
    <Build Include="Tables\Products And Services\Units.sql" />
    <Build Include="Tables\Products And Services\UQCs.sql" />
    <Build Include="Tables\Sales\DeliveryChallan\DeliveryChallans.sql" />
    <Build Include="Tables\Sales\DeliveryChallan\DeliveryChallanDetails.sql" />
    <Build Include="Tables\Sales\Proforma Invoice\ProformaInvoices.sql" />
    <Build Include="Tables\Sales\Proforma Invoice\ProformaInvoiceDetails.sql" />
    <Build Include="Tables\Sales\Invoices\Invoices.sql" />
    <Build Include="Tables\Sales\Invoices\InvoiceDetails.sql" />
    <Build Include="Tables\Products And Services\CommodityTypes.sql" />
    <Build Include="Tables\Products And Services\ProductGroups.sql" />
    <Build Include="Tables\Products And Services\ProductMake.sql" />
    <Build Include="Tables\Products And Services\ProductTypes.sql" />
    <Build Include="Tables\Products And Services\Services.sql" />
    <Build Include="Tables\MasterSettings\General\FeedBacks.sql" />
    <Build Include="Tables\Administration\BusinessUnits.sql" />
    <Build Include="Tables\Sales\Quotations\QuotationAnnexure.sql" />
    <Build Include="Tables\Sales\DeliveryChallan\DeliveryNotes.sql" />
    <Build Include="Tables\Sales\DeliveryChallan\DeliveryNoteDetails.sql" />
    <Build Include="Tables\Purchase\PurchaseOrders\PurchaseOrders.sql" />
    <Build Include="Tables\Purchase\PurchaseOrders\Vendors.sql" />
    <Build Include="Tables\Purchase\PurchaseOrders\VendorContacts.sql" />
    <Build Include="Tables\Purchase\PurchaseOrders\PurchaseOrderDetails.sql" />
    <Build Include="Tables\Products And Services\Commodities.sql" />
    <Build Include="Tables\Accounts\Receipts.sql" />
    <Build Include="Tables\Accounts\ReceiptDetails.sql" />
    <Build Include="Tables\Sales\SalesReturns\SalesReturns.sql" />
    <Build Include="Tables\Sales\SalesReturns\SalesReturnDetails.sql" />
    <Build Include="Tables\Purchase\POAmendments\POAmendments.sql" />
    <Build Include="Tables\Purchase\POAmendments\POAmendmentDetails.sql" />
    <Build Include="Tables\Purchase\GoodsReceivedNotes\GRNTypes.sql" />
    <Build Include="Tables\Purchase\GoodsReceivedNotes\GRNs.sql" />
    <Build Include="Tables\Purchase\GoodsReceivedNotes\GRNDetails.sql" />
    <Build Include="Tables\Stores &amp; Inventory\Locations.sql" />
    <Build Include="Tables\Stores &amp; Inventory\Warehouses.sql" />
    <Build Include="Tables\Stores &amp; Inventory\Stores.sql" />
    <Build Include="Tables\Stores &amp; Inventory\Racks.sql" />
    <Build Include="Tables\Purchase\VendorPayments\VendorPayments.sql" />
    <Build Include="Tables\Purchase\VendorPayments\VendorPaymentDetails.sql" />
    <Build Include="Tables\Purchase\VendorBillsReceived\VendorBills.sql" />
    <Build Include="Tables\Purchase\VendorBillsReceived\VendorBillDetails.sql" />
    <Build Include="Tables\Purchase\VendorBillsReceived\VendorBills.sql" />
    <Build Include="Tables\Purchase\Purchase Returns\PurchaseReturnDetails.sql" />
    <Build Include="Tables\Purchase\Purchase Returns\PurchaseReturns.sql" />
    <Build Include="Tables\Sales\CreditNotes\CreditNotes.sql" />
    <Build Include="Tables\Sales\CreditNotes\CreditNoteDetails.sql" />
    <Build Include="Tables\Purchase\DebitNotes\DebitNotes.sql" />
    <Build Include="Tables\Purchase\DebitNotes\DebitNoteDetails.sql" />
    <Build Include="Tables\MasterSettings\General\DataAuditLog.sql" />
    <Build Include="Tables\Sales\SalesRegister\SalesRegister.sql" />
    <Build Include="Tables\Administration\ClientBankAccounts.sql" />
    <Build Include="Tables\Administration\ConsultantBankAccounts.sql" />
    <Build Include="Tables\MasterSettings\Financial\LedgerAccounts.sql" />
    <Build Include="Tables\MasterSettings\Financial\SecondaryGroups.sql" />
    <Build Include="Tables\Stores &amp; Inventory\Mail.sql" />
    <Build Include="Tables\MasterSettings\Financial\OpeningBalances.sql" />
    <Build Include="Tables\MasterSettings\Financial\PrimaryGroups.sql" />
    <Build Include="Tables\Products And Services\ProductCategories.sql" />

  </ItemGroup>
</Project>