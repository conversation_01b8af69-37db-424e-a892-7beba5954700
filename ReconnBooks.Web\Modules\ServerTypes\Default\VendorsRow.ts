﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { VendorContactsRow } from "./VendorContactsRow";

export interface VendorsRow {
    RowNumber?: number;
    VendorId?: number;
    VendorName?: string;
    ShortName?: string;
    AddressedToId?: number;
    GSTIN?: string;
    PlaceOfSupplyId?: number;
    NatureOfSupplyId?: number;
    SupplyTypeId?: number;
    PAN?: string;
    CINNo?: string;
    TAN?: string;
    UdyamNo?: string;
    IECNo?: string;
    BillingAddress?: string;
    BillingCityId?: number;
    BillingPinCode?: string;
    CorrespondenceAddress?: string;
    CorrespondenceCityId?: number;
    CorrespondencePinCode?: string;
    PhoneNo?: string;
    MobileNo?: string;
    FaxNo?: string;
    HomePage?: string;
    EMailId?: string;
    VendorContactsList?: VendorContactsRow[];
    BankId?: number;
    BranchName?: string;
    AccountName?: string;
    AccountNumber?: string;
    IFSCCode?: string;
    BranchCode?: string;
    UploadFiles?: string;
    ClientId?: number;
    AddressedTo?: string;
    PlaceOfSupplyStateName?: string;
    PlaceOfSupplyStateCode?: string;
    PlaceOfSupplyStateCodeNo?: string;
    NatureOfSupply?: string;
    SupplyType?: string;
    BillingCityCityName?: string;
    CorrespondenceCityCityName?: string;
    BankName?: string;
    ClientName?: string;
}

export abstract class VendorsRow {
    static readonly idProperty = 'VendorId';
    static readonly nameProperty = 'VendorName';
    static readonly localTextPrefix = 'Default.Vendors';
    static readonly lookupKey = 'Default.Vendors';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<VendorsRow>('Default.Vendors') }
    static async getLookupAsync() { return getLookupAsync<VendorsRow>('Default.Vendors') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorsRow>();
}