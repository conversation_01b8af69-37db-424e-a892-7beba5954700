﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface GrnTypesRow {
    RowNumber?: number;
    GRNTypeId?: number;
    GRNTypeName?: string;
    Description?: string;
}

export abstract class GrnTypesRow {
    static readonly idProperty = 'GRNTypeId';
    static readonly nameProperty = 'GRNTypeName';
    static readonly localTextPrefix = 'Default.GrnTypes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GrnTypesRow>();
}