﻿CREATE TABLE [dbo].[Stores]
(
    [StoreId]           INT				    NOT NULL		IDENTITY (1, 1),
    [StoreName]         NVARCHAR (200)      NOT NULL,
    [StoreDescription]  NVARCHAR (500)          NULL,
    [Remarks]           NVARCHAR (MAX)          NULL,
    [LocationId]        INT				    NOT NULL    CONSTRAINT [DF_Stores_LocationId]   DEFAULT ((1)),
    [WarehouseId]       INT				    NOT NULL    CONSTRAINT [DF_Stores_WarehouseId]  DEFAULT ((1)),
    [SetDefault]        BIT             	NOT NULL	CONSTRAINT [DF_Stores_SetDefault]   DEFAULT ((1)),
    [Discontinued]      BIT             	NOT NULL	CONSTRAINT [DF_Stores_Discontinued] DEFAULT ((0)),
    [ClientId]          INT				    NOT NULL	CONSTRAINT [DF_Stores_ClientId]     DEFAULT ((0)),
   
    CONSTRAINT [PK_Stores] PRIMARY KEY  CLUSTERED   ([StoreId] ASC),
    CONSTRAINT [FK_Stores_Locations]    FOREIGN KEY ([LocationId])  REFERENCES [dbo].[Locations] ([LocationId]),
    CONSTRAINT [FK_Stores_Warehouses]   FOREIGN KEY ([WarehouseId]) REFERENCES [dbo].[Warehouses] ([WarehouseId]),
    CONSTRAINT [FK_Stores_Clients]      FOREIGN KEY ([ClientId])    REFERENCES [dbo].[Clients] ([ClientId]), 
);

GO
CREATE NONCLUSTERED INDEX [Locations]
    ON [dbo].[Stores]([LocationId] ASC);

GO
CREATE NONCLUSTERED INDEX [Warehouses]
    ON [dbo].[Stores]([WarehouseId] ASC);
