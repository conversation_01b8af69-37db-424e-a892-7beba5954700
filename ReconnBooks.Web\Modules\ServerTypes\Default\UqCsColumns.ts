﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { UqCsRow } from "./UqCsRow";

export interface UqCsColumns {
    RowNumber: Column<UqCsRow>;
    QuantityName: Column<UqCsRow>;
    QuantityType: Column<UqCsRow>;
    UniqueQuantityCode: Column<UqCsRow>;
    UqcName: Column<UqCsRow>;
    UQCDescription: Column<UqCsRow>;
    UqcId: Column<UqCsRow>;
}

export class UqCsColumns extends ColumnsBase<UqCsRow> {
    static readonly columnsKey = 'Default.UqCs';
    static readonly Fields = fieldsProxy<UqCsColumns>();
}