using Serenity.ComponentModel;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.ReturnsFilingForms")]
[BasedOnRow(typeof(ReturnsFilingFormsRow), CheckNames = true)]
public class ReturnsFilingFormsColumns
{
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Returns filing Form"), Width(150)]
    public string ReturnsFilingForm { get; set; }

    [Hidden]
    [DisplayName("Nature Of Supply"), Width(150)]
    public string NatureOfSupply { get; set; }

    [DisplayName("Supply Type"), Width(150)]
    public string SupplyType { get; set; }

    [Hidden]
    [DisplayName("Description")]
    public string Description { get; set; }

    [DisplayName("Filer"), Width(150)]
    public string Filer { get; set; }

    [DisplayName("Frequency"), Width(150)]
    public string Frequency { get; set; }

    [DisplayName("Due Date"), Width(80)]
    public DateTime DueDate { get; set; }

    [Hidden]
    [DisplayName("Remarks"), Width(150)]
    public string Remarks { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int ReturnsFilingFormId { get; set; }
}