using Serenity.ComponentModel;
using System;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.GrNs")]
[BasedOnRow(typeof(GrNsRow), CheckNames = true)]
public class GrNsForm
{
    [Category("GRN Details"), Collapsible]
    [HalfWidth(UntilNext = true)]
    [DisplayName("GRN No.")]
    public string GRNNo { get; set; }

    [DisplayName("GRN Date")]
    [DefaultValue("now")]
    public DateTime GRNDate { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Vendor Name")]
    public int VendorId { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("GRN Type")]
    public int GRNTypeId { get; set; }

    [ReadOnly(true)]
    [DisplayName("GST No.")]
    public string GSTIN { get; set; }

    [Category("Reference Details"), Collapsible]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Purchase Order No.")]
    public int PurchaseOrderId { get; set; }

    [DisplayName("Financial Year")]
    public int FinancialYearId { get; set; }

    [DisplayName("Vendor DC/Inv. No.")]
    public string VendorDcInvoiceNo { get; set; }

    [DisplayName("DC/Inv. Date")]
    public DateTime VendorDcInvoiceDate { get; set; }

    //--------------------------------------------------------------
    [Category("Products & Services"), Collapsible]
    [FullWidth(UntilNext = true)]
    [GrnDetailsGridEditor]
    public List<GrnDetailsRow> GrnDetailsList { get; set; }
    //--------------------------------------------------------------

    [Category("Delivery Details "), Collapsible]
    [FullWidth(UntilNext = true)]
    [DisplayName("Shipped Through")]
    public string ShippedThrough { get; set; }

    [DisplayName("Delivery Address")]
    public string DeliveryAddress { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("Delivery City")]
    public int DeliveryCityId { get; set; }

    [DisplayName("Pin Code")]
    public int DeliveryPinCode { get; set; }

    [DisplayName("Shipping Docket No.")]
    public string ShippingDocketNo { get; set; }

    [DisplayName("Received By")]
    public int ReceivedByEmployeeId { get; set; }

    [DisplayName("Vehicle No.")]
    public string VehicleNo { get; set; }

    [DisplayName("Vehicle Type")]
    public string VehicleType { get; set; }

    [DisplayName("Gate Pass No.")]
    public string GatePassNo { get; set; }

    [DisplayName("Gate Pass Date")]
    public DateTime GatePassDate { get; set; }

    [Hidden]
    [DisplayName("Inspection")]
    public string Inspection { get; set; }

    [FullWidth(UntilNext = true), TextAreaEditor(Rows = 3)]
    [DisplayName("Remarks")]
    public string Remarks { get; set; }
    public bool AuthorizedStatus { get; set; }

    [Category("Upload Documents"), Collapsible(Collapsed = true)]
    [MultipleFileUploadEditor]
    [DisplayName("Attach Files")]
    public string UploadDocuments { get; set; }

  //  public int ClientId { get; set; }

    [Hidden]
    public int PreparedByUserId { get; set; }
    [Hidden]
    public DateTime PreparedDate { get; set; }
    [Hidden]
    public int VerifiedByUserId { get; set; }
    [Hidden]
    public DateTime VerifiedDate { get; set; }
    [Hidden]
    public int AuthorizedByUserId { get; set; }
    [Hidden]
    public DateTime AuthorizedDate { get; set; }
    [Hidden]
    public int ModifiedByUserId { get; set; }
    [Hidden]
    public DateTime ModifiedDate { get; set; }
    [Hidden]
    public int CancelledByUserId { get; set; }
    [Hidden]
    public DateTime CancelledDate { get; set; }
}
