namespace ReconnBooks.Administration;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Administration"), <PERSON><PERSON>ame("Languages")]
[Display<PERSON><PERSON>("Languages"), Instance<PERSON>ame("Language"), GenerateFields]
[ReadPermission(PermissionKeys.Translation)]
[ModifyPermission(PermissionKeys.Translation)]
[LookupScript(typeof(Lookups.LanguageLookup))]
public sealed partial class LanguageRow : Row<LanguageRow.RowFields>
{
    [DisplayName("Id"), Identity, IdProperty]
    public int? Id { get => fields.Id[this]; set => fields.Id[this] = value; }

    [Display<PERSON><PERSON>("Language Id"), <PERSON><PERSON>(10), NotNull, QuickSearch]
    public string LanguageId { get => fields.LanguageId[this]; set => fields.LanguageId[this] = value; }

    [DisplayName("Language Name"), <PERSON><PERSON>(50), Not<PERSON>ull, QuickSearch, NameProperty]
    public string LanguageName { get => fields.LanguageName[this]; set => fields.LanguageName[this] = value; }
}