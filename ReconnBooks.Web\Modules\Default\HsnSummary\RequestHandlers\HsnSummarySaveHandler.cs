﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.HsnSummaryRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.HsnSummaryRow;

namespace ReconnBooks.Default;

public interface IHsnSummarySaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> { }

public class HsnSummarySaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IHsnSummarySaveHandler
{
    public HsnSummarySaveHandler(IRequestContext context)
            : base(context)
    {
    }
}