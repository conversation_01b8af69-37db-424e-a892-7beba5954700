import { Decorators, EntityDialog } from "@serenity-is/corelib";
import { DialogUtils, GridEditorDialog } from "@serenity-is/extensions";

Decorators.registerClass()
export class PendingChangesConfirmGridEditorDialog<TEntity> extends GridEditorDialog<TEntity> {

    protected loadedState: string;

    getSaveState() {
        try {
            return JSON.stringify(this.getSaveEntity());
        }
        catch (e) {
            return null;
        }
    }

    protected setDialogsLoadedState() {
        this.loadedState = this.getSaveState();
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();

        DialogUtils.pendingChangesConfirmation(this.element, () => this.getSaveState() != this.loadedState);
    }
}
