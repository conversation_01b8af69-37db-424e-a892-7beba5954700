﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { QuotationDetailsRow } from "./QuotationDetailsRow";

export namespace QuotationDetailsService {
    export const baseUrl = 'Default/QuotationDetails';

    export declare function Create(request: SaveRequest<QuotationDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<QuotationDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<QuotationDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<QuotationDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<QuotationDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<QuotationDetailsRow>>;

    export const Methods = {
        Create: "Default/QuotationDetails/Create",
        Update: "Default/QuotationDetails/Update",
        Delete: "Default/QuotationDetails/Delete",
        Retrieve: "Default/QuotationDetails/Retrieve",
        List: "Default/QuotationDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>QuotationDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}