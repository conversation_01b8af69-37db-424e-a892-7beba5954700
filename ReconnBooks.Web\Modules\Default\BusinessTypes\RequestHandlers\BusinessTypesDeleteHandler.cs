﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.BusinessTypesRow;

namespace ReconnBooks.Default;

public interface IBusinessTypesDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessTypesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessTypesDeleteHandler
{
    public BusinessTypesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}