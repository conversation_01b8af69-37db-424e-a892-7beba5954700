﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface LocationsRow {
    RowNumber?: number;
    LocationId?: number;
    LocationName?: string;
    Description?: string;
    Remarks?: string;
    Discontinued?: boolean;
    ClientId?: number;
    ClientName?: string;
}

export abstract class LocationsRow {
    static readonly idProperty = 'LocationId';
    static readonly nameProperty = 'LocationName';
    static readonly localTextPrefix = 'Default.Locations';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<LocationsRow>();
}