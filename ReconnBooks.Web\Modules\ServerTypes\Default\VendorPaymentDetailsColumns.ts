﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { VendorPaymentDetailsRow } from "./VendorPaymentDetailsRow";

export interface VendorPaymentDetailsColumns {
    RowNumber: Column<VendorPaymentDetailsRow>;
    VendorPaymentPaymentVoucherNo: Column<VendorPaymentDetailsRow>;
    PurchaseOrderNo: Column<VendorPaymentDetailsRow>;
    VendorBillNo: Column<VendorPaymentDetailsRow>;
    VendorBillDetailId: Column<VendorPaymentDetailsRow>;
    AmountPaid: Column<VendorPaymentDetailsRow>;
    VendorPaymentDetailId: Column<VendorPaymentDetailsRow>;
}

export class VendorPaymentDetailsColumns extends ColumnsBase<VendorPaymentDetailsRow> {
    static readonly columnsKey = 'Default.VendorPaymentDetails';
    static readonly Fields = fieldsProxy<VendorPaymentDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types