﻿import { StringEditor, DecimalEditor, TextAreaEditor, DateEditor, LookupEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface TdsRatesForm {
    Section: StringEditor;
    TDSRate: DecimalEditor;
    Transaction: TextAreaEditor;
    Limit: StringEditor;
    Deductee: StringEditor;
    WefDate: DateEditor;
    FinancialYearId: LookupEditor;
    IsDefault: BooleanEditor;
    Remarks: TextAreaEditor;
}

export class TdsRatesForm extends PrefixedContext {
    static readonly formKey = 'Default.TdsRates';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!TdsRatesForm.init)  {
            TdsRatesForm.init = true;

            var w0 = StringEditor;
            var w1 = DecimalEditor;
            var w2 = TextAreaEditor;
            var w3 = DateEditor;
            var w4 = LookupEditor;
            var w5 = BooleanEditor;

            initFormType(TdsRatesForm, [
                'Section', w0,
                'TDSRate', w1,
                'Transaction', w2,
                'Limit', w0,
                'Deductee', w0,
                'WefDate', w3,
                'FinancialYearId', w4,
                'IsDefault', w5,
                'Remarks', w2
            ]);
        }
    }
}