﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { SalesOrderDetailsRow } from "./SalesOrderDetailsRow";

export namespace SalesOrderDetailsService {
    export const baseUrl = 'Default/SalesOrderDetails';

    export declare function Create(request: SaveRequest<SalesOrderDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SalesOrderDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SalesOrderDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SalesOrderDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SalesOrderDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesOrderDetailsRow>>;

    export const Methods = {
        Create: "Default/SalesOrderDetails/Create",
        Update: "Default/SalesOrderDetails/Update",
        Delete: "Default/SalesOrderDetails/Delete",
        Retrieve: "Default/SalesOrderDetails/Retrieve",
        List: "Default/SalesOrderDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>SalesOrderDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}