﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { SalesRegisterRow } from "./SalesRegisterRow";

export namespace SalesRegisterService {
    export const baseUrl = 'Default/SalesRegister';

    export declare function Create(request: SaveRequest<SalesRegisterRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SalesRegisterRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SalesRegisterRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SalesRegisterRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SalesRegisterRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesRegisterRow>>;

    export const Methods = {
        Create: "Default/SalesRegister/Create",
        Update: "Default/SalesRegister/Update",
        Delete: "Default/SalesRegister/Delete",
        Retrieve: "Default/SalesRegister/Retrieve",
        List: "Default/SalesRegister/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>SalesRegisterService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}