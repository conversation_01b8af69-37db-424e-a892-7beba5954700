﻿CREATE TABLE [dbo].[Customers]
(
    [CustomerId]            INT             NOT NULL    IDENTITY (1, 1),
    [CompanyName]           NVARCHAR (100)  NOT NULL,
    [AddressedToId]         INT                 NULL,
    [CompanyCode]           NVARCHAR (50)       NULL,
    
    [GSTIN]                 NVARCHAR (30)       NULL,
    [PlaceOfSupplyId]       INT                 NULL,
    [NatureOfSupplyId]      INT                 NULL,
    [SupplyTypeId]          INT                 NULL,
    
    [PAN]                   NVARCHAR (30)       NULL,
    [IECNo]                 NVARCHAR (50)       NULL,
    [CINNo]                 NVARCHAR (50)       NULL,
    [TAN]                   NVARCHAR (50)       NULL,
    [UdyamNo]               NVARCHAR (50)       NULL,

    [BillingAddress]        NVARCHAR (MAX)  NOT NULL,
    [BillingCityId]         INT             NOT NULL,
    [BillingPINCode]        NVARCHAR (10)       NULL,
    [CorrespondenceAddress] NVARCHAR (MAX)      NULL,
    [CorrespondenceCityId]  INT                 NULL,
    [CorrespondencePINCode] NVARCHAR (10)       NULL,

    [PhoneNo]               NVARCHAR (50)       NULL,
    [FaxNo]                 NVARCHAR (50)       NULL,
    [HomePage]              NVARCHAR (200)      NULL,
    [EMailId]               NVARCHAR (30)       NULL,

    [BankId]                INT                 NULL,
    [BranchName]            NVARCHAR (50)       NULL,
    [AccountName]           NVARCHAR (50)       NULL,
    [AccountNumber]         NVARCHAR (50)       NULL,
    [IFSCCode]              NVARCHAR (50)       NULL,
    [BranchCode]            NVARCHAR (50)       NULL,
    [UploadFiles]           NTEXT               NULL,
    [ClientId]              INT             NOT NULL    CONSTRAINT [DF_Customers_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_Customers] PRIMARY KEY           CLUSTERED   ([CustomerId] ASC),
    CONSTRAINT [FK_Customers_AddressedTo]           FOREIGN KEY ([AddressedToId])       REFERENCES [dbo].[AddressedTo] ([AddressedToId]),
    CONSTRAINT [FK_Customers_PlaceOfSupply]         FOREIGN KEY ([PlaceOfSupplyId])     REFERENCES [dbo].[States] ([StateId]),
    CONSTRAINT [FK_Customers_NatureOfSupply]        FOREIGN KEY ([NatureOfSupplyId])    REFERENCES [dbo].[NatureOfSupply] ([NatureOfSupplyId]),
    CONSTRAINT [FK_Customers_SupplyTypes]           FOREIGN KEY ([SupplyTypeId])        REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_Customers_BillingCities]         FOREIGN KEY ([BillingCityId])       REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Customers_CorrespondenceCities]  FOREIGN KEY ([CorrespondenceCityId])REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Customers_Banks]                 FOREIGN KEY ([BankId])              REFERENCES [dbo].[Banks] ([BankId]),
    CONSTRAINT [FK_Customers_Clients]               FOREIGN KEY ([ClientId])            REFERENCES [dbo].[Clients] ([ClientId]),
);
GO
CREATE NONCLUSTERED INDEX [BillingCityId]
    ON [dbo].[Customers]([BillingCityId] ASC);
GO
CREATE NONCLUSTERED INDEX [CompanyName]
    ON [dbo].[Customers]([CompanyName] ASC);
GO
CREATE NONCLUSTERED INDEX [BillingPINCode]
    ON [dbo].[Customers]([BillingPINCode] ASC);
GO
CREATE NONCLUSTERED INDEX [GSTIN]
    ON [dbo].[Customers]([GSTIN] ASC);

