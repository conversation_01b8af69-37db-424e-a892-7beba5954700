﻿import { RetrieveRequest, RetrieveResponse, ServiceOptions, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { DataAuditLogRow } from "./DataAuditLogRow";

export namespace DataAuditLogService {
    export const baseUrl = 'Default/DataAuditLog';

    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DataAuditLogRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DataAuditLogRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DataAuditLogRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DataAuditLogRow>>;

    export const Methods = {
        Retrieve: "Default/DataAuditLog/Retrieve",
        List: "Default/DataAuditLog/List"
    } as const;

    [
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>DataAuditLogService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}