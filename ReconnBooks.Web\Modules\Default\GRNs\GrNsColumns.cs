using Serenity.ComponentModel;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.GrNs")]
[BasedOnRow(typeof(GrNsRow), CheckNames = true)]
public class GrNsColumns
{
    [Width(50), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Grn No."), Width(150), SortOrder(1, descending: true)]
    public string GRNNo { get; set; }
    
    [EditLink, DisplayName("Date"), Width(80)]
    public DateTime GRNDate { get; set; }
    
    [EditLink, DisplayName("GrnType"), Width(120)]

    public string GRNTypeName { get; set; }

    [EditLink, DisplayName("VendorName"), Width(180)]
    public string VendorName { get; set; }

    //TODO: Add vendor GST No. column here

    [DisplayName("PO No."), Width(150)]
    public string PurchaseOrderNo { get; set; }

       
    [DisplayName("DC/Invoice No."), Width(130)]
    public string VendorDcInvoiceNo { get; set; }
    
    [DisplayName("Date"), Width(80)]
    public DateTime VendorDcInvoiceDate { get; set; }
    
    [DisplayName("Docket No."), Width(100)]
    public string ShippingDocketNo { get; set; }
    
    [DisplayName("Shipped Through"), Width(200)]
    public string ShippedThrough { get; set; }
    
    [Hidden]
    [DisplayName("Delivery Address"), Width(200)]
    public string DeliveryAddress { get; set; }
    
    [DisplayName("Delivery City"), Width(100)]
    public string DeliveryCityCityName { get; set; }
    
    [DisplayName("Vehicle No."), Width(100)]
    public string VehicleNo { get; set; }

    [Hidden]
    [DisplayName("Vehicle Type"), Width(100)]
    public string VehicleType { get; set; }


    [DisplayName("Gate Pass No."), Width(120)]
    public string GatePassNo { get; set; }
    
    [DisplayName("Date"), Width(80)]
    public DateTime GatePassDate { get; set; }
    
    [Hidden]
    [DisplayName("Inspection"), Width(100)]
    public string Inspection { get; set; }
    
    [Width(100)]
    [DisplayName("Received By"), LookupEditor(typeof(EmployeesRow), AutoComplete = true)]
    public string ReceivedByEmployeeEmployeeName { get; set; }

    [DisplayName("Financial Year"), Width(100), LookupEditor(typeof(FinancialYearsRow)), QuickFilter]
    public string FinancialYearName { get; set; }

    [Hidden]
    public string GRNMonth { get; set; }

    [Hidden]
    public string UploadDocuments { get; set; }
    
    [Hidden]
    public string Remarks { get; set; }
    
    [Hidden]
    public int ClientId { get; set; }
    [Hidden]
    public string PreparedByUserUsername { get; set; }
    
    [Hidden, DateTimeFormatter]
    public DateTime PreparedDate { get; set; }
    
    [Hidden]
    public string VerifiedByUserUsername { get; set; }
    
    [Hidden, DateTimeFormatter]
    public DateTime VerifiedDate { get; set; }
    
    [Hidden]
    public string AuthorizedByUserUsername { get; set; }
    
    [Hidden, DateTimeFormatter]
    public DateTime AuthorizedDate { get; set; }
    
    [Hidden]
    public string ModifiedByUserUsername { get; set; }
    
    [Hidden, DateTimeFormatter]
    public DateTime ModifiedDate { get; set; }
    
    [Hidden]
    public string CancelledByUserUsername { get; set; }
    
    [Hidden, DateTimeFormatter]
    public DateTime CancelledDate { get; set; }
   
    [EditLink, DisplayName("Authorized"), Width(90)]
    public bool AuthorizedStatus { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), AlignRight]
    public int GRNId { get; set; }

}
