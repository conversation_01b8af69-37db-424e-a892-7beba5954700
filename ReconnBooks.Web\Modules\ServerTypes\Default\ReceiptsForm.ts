﻿import { StringEditor, DateEditor, ServiceLookupEditor, DecimalEditor, LookupEditor, BooleanEditor, DateTimeEditor, EnumEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { ReceiptDetailsGridEditor } from "../../Default/ReceiptDetails/ReceiptDetailsGridEditor";
import { TransactionStatus } from "../Modules/Common.Helpers.TransactionStatus";

export interface ReceiptsForm {
    ReceiptNo: StringEditor;
    ReceiptDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    TotalReceivable: DecimalEditor;
    OnAccount: DecimalEditor;
    AmountReceived: DecimalEditor;
    ModeOfPaymentId: ServiceLookupEditor;
    PaymentRefNo: StringEditor;
    FinancialYearId: LookupEditor;
    ReceiptDetailsList: ReceiptDetailsGridEditor;
    Narration: StringEditor;
    ChequeDdNo: StringEditor;
    ChequeDdDate: DateEditor;
    BankBranchName: StringEditor;
    Remarks: StringEditor;
    AuthorizedStatus: BooleanEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    CancelReason: StringEditor;
    TransactionStatus: EnumEditor;
}

export class ReceiptsForm extends PrefixedContext {
    static readonly formKey = 'Default.Receipts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ReceiptsForm.init)  {
            ReceiptsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = DecimalEditor;
            var w4 = LookupEditor;
            var w5 = ReceiptDetailsGridEditor;
            var w6 = BooleanEditor;
            var w7 = DateTimeEditor;
            var w8 = EnumEditor;

            initFormType(ReceiptsForm, [
                'ReceiptNo', w0,
                'ReceiptDate', w1,
                'CustomerId', w2,
                'TotalReceivable', w3,
                'OnAccount', w3,
                'AmountReceived', w3,
                'ModeOfPaymentId', w2,
                'PaymentRefNo', w0,
                'FinancialYearId', w4,
                'ReceiptDetailsList', w5,
                'Narration', w0,
                'ChequeDdNo', w0,
                'ChequeDdDate', w1,
                'BankBranchName', w0,
                'Remarks', w0,
                'AuthorizedStatus', w6,
                'PreparedByUserId', w4,
                'PreparedDate', w7,
                'VerifiedByUserId', w4,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w4,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w4,
                'ModifiedDate', w1,
                'CancelledByUserId', w4,
                'CancelledDate', w1,
                'CancelReason', w0,
                'TransactionStatus', w8
            ]);
        }
    }
}

[TransactionStatus]; // referenced types
queueMicrotask(() => [CustomersDialog]); // referenced dialogs