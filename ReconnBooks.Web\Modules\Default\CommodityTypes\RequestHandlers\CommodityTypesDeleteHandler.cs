﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.CommodityTypesRow;

namespace ReconnBooks.Default;

public interface ICommodityTypesDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class CommodityTypesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, ICommodityTypesDeleteHandler
{
    public CommodityTypesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}