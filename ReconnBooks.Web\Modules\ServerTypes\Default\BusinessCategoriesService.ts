﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { BusinessCategoriesRow } from "./BusinessCategoriesRow";

export namespace BusinessCategoriesService {
    export const baseUrl = 'Default/BusinessCategories';

    export declare function Create(request: SaveRequest<BusinessCategoriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<BusinessCategoriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<BusinessCategoriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<BusinessCategoriesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<BusinessCategoriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<BusinessCategoriesRow>>;

    export const Methods = {
        Create: "Default/BusinessCategories/Create",
        Update: "Default/BusinessCategories/Update",
        Delete: "Default/BusinessCategories/Delete",
        Retrieve: "Default/BusinessCategories/Retrieve",
        List: "Default/BusinessCategories/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>BusinessCategoriesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}