﻿import { fieldsProxy } from "@serenity-is/corelib";
import { HsnSummaryDetailsRow } from "./HsnSummaryDetailsRow";

export interface HsnSummaryRow {
    HsnSummaryId?: number;
    FinancialYearId?: number;
    FinancialYearName?: string;
    InvoiceMonth?: string;
    InvoiceId?: number;
    InvoiceDetailId?: number;
    Description?: string;
    Remarks?: string;
    HsnCode?: string;
    HsnDescription?: string;
    Quantity?: number;
    UQC?: string;
    TaxableValue?: number;
    Cgst?: number;
    Sgst?: number;
    Igst?: number;
    Cess?: number;
    NetAmount?: number;
    Invoices?: HsnSummaryDetailsRow[];
}

export abstract class HsnSummaryRow {
    static readonly idProperty = 'HsnCode';
    static readonly nameProperty = 'Description';
    static readonly localTextPrefix = 'Default.HsnSummary';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<HsnSummaryRow>();
}