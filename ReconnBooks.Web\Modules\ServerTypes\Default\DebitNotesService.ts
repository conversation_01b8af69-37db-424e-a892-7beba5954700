﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { DebitNoteDetailsRow } from "./DebitNoteDetailsRow";
import { DebitNotesRow } from "./DebitNotesRow";

export namespace DebitNotesService {
    export const baseUrl = 'Default/DebitNotes';

    export declare function Create(request: SaveRequest<DebitNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DebitNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DebitNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DebitNotesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DebitNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DebitNotesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromPurchaseOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<DebitNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DebitNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/DebitNotes/Create",
        Update: "Default/DebitNotes/Update",
        Delete: "Default/DebitNotes/Delete",
        Retrieve: "Default/DebitNotes/Retrieve",
        List: "Default/DebitNotes/List",
        GetNextNumber: "Default/DebitNotes/GetNextNumber",
        GetFromPurchaseOrderDetails: "Default/DebitNotes/GetFromPurchaseOrderDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromPurchaseOrderDetails'
    ].forEach(x => {
        (<any>DebitNotesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}