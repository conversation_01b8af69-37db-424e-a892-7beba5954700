﻿using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Administration.RoleRow>;
using MyRow = ReconnBooks.Administration.RoleRow;


namespace ReconnBooks.Administration;
public interface IRoleListHandler : IList<PERSON><PERSON><PERSON><MyRow, MyRequest, MyResponse> { }

public class RoleListHandler(IRequestContext context) :
    ListRequestHandler<MyRow, MyRequest, MyResponse>(context), IRoleListHandler
{
}