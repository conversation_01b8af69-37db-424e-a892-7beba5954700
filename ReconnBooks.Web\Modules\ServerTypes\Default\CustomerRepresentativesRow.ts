﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface CustomerRepresentativesRow {
    RepresentativeId?: number;
    CustomerId?: number;
    EmployeeId?: number;
}

export abstract class CustomerRepresentativesRow {
    static readonly idProperty = 'RepresentativeId';
    static readonly localTextPrefix = 'Default.CustomerRepresentatives';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CustomerRepresentativesRow>();
}