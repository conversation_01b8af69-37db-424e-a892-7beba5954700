﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { PurchaseOrdersRow } from "./PurchaseOrdersRow";

export interface PurchaseOrdersColumns {
    RowNumber: Column<PurchaseOrdersRow>;
    PurchaseOrderNo: Column<PurchaseOrdersRow>;
    PurchaseOrderDate: Column<PurchaseOrdersRow>;
    SupplyTypeId: Column<PurchaseOrdersRow>;
    VendorName: Column<PurchaseOrdersRow>;
    GSTIN: Column<PurchaseOrdersRow>;
    PlaceOfSupplyStateName: Column<PurchaseOrdersRow>;
    NetTaxableAmount: Column<PurchaseOrdersRow>;
    NetCGSTAmount: Column<PurchaseOrdersRow>;
    NetSGSTAmount: Column<PurchaseOrdersRow>;
    NetIGSTAmount: Column<PurchaseOrdersRow>;
    ReferenceNo: Column<PurchaseOrdersRow>;
    ReferenceDate: Column<PurchaseOrdersRow>;
    GrandTotal: Column<PurchaseOrdersRow>;
    DeliveryDueDate: Column<PurchaseOrdersRow>;
    Taxes: Column<PurchaseOrdersRow>;
    PaymentTerms: Column<PurchaseOrdersRow>;
    PaymentDueDate: Column<PurchaseOrdersRow>;
    Inspection: Column<PurchaseOrdersRow>;
    FootNote: Column<PurchaseOrdersRow>;
    UploadDocuments: Column<PurchaseOrdersRow>;
    Remarks: Column<PurchaseOrdersRow>;
    FinancialYearName: Column<PurchaseOrdersRow>;
    PurchaseOrderMonth: Column<PurchaseOrdersRow>;
    PreparedByUserUsername: Column<PurchaseOrdersRow>;
    PreparedDate: Column<PurchaseOrdersRow>;
    VerifiedByUserUsername: Column<PurchaseOrdersRow>;
    VerifiedDate: Column<PurchaseOrdersRow>;
    AuthorizedByUserUsername: Column<PurchaseOrdersRow>;
    AuthorizedDate: Column<PurchaseOrdersRow>;
    ModifiedByUserUsername: Column<PurchaseOrdersRow>;
    ModifiedDate: Column<PurchaseOrdersRow>;
    CancelledByUserUsername: Column<PurchaseOrdersRow>;
    CancelledDate: Column<PurchaseOrdersRow>;
    POStatus: Column<PurchaseOrdersRow>;
    AuthorizedStatus: Column<PurchaseOrdersRow>;
    PurchaseOrderId: Column<PurchaseOrdersRow>;
}

export class PurchaseOrdersColumns extends ColumnsBase<PurchaseOrdersRow> {
    static readonly columnsKey = 'Default.PurchaseOrders';
    static readonly Fields = fieldsProxy<PurchaseOrdersColumns>();
}

[IndianNumberFormatter]; // referenced types