using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Default;
using Serenity.Extensions.Entities;

namespace ReconnBooks.Administration;
[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Administration"), TableName("Users")]
[Display<PERSON>ame("Users"), InstanceName("User"), GenerateFields]
[ReadPermission(PermissionKeys.Security)]
[ModifyPermission(PermissionKeys.Security)]
[LookupScript(Permission = PermissionKeys.Security)]
public sealed partial class UserRow : LoggingRow<UserRow.RowFields>, IIdRow, INameRow, IIsActiveRow, IDisplayNameRow, IEmailRow, IPasswordRow, ITwoFactorRow, IRowNumberedRow
{
    const string jUserType = nameof(jUserType);
    const string jConsultant = nameof(jConsultant);
    const string jClient = nameof(jClient);
    const string jEmployee = nameof(jEmployee);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("User Id"), Identity, IdProperty]
    public int? UserId { get => fields.UserId[this]; set => fields.UserId[this] = value; }

    [DisplayName("Username"), Size(100), NotNull, QuickSearch, LookupInclude, NameProperty]
    public string Username { get => fields.Username[this]; set => fields.Username[this] = value; }

    [DisplayName("Source"), Size(4), NotNull, Insertable(false), Updatable(false), DefaultValue("site")]
    public string Source { get => fields.Source[this]; set => fields.Source[this] = value; }

    [DisplayName("Password Hash"), Size(86), NotNull, Insertable(false), Updatable(false), MinSelectLevel(SelectLevel.Never), ScriptSkip]
    public string PasswordHash { get => fields.PasswordHash[this]; set => fields.PasswordHash[this] = value; }

    [DisplayName("Password Salt"), Size(10), NotNull, Insertable(false), Updatable(false), MinSelectLevel(SelectLevel.Never), ScriptSkip]
    public string PasswordSalt { get => fields.PasswordSalt[this]; set => fields.PasswordSalt[this] = value; }

    [DisplayName("Display Name"), Size(100), NotNull, LookupInclude]
    public string DisplayName { get => fields.DisplayName[this]; set => fields.DisplayName[this] = value; }

    [DisplayName("Email"), Size(100)]
    public string Email { get => fields.Email[this]; set => fields.Email[this] = value; }

    [DisplayName("Mobile No."), Size(20)]
    public string MobilePhoneNumber { get => fields.MobilePhoneNumber[this]; set => fields.MobilePhoneNumber[this] = value; }

    [DisplayName("2FA Data"), EmitFieldType(typeof(JsonField<>)), MinSelectLevel(SelectLevel.Never), Insertable(false), Updatable(false), ScriptSkip]
    public TwoFactorUserData TwoFactorData { get => fields.TwoFactorData[this]; set => fields.TwoFactorData[this] = value; }

    [DisplayName("User Image"), Size(100)]
    [ImageUploadEditor(FilenameFormat = "UserImage/~", CopyToHistory = true)]
    public string UserImage { get => fields.UserImage[this]; set => fields.UserImage[this] = value; }

    [DisplayName("Password"), Size(50), NotMapped]
    public string Password { get => fields.Password[this]; set => fields.Password[this] = value; }

    [DisplayName("Activated"), NotNull, Insertable(false), Updatable(true)]
    public short? IsActive { get => fields.IsActive[this]; set => fields.IsActive[this] = value; }

    [DisplayName("Confirm Password"), Size(50), NotMapped]
    public string PasswordConfirm { get => fields.PasswordConfirm[this]; set => fields.PasswordConfirm[this] = value; }

    [DisplayName("Last Directory Update"), Insertable(false), Updatable(false)]
    public DateTime? LastDirectoryUpdate { get => fields.LastDirectoryUpdate[this]; set => fields.LastDirectoryUpdate[this] = value; }

    [NotMapped, MinSelectLevel(SelectLevel.Explicit), ReadPermission("ImpersonateAs")]
    public string ImpersonationToken { get => fields.ImpersonationToken[this]; set => fields.ImpersonationToken[this] = value; }

    [DisplayName("Roles"), LinkingSetRelation(typeof(UserRoleRow), nameof(UserRoleRow.UserId), nameof(UserRoleRow.RoleId))]
    [AsyncLookupEditor(typeof(RoleRow), Multiple = true)]
    public List<int> Roles { get => fields.Roles[this]; set => fields.Roles[this] = value; }

    [DisplayName("Employee Name"), NotNull, ForeignKey(typeof(EmployeesRow), "EmployeeId"), LeftJoin(nameof(jEmployee)), LookupInclude]
    [LookupEditor(typeof(EmployeesRow), InplaceAdd = true)]
    public Int32? EmployeeId
    {
        get { return Fields.EmployeeId[this]; }
        set { Fields.EmployeeId[this] = value; }
    }

    [Expression($"{jEmployee}.[ClientId]")]
    [ForeignKey(typeof(ClientsRow), "ClientId"), LeftJoin(nameof(jClient)), LookupInclude]
    [ServiceLookupEditor(typeof(ClientsRow), InplaceAdd = (true), Service = "Default/Clients/List")]
    [ReadPermission(PermissionKeys.Security)]
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [Expression($"{jClient}.[ClientName]"), DisplayName("Client Name"), QuickSearch, LookupInclude]
    public string ClientName
    {
        get { return Fields.ClientName[this]; }
        set { Fields.ClientName[this] = value; }
    }

    [Expression($"{jClient}.[ClientCode]"), DisplayName("Client Code"), LookupInclude]
    public string ClientCode
    {
        get { return Fields.ClientCode[this]; }
        set { Fields.ClientCode[this] = value; }
    }

    [Expression($"{jClient}.[Logo]"), DisplayName("Client Logo"), LookupInclude]
    public string ClientLogo
    {
        get { return Fields.ClientLogo[this]; }
        set { Fields.ClientLogo[this] = value; }
    }

    [Expression($"{jClient}.[PlaceOfSupplyId]"), DisplayName("Place of Supply"), LookupInclude]
    public int? PlaceOfSupplyId { get => fields.PlaceOfSupplyId[this]; set => fields.PlaceOfSupplyId[this] = value; }

    [DisplayName("Consultant"), Expression($"{jEmployee}.[ConsultantId]")]
    [ForeignKey(typeof(ConsultantsRow), "ConsultantId"), LeftJoin(nameof(jConsultant)), LookupInclude]
    [ServiceLookupEditor(typeof(ConsultantsRow), InplaceAdd = (true), Service = "Default/Consultants/List")]
    //[LookupEditor(typeof(ConsultantsRow), InplaceAdd = true)]
    [ReadPermission(PermissionKeys.Security)]
    public Int32? ConsultantId
    {
        get { return Fields.ConsultantId[this]; }
        set { Fields.ConsultantId[this] = value; }
    }

    [DisplayName("Consultant"), Expression($"{jConsultant}.[ConsultantName]"), QuickSearch, LookupInclude]
    public String ConsultantName
    {
        get { return Fields.ConsultantName[this]; }
        set { Fields.ConsultantName[this] = value; }
    }

    [Expression($"{jConsultant}.[Logo]"), DisplayName("Consultant Logo"), LookupInclude]
    public string ConsultantLogo
    {
        get { return Fields.ConsultantLogo[this]; }
        set { Fields.ConsultantLogo[this] = value; }
    }

    [DisplayName("User Type"), Expression($"{jEmployee}.[UserTypeId]")]
    [ForeignKey(typeof(UserTypesRow), "UserTypeId"), LeftJoin(nameof(jUserType)), TextualField("UserTypeName"), LookupInclude,]
    [LookupEditor(typeof(UserTypesRow), InplaceAdd = true)]
    public Int32? UserTypeId
    {
        get { return Fields.UserTypeId[this]; }
        set { Fields.UserTypeId[this] = value; }
    }

    [DisplayName("User Type Name"), Expression($"{jUserType}.[UserTypeName]")]
    public String UserTypeName
    {
        get { return Fields.UserTypeName[this]; }
        set { Fields.UserTypeName[this] = value; }
    }

    [DisplayName("User Type Description"), Expression($"{jUserType}.[Description]")]
    public String UserTypeDescription
    {
        get { return Fields.UserTypeDescription[this]; }
        set { Fields.UserTypeDescription[this] = value; }
    }

    [DisplayName("Client Assignment")]
    [LookupEditor(typeof(ClientsRow), Multiple = true), NotMapped]
    [LinkingSetRelation(typeof(ClientUsersRow), nameof(ClientUsersRow.UserId), nameof(ClientUsersRow.ClientId))]
    [ReadPermission(PermissionKeys.Consultants.User)]
    public List<Int32> ClientList
    {
        get { return Fields.ClientList[this]; }
        set { Fields.ClientList[this] = value; }
    }

    StringField IDisplayNameRow.DisplayNameField => fields.DisplayName;
    StringField IEmailRow.EmailField => fields.Email;
    Int16Field IIsActiveRow.IsActiveField => fields.IsActive;
    StringField IPasswordRow.PasswordHashField => fields.PasswordHash;
    StringField IPasswordRow.PasswordSaltField => fields.PasswordSalt;
    GenericClassField<TwoFactorUserData> ITwoFactorRow.TwoFactorDataField => fields.TwoFactorData;

    public partial class RowFields : LoggingRowFields
    {
    }
}