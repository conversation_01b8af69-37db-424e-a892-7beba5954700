﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { PurchaseOrdersRow } from "./PurchaseOrdersRow";

export namespace PurchaseOrdersService {
    export const baseUrl = 'Default/PurchaseOrders';

    export declare function Create(request: SaveRequest<PurchaseOrdersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PurchaseOrdersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PurchaseOrdersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PurchaseOrdersRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PurchaseOrdersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PurchaseOrdersRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function EmailPurchaseOrder(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/PurchaseOrders/Create",
        Update: "Default/PurchaseOrders/Update",
        Delete: "Default/PurchaseOrders/Delete",
        Retrieve: "Default/PurchaseOrders/Retrieve",
        List: "Default/PurchaseOrders/List",
        GetNextNumber: "Default/PurchaseOrders/GetNextNumber",
        EmailPurchaseOrder: "Default/PurchaseOrders/EmailPurchaseOrder"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'EmailPurchaseOrder'
    ].forEach(x => {
        (<any>PurchaseOrdersService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}