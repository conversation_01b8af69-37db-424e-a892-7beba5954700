﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CustomerContactsRow } from "./CustomerContactsRow";

export interface CustomerContactsColumns {
    RowNumber: Column<CustomerContactsRow>;
    CustomerCompanyName: Column<CustomerContactsRow>;
    TitleOfRespect: Column<CustomerContactsRow>;
    ContactName: Column<CustomerContactsRow>;
    Designation: Column<CustomerContactsRow>;
    DepartmentName: Column<CustomerContactsRow>;
    MobileNo: Column<CustomerContactsRow>;
    EMail: Column<CustomerContactsRow>;
    AlternateNo: Column<CustomerContactsRow>;
    OfficePhoneNo: Column<CustomerContactsRow>;
    ExtensionNo: Column<CustomerContactsRow>;
    Status: Column<CustomerContactsRow>;
    CustomerContactId: Column<CustomerContactsRow>;
}

export class CustomerContactsColumns extends ColumnsBase<CustomerContactsRow> {
    static readonly columnsKey = 'Default.CustomerContacts';
    static readonly Fields = fieldsProxy<CustomerContactsColumns>();
}