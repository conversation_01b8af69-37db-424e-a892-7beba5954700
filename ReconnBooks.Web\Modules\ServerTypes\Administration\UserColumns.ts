﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ClientUsersFormatter } from "../../Default/ClientUsers/ClientUsersFormatter";
import { UserRow } from "./UserRow";

export interface UserColumns {
    RowNumber: Column<UserRow>;
    UserTypeId: Column<UserRow>;
    UserTypeName: Column<UserRow>;
    Username: Column<UserRow>;
    ClientName: Column<UserRow>;
    ConsultantName: Column<UserRow>;
    ImpersonationToken: Column<UserRow>;
    DisplayName: Column<UserRow>;
    Source: Column<UserRow>;
    ClientList: Column<UserRow>;
    Roles: Column<UserRow>;
    UserId: Column<UserRow>;
}

export class UserColumns extends ColumnsBase<UserRow> {
    static readonly columnsKey = 'Administration.User';
    static readonly Fields = fieldsProxy<UserColumns>();
}

[ClientUsersFormatter]; // referenced types