using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.BusinessGroups")]
[BasedOnRow(typeof(BusinessGroupsRow), CheckNames = true)]
public class BusinessGroupsColumns
{
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int BusinessGroupId { get; set; }
    
    [EditLink, Width(200)]
    public string BusinessGroup { get; set; }

    [EditLink, Width(300)]
    public string Description { get; set; }
}