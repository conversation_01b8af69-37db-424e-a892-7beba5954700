﻿CREATE TABLE [dbo].[ProformaInvoiceDetails] (
    [ProformaInvoiceDetailId] INT             IDENTITY (1, 1) NOT NULL,
    [ProformaInvoiceId]       INT             NOT NULL,
    [CommodityTypeId]         INT             NOT NULL,
    [CommodityId]             BIGINT          NOT NULL,
    [CommodityDescription]    NVARCHAR (MAX)  NULL,
    [Quantity]                DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_Quantity] DEFAULT ((1)) NOT NULL,
    [UnitId]                  INT             NOT NULL,
    [UnitPrice]               DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_UnitPrice] DEFAULT ((0)) NOT NULL,
    [DiscountPercent]         DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_DiscountPercent] DEFAULT ((0)) NULL,
    [DiscountAmountPerUnit]   DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_DiscountAmtPerUnit] DEFAULT ((0)) NULL,
    [NetDiscountAmount]       DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_NetDiscountAmt] DEFAULT ((0)) NULL,
    [GSTRateId]               INT             NULL,
    [IGSTRate]                DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_IGSTRate] DEFAULT ((0)) NULL,
    [CGSTRate]                DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_CGSTRate] DEFAULT ((0)) NULL,
    [SGSTRate]                DECIMAL (18, 2) CONSTRAINT [DF_ProformaInvoiceDetails_SGSTRate] DEFAULT ((0)) NULL,
    [DummyField]              NVARCHAR (200)  NULL,
    [NetPricePerUnit]         DECIMAL (18, 2) NULL,
    [NetAmount]               DECIMAL (18, 2) NULL,
    CONSTRAINT [PK_ProformaInvoiceDetails] PRIMARY KEY CLUSTERED ([ProformaInvoiceDetailId] ASC),
    CONSTRAINT [FK_ProformaInvoiceDetails_ProformaInvoices] FOREIGN KEY ([ProformaInvoiceId]) REFERENCES [dbo].[ProformaInvoices] ([ProformaInvoiceId]),
    CONSTRAINT [FK_ProformaInvoiceDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_ProformaInvoiceDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_ProformaInvoiceDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_ProformaInvoiceDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [ProformaInvoices]
    ON [dbo].[ProformaInvoiceDetails]([ProformaInvoiceId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[ProformaInvoiceDetails]([CommodityId] ASC);

