﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { FeedbacksRow } from "./FeedbacksRow";

export interface FeedbacksColumns {
    RowNumber: Column<FeedbacksRow>;
    FeedbackId: Column<FeedbacksRow>;
    Feedback: Column<FeedbacksRow>;
    Description: Column<FeedbacksRow>;
    ClientName: Column<FeedbacksRow>;
}

export class FeedbacksColumns extends ColumnsBase<FeedbacksRow> {
    static readonly columnsKey = 'Default.Feedbacks';
    static readonly Fields = fieldsProxy<FeedbacksColumns>();
}