﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GstRatesRow } from "./GstRatesRow";

export namespace GstRatesService {
    export const baseUrl = 'Default/GstRates';

    export declare function Create(request: SaveRequest<GstRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GstRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GstRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GstRatesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GstRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GstRatesRow>>;

    export const Methods = {
        Create: "Default/GstRates/Create",
        Update: "Default/GstRates/Update",
        Delete: "Default/GstRates/Delete",
        Retrieve: "Default/GstRates/Retrieve",
        List: "Default/GstRates/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>GstRatesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}