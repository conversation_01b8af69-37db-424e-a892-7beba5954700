using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("PaymentTerms")]
[DisplayName("Payment Terms"), InstanceName("Payment Terms"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class PaymentTermsRow : Row<PaymentTermsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    
    public Int64Field RowNumberField { get => fields.RowNumber; }

    
    [DisplayName("Payment Terms Id"), Identity, IdProperty]
    public int? PaymentTermsId { get => fields.PaymentTermsId[this]; set => fields.PaymentTermsId[this] = value; }

    
    [DisplayName("Payment Terms"), Size(150), NotNull, QuickSearch, NameProperty]
    public string PaymentTerms { get => fields.PaymentTerms[this]; set => fields.PaymentTerms[this] = value; }
}