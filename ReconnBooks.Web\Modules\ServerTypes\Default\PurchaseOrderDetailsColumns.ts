﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { PurchaseOrderDetailsRow } from "./PurchaseOrderDetailsRow";

export interface PurchaseOrderDetailsColumns {
    RowNumber: Column<PurchaseOrderDetailsRow>;
    CommodityName: Column<PurchaseOrderDetailsRow>;
    CommodityCode: Column<PurchaseOrderDetailsRow>;
    CommodityType: Column<PurchaseOrderDetailsRow>;
    Quantity: Column<PurchaseOrderDetailsRow>;
    UnitName: Column<PurchaseOrderDetailsRow>;
    UnitPrice: Column<PurchaseOrderDetailsRow>;
    UnitAmount: Column<PurchaseOrderDetailsRow>;
    DiscountPercent: Column<PurchaseOrderDetailsRow>;
    DiscountAmountPerUnit: Column<PurchaseOrderDetailsRow>;
    NetDiscountAmount: Column<PurchaseOrderDetailsRow>;
    AmendedQuantity: Column<PurchaseOrderDetailsRow>;
    AmendedUnitId: Column<PurchaseOrderDetailsRow>;
    AmendedPrice: Column<PurchaseOrderDetailsRow>;
    PendingQuantity: Column<PurchaseOrderDetailsRow>;
    TaxableAmountPerUnit: Column<PurchaseOrderDetailsRow>;
    NetTaxableAmount: Column<PurchaseOrderDetailsRow>;
    GSTRateRemarks: Column<PurchaseOrderDetailsRow>;
    IGSTRate: Column<PurchaseOrderDetailsRow>;
    IGSTAmountPerUnit: Column<PurchaseOrderDetailsRow>;
    NetIGSTAmount: Column<PurchaseOrderDetailsRow>;
    CGSTRate: Column<PurchaseOrderDetailsRow>;
    CGSTAmountPerUnit: Column<PurchaseOrderDetailsRow>;
    NetCGSTAmount: Column<PurchaseOrderDetailsRow>;
    SGSTRate: Column<PurchaseOrderDetailsRow>;
    SGSTAmountPerUnit: Column<PurchaseOrderDetailsRow>;
    NetSGSTAmount: Column<PurchaseOrderDetailsRow>;
    NetAmount: Column<PurchaseOrderDetailsRow>;
    NetPricePerUnit: Column<PurchaseOrderDetailsRow>;
    CommodityDescription: Column<PurchaseOrderDetailsRow>;
    PurchaseOrderDetailId: Column<PurchaseOrderDetailsRow>;
    PurchaseOrderNo: Column<PurchaseOrderDetailsRow>;
}

export class PurchaseOrderDetailsColumns extends ColumnsBase<PurchaseOrderDetailsRow> {
    static readonly columnsKey = 'Default.PurchaseOrderDetails';
    static readonly Fields = fieldsProxy<PurchaseOrderDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types