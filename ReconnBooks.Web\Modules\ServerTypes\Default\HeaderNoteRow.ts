﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface HeaderNoteRow {
    RowNumber?: number;
    HeaderNoteId?: number;
    HeaderNote?: string;
    Remarks?: string;
    ClientId?: number;
}

export abstract class HeaderNoteRow {
    static readonly idProperty = 'HeaderNoteId';
    static readonly nameProperty = 'HeaderNote';
    static readonly localTextPrefix = 'Default.HeaderNote';
    static readonly lookupKey = 'Default.HeaderNote';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<HeaderNoteRow>('Default.HeaderNote') }
    static async getLookupAsync() { return getLookupAsync<HeaderNoteRow>('Default.HeaderNote') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<HeaderNoteRow>();
}