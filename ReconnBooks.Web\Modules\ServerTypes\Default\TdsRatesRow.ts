﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface TdsRatesRow {
    RowNumber?: number;
    TDSRateId?: number;
    Section?: string;
    TDSRate?: number;
    TDSRateWithSection?: string;
    Transaction?: string;
    Limit?: string;
    Deductee?: string;
    WefDate?: string;
    FinancialYearId?: number;
    IsDefault?: boolean;
    Remarks?: string;
    FinancialYearName?: string;
}

export abstract class TdsRatesRow {
    static readonly idProperty = 'TDSRateId';
    static readonly nameProperty = 'Section';
    static readonly localTextPrefix = 'Default.TdsRates';
    static readonly lookupKey = 'Default.TdsRates';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<TdsRatesRow>('Default.TdsRates') }
    static async getLookupAsync() { return getLookupAsync<TdsRatesRow>('Default.TdsRates') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<TdsRatesRow>();
}