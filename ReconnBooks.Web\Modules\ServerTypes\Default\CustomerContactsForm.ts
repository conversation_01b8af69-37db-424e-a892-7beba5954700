﻿import { ServiceLookupEditor, StringEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { DepartmentsDialog } from "../../Default/Departments/DepartmentsDialog";
import { DesignationsDialog } from "../../Default/Designations/DesignationsDialog";
import { TitlesDialog } from "../../Default/Titles/TitlesDialog";

export interface CustomerContactsForm {
    TitleId: ServiceLookupEditor;
    ContactName: StringEditor;
    DesignationId: ServiceLookupEditor;
    DepartmentId: ServiceLookupEditor;
    EMail: StringEditor;
    OfficePhoneNo: StringEditor;
    ExtensionNo: StringEditor;
    MobileNo: StringEditor;
    AlternateNo: StringEditor;
    Status: BooleanEditor;
}

export class CustomerContactsForm extends PrefixedContext {
    static readonly formKey = 'Default.CustomerContacts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CustomerContactsForm.init)  {
            CustomerContactsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = StringEditor;
            var w2 = BooleanEditor;

            initFormType(CustomerContactsForm, [
                'TitleId', w0,
                'ContactName', w1,
                'DesignationId', w0,
                'DepartmentId', w0,
                'EMail', w1,
                'OfficePhoneNo', w1,
                'ExtensionNo', w1,
                'MobileNo', w1,
                'AlternateNo', w1,
                'Status', w2
            ]);
        }
    }
}

queueMicrotask(() => [TitlesDialog, DesignationsDialog, DepartmentsDialog]); // referenced dialogs