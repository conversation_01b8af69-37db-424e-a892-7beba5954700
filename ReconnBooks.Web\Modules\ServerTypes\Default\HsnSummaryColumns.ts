﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { HsnSummaryRow } from "./HsnSummaryRow";

export interface HsnSummaryColumns {
    HsnSummaryId: Column<HsnSummaryRow>;
    FinancialYearName: Column<HsnSummaryRow>;
    InvoiceMonth: Column<HsnSummaryRow>;
    HsnCode: Column<HsnSummaryRow>;
    HsnDescription: Column<HsnSummaryRow>;
    Quantity: Column<HsnSummaryRow>;
    UQC: Column<HsnSummaryRow>;
    TaxableValue: Column<HsnSummaryRow>;
    Cgst: Column<HsnSummaryRow>;
    Sgst: Column<HsnSummaryRow>;
    Igst: Column<HsnSummaryRow>;
    Cess: Column<HsnSummaryRow>;
    NetAmount: Column<HsnSummaryRow>;
}

export class HsnSummaryColumns extends ColumnsBase<HsnSummaryRow> {
    static readonly columnsKey = 'Default.HsnSummary';
    static readonly Fields = fieldsProxy<HsnSummaryColumns>();
}

[IndianNumberFormatter]; // referenced types