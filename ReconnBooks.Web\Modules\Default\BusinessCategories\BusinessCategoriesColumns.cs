using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.BusinessCategories")]
[BasedOnRow(typeof(BusinessCategoriesRow), CheckNames = true)]
public class BusinessCategoriesColumns
{
    public long RowNumber { get; set; }
    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int BusinessCategoryId { get; set; }
    [EditLink, Width(200)]
    public string BusinessCategory { get; set; }
    
    [EditLink, Width(300)]
    public string Description { get; set; }
}