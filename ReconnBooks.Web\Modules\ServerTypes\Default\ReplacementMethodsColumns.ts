﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ReplacementMethodsRow } from "./ReplacementMethodsRow";

export interface ReplacementMethodsColumns {
    RowNumber: Column<ReplacementMethodsRow>;
    ReplacementMethodId: Column<ReplacementMethodsRow>;
    ReplacementMethod: Column<ReplacementMethodsRow>;
    Description: Column<ReplacementMethodsRow>;
    ClientName: Column<ReplacementMethodsRow>;
}

export class ReplacementMethodsColumns extends ColumnsBase<ReplacementMethodsRow> {
    static readonly columnsKey = 'Default.ReplacementMethods';
    static readonly Fields = fieldsProxy<ReplacementMethodsColumns>();
}