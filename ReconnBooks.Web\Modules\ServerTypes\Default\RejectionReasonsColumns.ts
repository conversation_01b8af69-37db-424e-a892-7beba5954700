﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { RejectionReasonsRow } from "./RejectionReasonsRow";

export interface RejectionReasonsColumns {
    RowNumber: Column<RejectionReasonsRow>;
    RejectionReasonId: Column<RejectionReasonsRow>;
    RejectionReason: Column<RejectionReasonsRow>;
    Description: Column<RejectionReasonsRow>;
    ClientName: Column<RejectionReasonsRow>;
}

export class RejectionReasonsColumns extends ColumnsBase<RejectionReasonsRow> {
    static readonly columnsKey = 'Default.RejectionReasons';
    static readonly Fields = fieldsProxy<RejectionReasonsColumns>();
}