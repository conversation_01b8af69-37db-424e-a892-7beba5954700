using Serenity.ComponentModel;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.SalesReturns")]
[BasedOnRow(typeof(SalesReturnsRow), CheckNames = true)]
public class SalesReturnsColumns
{
    [Width(50), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Sales Return No."), SortOrder(1, descending: true)]
    public string SalesReturnNo { get; set; }

    [DisplayName("Sales Return Date")]
    public DateTime SalesReturnDate { get; set; }

    [DisplayName("Customer Name")]
    public string CustomerCompanyName { get; set; }

    [DisplayName("Delivery Note No.")]
    public string DeliveryNoteNo { get; set; }

    [DisplayName("Invoice No.")]
    public string InvoiceNo { get; set; }

    [Hidden, Width(100), LookupEditor(typeof(FinancialYearsRow)), QuickFilter]
    public string FinancialYearName { get; set; }

    [Hidden]
    public string SalesReturnMonth { get; set; }

    [Hidden]
    public string UploadFiles { get; set; }

    [DisplayName("Remarks")]
    public string Remarks { get; set; }

    [Hidden]
    public int ClientId { get; set; }

    [Hidden]
    public string PreparedByUserUsername { get; set; }

    [Hidden, DateTimeFormatter]
    public DateTime PreparedDate { get; set; }

    [Hidden]
    public string VerifiedByUserUsername { get; set; }

    [Hidden, DateTimeFormatter]
    public DateTime VerifiedDate { get; set; }

    [Hidden]
    public string AuthorizedByUserUsername { get; set; }

    [Hidden, DateTimeFormatter]
    public DateTime AuthorizedDate { get; set; }

    [Hidden]
    public string ModifiedByUserUsername { get; set; }

    [Hidden, DateTimeFormatter]
    public DateTime ModifiedDate { get; set; }

    [Hidden]
    public string CancelledByUserUsername { get; set; }

    [Hidden, DateTimeFormatter]
    public DateTime CancelledDate { get; set; }

    [Width (105)]
    public bool AuthorizedStatus { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: true), AlignRight]
    public int SalesReturnId { get; set; }
}