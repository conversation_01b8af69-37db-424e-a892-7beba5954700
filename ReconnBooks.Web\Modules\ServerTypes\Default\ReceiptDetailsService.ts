﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ReceiptDetailsRequest } from "./ReceiptDetailsRequest";
import { ReceiptDetailsRow } from "./ReceiptDetailsRow";
import { ReceiptsRow } from "./ReceiptsRow";

export namespace ReceiptDetailsService {
    export const baseUrl = 'Default/ReceiptDetails';

    export declare function Create(request: SaveRequest<ReceiptDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ReceiptDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ReceiptDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReceiptDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ReceiptDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ReceiptDetailsRow>>;
    export declare function GetAllInvoiceForReceiptList(request: ReceiptDetailsRequest, onSuccess?: (response: RetrieveResponse<ReceiptsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReceiptsRow>>;
    export declare function GetReceiptDetailWithTDS(request: ReceiptDetailsRequest, onSuccess?: (response: RetrieveResponse<ReceiptDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReceiptDetailsRow>>;

    export const Methods = {
        Create: "Default/ReceiptDetails/Create",
        Update: "Default/ReceiptDetails/Update",
        Delete: "Default/ReceiptDetails/Delete",
        Retrieve: "Default/ReceiptDetails/Retrieve",
        List: "Default/ReceiptDetails/List",
        GetAllInvoiceForReceiptList: "Default/ReceiptDetails/GetAllInvoiceForReceiptList",
        GetReceiptDetailWithTDS: "Default/ReceiptDetails/GetReceiptDetailWithTDS"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetAllInvoiceForReceiptList', 
        'GetReceiptDetailWithTDS'
    ].forEach(x => {
        (<any>ReceiptDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}