﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ClientsRow } from "./ClientsRow";

export namespace ClientsService {
    export const baseUrl = 'Default/Clients';

    export declare function Create(request: SaveRequest<ClientsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ClientsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ClientsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ClientsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ClientsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ClientsRow>>;

    export const Methods = {
        Create: "Default/Clients/Create",
        Update: "Default/Clients/Update",
        Delete: "Default/Clients/Delete",
        Retrieve: "Default/Clients/Retrieve",
        List: "Default/Clients/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ClientsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}