import { DepartmentsColumns, DepartmentsRow, DepartmentsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { DepartmentsDialog } from './DepartmentsDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.DepartmentsGrid')
export class DepartmentsGrid<P = {}> extends EntityGridDialog<DepartmentsRow, P> {
    protected getColumnsKey() { return DepartmentsColumns.columnsKey; }
    protected getDialogType() { return DepartmentsDialog; }
    protected getRowDefinition() { return DepartmentsRow; }
    protected getService() { return DepartmentsService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}