﻿import { ServiceOptions, serviceRequest } from "@serenity-is/corelib";
import { BulkReportServiceRequest } from "./Common.Reporting.BulkReportServiceRequest";
import { FileContentResponse } from "./Common.Reporting.FileContentResponse";

export namespace BulkReportsService {
    export const baseUrl = 'Default/BulkReports';

    export declare function Download(request: BulkReportServiceRequest, onSuccess?: (response: FileContentResponse) => void, opt?: ServiceOptions<any>): PromiseLike<FileContentResponse>;

    export const Methods = {
        Download: "Default/BulkReports/Download"
    } as const;

    [
        'Download'
    ].forEach(x => {
        (<any>BulkReportsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}