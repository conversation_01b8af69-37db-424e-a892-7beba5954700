﻿import { StringEditor, ServiceLookupEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface HsnsacCodesForm {
    HSNSACCode: StringEditor;
    GSTRateId: ServiceLookupEditor;
    HSNSACDescription: TextAreaEditor;
    HSNSACGroup: TextAreaEditor;
}

export class HsnsacCodesForm extends PrefixedContext {
    static readonly formKey = 'Default.HsnsacCodes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!HsnsacCodesForm.init)  {
            HsnsacCodesForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = TextAreaEditor;

            initFormType(HsnsacCodesForm, [
                'HSNSACCode', w0,
                'GSTRateId', w1,
                'HSNSACDescription', w2,
                'HSNSACGroup', w2
            ]);
        }
    }
}