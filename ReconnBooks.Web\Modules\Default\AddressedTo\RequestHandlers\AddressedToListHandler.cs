﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.AddressedToRow>;
using MyRow = ReconnBooks.Default.AddressedToRow;

namespace ReconnBooks.Default;

public interface IAddressedToListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class AddressedToListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IAddressedToListHandler
{
    public AddressedToListHandler(IRequestContext context)
            : base(context)
    {
    }
}