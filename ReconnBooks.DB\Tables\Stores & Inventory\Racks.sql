﻿CREATE TABLE [dbo].[Racks]
(
    [RackId]            INT				    NOT NULL		IDENTITY (1, 1),
    [RackNo]            NVARCHAR (100)      NOT NULL,
    [CompartmentNo]     NVARCHAR (100)          NULL,
    [BinNo]             NVARCHAR (100)          NULL,
    [RackDescription]   NVARCHAR (500)          NULL,
    [Remarks]           NVARCHAR (MAX)          NULL,
    [StoreId]           INT				    NOT NULL    CONSTRAINT [DF_Racks_StoreId]       DEFAULT ((1)),
    [Discontinued]      BIT             	NOT NULL	CONSTRAINT [DF_Racks_Discontinued]  DEFAULT ((0)),
    [ClientId]          INT				    NOT NULL	CONSTRAINT [DF_Racks_ClientId]      DEFAULT ((0)),
   
   CONSTRAINT [PK_Racks]			PRIMARY KEY CLUSTERED ([RackId] ASC),
   CONSTRAINT [FK_Racks_Stores] 	FOREIGN KEY ([StoreId])		REFERENCES [dbo].[Stores] ([StoreId]),
);
GO
CREATE NONCLUSTERED INDEX [StoreId]
    ON [dbo].[Racks]([StoreId] ASC);
