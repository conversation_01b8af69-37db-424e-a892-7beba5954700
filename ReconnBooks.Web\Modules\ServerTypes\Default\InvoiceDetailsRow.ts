﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface InvoiceDetailsRow {
    RowNumber?: number;
    InvoiceDetailId?: number;
    InvoiceId?: number;
    InvoiceNo?: string;
    InvoiceDate?: string;
    InvoiceMonth?: string;
    ClientId?: number;
    FinancialYearId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    Quantity?: number;
    UnitId?: number;
    UnitName?: string;
    RevisedQuantity?: number;
    UnitPrice?: number;
    NetUnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    GSTRateRemarks?: string;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
    ProductSerialNos?: string;
    SKU?: string;
}

export abstract class InvoiceDetailsRow {
    static readonly idProperty = 'InvoiceDetailId';
    static readonly nameProperty = 'InvoiceDetailId';
    static readonly localTextPrefix = 'Default.InvoiceDetails';
    static readonly lookupKey = 'Default.InvoiceDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<InvoiceDetailsRow>('Default.InvoiceDetails') }
    static async getLookupAsync() { return getLookupAsync<InvoiceDetailsRow>('Default.InvoiceDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<InvoiceDetailsRow>();
}