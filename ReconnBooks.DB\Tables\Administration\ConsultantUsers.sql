﻿CREATE TABLE [dbo].[ConsultantUsers]
(
    [ConsultantUserId]  INT     NOT NULL    IDENTITY (1, 1),
    [UserId]            INT     NOT NULL,
    [ConsultantId]      INT     NOT NULL,
    [Status]            BIT     NOT NULL    DEFAULT ((0)),

    CONSTRAINT [PK_ConsultantUsers] PRIMARY KEY CLUSTERED   ([ConsultantUserId] ASC, [UserId] ASC),
    CONSTRAINT [FK_ConsultantUsers_Consultants] FOREIGN KEY ([ConsultantId])    REFERENCES  [dbo].[Consultants] ([ConsultantId]),
    CONSTRAINT [FK_ConsultantUsers_Users]       FOREIGN KEY ([UserId])          REFERENCES  [dbo].[Users] ([UserId])
);
GO
CREATE NONCLUSTERED INDEX [UserId]
    ON [dbo].[ConsultantUsers]([UserId] ASC);
GO
CREATE NONCLUSTERED INDEX [ConsultantId]
    ON [dbo].[ConsultantUsers]([ConsultantId] ASC);


