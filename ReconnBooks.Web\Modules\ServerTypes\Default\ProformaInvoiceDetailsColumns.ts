﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { ProformaInvoiceDetailsRow } from "./ProformaInvoiceDetailsRow";

export interface ProformaInvoiceDetailsColumns {
    RowNumber: Column<ProformaInvoiceDetailsRow>;
    CommodityName: Column<ProformaInvoiceDetailsRow>;
    CommodityCode: Column<ProformaInvoiceDetailsRow>;
    CommodityType: Column<ProformaInvoiceDetailsRow>;
    HSNSACCode: Column<ProformaInvoiceDetailsRow>;
    Quantity: Column<ProformaInvoiceDetailsRow>;
    UnitName: Column<ProformaInvoiceDetailsRow>;
    UnitPrice: Column<ProformaInvoiceDetailsRow>;
    UnitAmount: Column<ProformaInvoiceDetailsRow>;
    DiscountPercent: Column<ProformaInvoiceDetailsRow>;
    DiscountAmountPerUnit: Column<ProformaInvoiceDetailsRow>;
    NetDiscountAmount: Column<ProformaInvoiceDetailsRow>;
    TaxableAmountPerUnit: Column<ProformaInvoiceDetailsRow>;
    NetTaxableAmount: Column<ProformaInvoiceDetailsRow>;
    GSTRateRemarks: Column<ProformaInvoiceDetailsRow>;
    IGSTRate: Column<ProformaInvoiceDetailsRow>;
    IGSTAmountPerUnit: Column<ProformaInvoiceDetailsRow>;
    NetIGSTAmount: Column<ProformaInvoiceDetailsRow>;
    CGSTRate: Column<ProformaInvoiceDetailsRow>;
    CGSTAmountPerUnit: Column<ProformaInvoiceDetailsRow>;
    NetCGSTAmount: Column<ProformaInvoiceDetailsRow>;
    SGSTRate: Column<ProformaInvoiceDetailsRow>;
    SGSTAmountPerUnit: Column<ProformaInvoiceDetailsRow>;
    NetSGSTAmount: Column<ProformaInvoiceDetailsRow>;
    NetAmount: Column<ProformaInvoiceDetailsRow>;
    NetPricePerUnit: Column<ProformaInvoiceDetailsRow>;
    CommodityDescription: Column<ProformaInvoiceDetailsRow>;
    ProformaInvoiceDetailId: Column<ProformaInvoiceDetailsRow>;
    ProformaInvoiceNo: Column<ProformaInvoiceDetailsRow>;
}

export class ProformaInvoiceDetailsColumns extends ColumnsBase<ProformaInvoiceDetailsRow> {
    static readonly columnsKey = 'Default.ProformaInvoiceDetails';
    static readonly Fields = fieldsProxy<ProformaInvoiceDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types