﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ProductTypesForm {
    ProductType: StringEditor;
}

export class ProductTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.ProductTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProductTypesForm.init)  {
            ProductTypesForm.init = true;

            var w0 = StringEditor;

            initFormType(ProductTypesForm, [
                'ProductType', w0
            ]);
        }
    }
}