import { CommoditiesRow, CommodityTypesRow, QuotationDetailsColumns, QuotationDetailsRow, QuotationDetailsService, UnitsRow, GstRatesRow, QuotationsService } from '@/ServerTypes/Default';
import { Aggregators, Decorators, EntityGrid, toId, tryFirst, Widget, confirmDialog, ToolButton, getRemoteData, EditorProps } from '@serenity-is/corelib';
import { Column } from '@serenity-is/sleekgrid';
import { QuotationDetailsDialog } from './QuotationDetailsDialog';
import { ReconnGridEditorBase } from '../../Common/Helpers/ReconnGridEditorBase';
import { QuickSearchField } from '@serenity-is/corelib';
import { ExcelExportHelper } from '@serenity-is/extensions';
import { notifyWarning, formatDate } from "@serenity-is/corelib";
import { DetailsGridExcelExportButton } from '../../Common/Helpers/DetailsGridExcelExportButton';

@Decorators.registerEditor('ReconnBooks.Default.QuotationDetailsGridEditor')
@Decorators.registerClass('ReconnBooks.Default.QuotationDetailsGridEditor')
export class QuotationDetailsGridEditor extends ReconnGridEditorBase<QuotationDetailsRow> {
    protected getColumnsKey() { return QuotationDetailsColumns.columnsKey; }
    protected getDialogType() { return QuotationDetailsDialog; }
    protected getLocalTextDbPrefix() { return QuotationDetailsRow.localTextPrefix; }
    protected getService() { return QuotationsService.baseUrl; }

    //public PlaceOfSupplyStateId: number;
    private isSamePlaceOfSupply: boolean;

    public SetPlaceOfSupply(isSamePlaceOfSupply: boolean) {
        this.isSamePlaceOfSupply = isSamePlaceOfSupply;

        const columns = this.allColumns;
        if (this.isSamePlaceOfSupply) {
            this.hideIGSTAndShowCGSTSGST(columns);
        }
        else {
            this.showIGSTAndHideCGSTSGST(columns);
        }
        this.slickGrid.setColumns(columns);
    }

    protected getQuickSearchFields(): QuickSearchField[] {
        return [
            { name: 'CommodityName', title: 'Commodity Name' },
            { name: 'CommodityDescription', title: 'Commodity Description' }
        ];
    }

    private showIGSTAndHideCGSTSGST(columns: Column<any>[]) {
        tryFirst(columns, x => x.field === 'CGSTRate').visible = false;
        tryFirst(columns, x => x.field === 'NetCGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'PerUnitCGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'NetSGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'PerUnitSGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'SGSTRate').visible = false;
        tryFirst(columns, x => x.field === 'NetIGSTAmount').visible = true;
        tryFirst(columns, x => x.field === 'PerUnitIGSTAmount').visible = true;
        tryFirst(columns, x => x.field === 'IGSTRate').visible = true;
    }

    private hideIGSTAndShowCGSTSGST(columns: Column<any>[]) {
        tryFirst(columns, x => x.field === 'IGSTRate').visible = false;
        tryFirst(columns, x => x.field === 'NetIGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'PerUnitIGSTAmount').visible = false;
        tryFirst(columns, x => x.field === 'CGSTRate').visible = true;
        tryFirst(columns, x => x.field === 'NetCGSTAmount').visible = true;
        tryFirst(columns, x => x.field === 'PerUnitCGSTAmount').visible = true;
        tryFirst(columns, x => x.field === 'SGSTRate').visible = true;
        tryFirst(columns, x => x.field === 'NetSGSTAmount').visible = true;
        tryFirst(columns, x => x.field === 'PerUnitSGSTAmount').visible = true;
    }

    protected getButtons(): ToolButton[] {
        const buttons = super.getButtons();
        // Get the button with name 'Add'
        const addButton = buttons.find(button => button.title === 'Add');

        //check if addButton exists before setting OnClick
        if (addButton) {
            addButton.onClick = () => {
                const dialog = new QuotationDetailsDialog({}); // Pass current items in the grid to the child dialog

                dialog.onSave = (newItem, callback) => {

                    var newEntity = newItem.request.Entity;
                    // Check for duplicates before saving
                    const existingItems = this.view.getItems();
                    const currentlyExistingItem = existingItems.find(item => item.CommodityId == newEntity.CommodityId && item.CommodityDescription == newEntity.CommodityDescription);

                    if (currentlyExistingItem) {
                        // Show confirmation dialog for handling duplicates
                        confirmDialog(
                            `An item with the name "${currentlyExistingItem.CommodityName}" already exists. Do you want to add the quantity (${newEntity.Quantity}) to the existing item?`,
                            () => {
                                // User clicked "Yes" - Update existing item
                                currentlyExistingItem.Quantity += newEntity.Quantity;
                                currentlyExistingItem.Amount = currentlyExistingItem.Quantity * currentlyExistingItem.UnitPrice;

                                currentlyExistingItem.DiscountAmountPerUnit = (currentlyExistingItem.UnitPrice) * (currentlyExistingItem.DiscountPercent ?? 0) / 100;
                                currentlyExistingItem.DiscountAmount = (currentlyExistingItem.Amount) * (currentlyExistingItem.DiscountPercent ?? 0) / 100;

                                currentlyExistingItem.TaxableAmountPerUnit = currentlyExistingItem.UnitPrice - (currentlyExistingItem.DiscountAmountPerUnit ?? 0);
                                currentlyExistingItem.NetTaxableAmount = currentlyExistingItem.Amount - (currentlyExistingItem.DiscountAmount ?? 0);

                                currentlyExistingItem.PerUnitIGSTAmount = currentlyExistingItem.TaxableAmountPerUnit * currentlyExistingItem.IGSTRate / 100;
                                currentlyExistingItem.NetIGSTAmount = currentlyExistingItem.PerUnitIGSTAmount * currentlyExistingItem.Quantity;
                                currentlyExistingItem.PerUnitCGSTAmount = currentlyExistingItem.TaxableAmountPerUnit * currentlyExistingItem.CGSTRate / 100;
                                currentlyExistingItem.NetCGSTAmount = currentlyExistingItem.PerUnitCGSTAmount * currentlyExistingItem.Quantity;
                                currentlyExistingItem.PerUnitSGSTAmount = currentlyExistingItem.TaxableAmountPerUnit * currentlyExistingItem.SGSTRate / 100;
                                currentlyExistingItem.NetSGSTAmount = currentlyExistingItem.PerUnitSGSTAmount * currentlyExistingItem.Quantity;

                                currentlyExistingItem.PerUnitPrice = currentlyExistingItem.TaxableAmountPerUnit + (this.isSamePlaceOfSupply ? currentlyExistingItem.PerUnitCGSTAmount + currentlyExistingItem.PerUnitSGSTAmount : currentlyExistingItem.PerUnitIGSTAmount);
                                currentlyExistingItem.NetAmount = currentlyExistingItem.NetTaxableAmount + (this.isSamePlaceOfSupply ? currentlyExistingItem.NetCGSTAmount + currentlyExistingItem.NetSGSTAmount : currentlyExistingItem.NetIGSTAmount);

                                const id = currentlyExistingItem[this.getIdProperty()];

                                //this.view.updateItem(id, currentlyExistingItem); // Update the grid
                                //this.view.refresh(); // Refresh the grid view
                                dialog.dialogClose("save-and-close"); // Close the dialog
                            },
                            {
                                onNo: () => {
                                    // User clicked "No" - Do nothing
                                    //console.log("Duplicate addition canceled.");
                                    //dialog.load()
                                }
                            }
                        );
                        return;
                    }

                    // No duplicates, proceed with normal save operation
                    this.save(newItem, async (response) => {
                        // Update the RowNumber and foreign key lookups for the newly added item
                        const lastItem = this.view.getItems()[this.view.getLength() - 1];
                        lastItem.RowNumber = this.view.getLength();

                        // Update foreign key lookups directly on the item before calling callback
                        await this.updateLookupsDirectly(lastItem);

                        // Call the original callback after lookups are updated
                        callback(response);
                    });

                    dialog.dialogClose();
                };
                //dialog.PlaceOfSupplyStateId = this.PlaceOfSupplyStateId;
                dialog.IsSamePlaceOfSupply = this.isSamePlaceOfSupply;
                dialog.loadEntityAndOpenDialog(this.getNewEntity());
            };
        }

        buttons.push(DetailsGridExcelExportButton({
            grid: this,
            fileName: 'QuotationDetails',
            hint: 'Export current view to Excel',
            title: 'Excel',
            separator: true
        }));

        return buttons;
    }

    protected getAddButtonCaption() {
        return "Add";
    }

    // --- TO add Colum summary on grid.make sure to enable footer on grid
    protected setupSummaryOptions(): void {
        this.view.setSummaryOptions({
            aggregators: [
                new Aggregators.Avg('UnitPrice'),
                new Aggregators.Sum('Amount'),
                new Aggregators.Sum('DiscountAmount'),
                new Aggregators.Sum('TaxableAmountPerUnit'),
                new Aggregators.Sum('NetTaxableAmount'),

                new Aggregators.Sum('PerUnitIGSTAmount'),
                new Aggregators.Sum('NetIGSTAmount'),

                new Aggregators.Sum('PerUnitCGSTAmount'),
                new Aggregators.Sum('NetCGSTAmount'),

                new Aggregators.Sum('PerUnitSGSTAmount'),
                new Aggregators.Sum('NetSGSTAmount'),

                new Aggregators.Sum('PerUnitPrice'),
                new Aggregators.Sum('NetAmount'),
            ]
        });
    }

    protected createSlickGrid() {
        var grid = super.createSlickGrid();
        this.renameGridFooterClass();
        return grid;
    }

    protected getFrozenFields(): string[] {
        return ['RowNumber', 'CommodityName'];
    }

    protected initEntityDialog(itemType: string, dialog: Widget<any>) {
        super.initEntityDialog(itemType, dialog);

        // passing PlaceOfSupplyStateId from grid editor to detail dialog
        (dialog as QuotationDetailsDialog).IsSamePlaceOfSupply = this.isSamePlaceOfSupply;
    }

    protected validateEntity(row: QuotationDetailsRow, id: number): boolean {
        if (!row.CommodityTypeId || !row.CommodityId || !row.UnitId || !row.GSTRateId) {
            console.error(`Validation failed for row ${id}: Required fields are missing.`);
            return false; // Return false if mandatory fields are missing
        }
        return true;
    }

    private async updateLookupsDirectly(item: QuotationDetailsRow): Promise<void> {
        try {
            // Get all lookups in parallel
            const [commodityTypesLookup, commoditiesLookup, unitsLookup, gstRatesLookup] = await Promise.all([
                CommodityTypesRow.getLookupAsync(),
                CommoditiesRow.getLookupAsync(),
                UnitsRow.getLookupAsync(),
                GstRatesRow.getLookupAsync()
            ]);

            // Update all properties directly on the item (no updateItem call)
            item.CommodityTypeId = toId(item.CommodityTypeId);
            if (item.CommodityTypeId && commodityTypesLookup.itemById[item.CommodityTypeId]) {
                item.CommodityType = commodityTypesLookup.itemById[item.CommodityTypeId].CommodityType;
            }

            item.CommodityId = toId(item.CommodityId);
            if (item.CommodityId && commoditiesLookup.itemById[item.CommodityId]) {
                item.CommodityName = commoditiesLookup.itemById[item.CommodityId].CommodityName;
                item.CommodityCode = commoditiesLookup.itemById[item.CommodityId].CommodityCode;
            }

            item.UnitId = toId(item.UnitId);
            if (item.UnitId && unitsLookup.itemById[item.UnitId]) {
                item.UnitName = unitsLookup.itemById[item.UnitId].UnitName;
            }

            item.GSTRateId = toId(item.GSTRateId);
            if (item.GSTRateId && gstRatesLookup.itemById[item.GSTRateId]) {
                item.GSTRateRemarks = gstRatesLookup.itemById[item.GSTRateId].Remarks;
            }

            // No updateItem call here - just update the object properties directly

        } catch (error) {
            console.error('Error in direct lookup updates:', error);
        }
    }
}