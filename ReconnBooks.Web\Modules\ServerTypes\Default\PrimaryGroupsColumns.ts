﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { PrimaryGroupsRow } from "./PrimaryGroupsRow";

export interface PrimaryGroupsColumns {
    RowNumber: Column<PrimaryGroupsRow>;
    PrimaryGroupCode: Column<PrimaryGroupsRow>;
    PrimaryGroupName: Column<PrimaryGroupsRow>;
    Remarks: Column<PrimaryGroupsRow>;
    PrimaryGroupId: Column<PrimaryGroupsRow>;
}

export class PrimaryGroupsColumns extends ColumnsBase<PrimaryGroupsRow> {
    static readonly columnsKey = 'Default.PrimaryGroups';
    static readonly Fields = fieldsProxy<PrimaryGroupsColumns>();
}