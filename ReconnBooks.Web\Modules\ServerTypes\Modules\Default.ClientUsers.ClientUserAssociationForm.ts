﻿import { PrefixedContext, initFormType } from "@serenity-is/corelib";
import { ClientUserSelector } from "../../Default/ClientUsers/ClientUserSelector";

export interface ClientUserAssociationForm {
    Clients: ClientUserSelector;
}

export class ClientUserAssociationForm extends PrefixedContext {
    static readonly formKey = 'Administration.ClientUserAssociationForm';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ClientUserAssociationForm.init)  {
            ClientUserAssociationForm.init = true;

            var w0 = ClientUserSelector;

            initFormType(ClientUserAssociationForm, [
                'Clients', w0
            ]);
        }
    }
}