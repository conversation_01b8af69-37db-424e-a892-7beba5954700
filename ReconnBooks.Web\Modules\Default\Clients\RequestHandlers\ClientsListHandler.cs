﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.ClientsRow>;
using MyRow = ReconnBooks.Default.ClientsRow;

namespace ReconnBooks.Default;

public interface IClientsListHandler : IList<PERSON>andler<MyRow, MyRequest, MyResponse> {}

public class ClientsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IClientsListHandler
{
    public ClientsListHandler(IRequestContext context)
            : base(context)
    {
    }
}