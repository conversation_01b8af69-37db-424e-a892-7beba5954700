﻿import { StringEditor, TextAreaEditor, ServiceLookupEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface WarehousesForm {
    WarehouseName: StringEditor;
    Description: TextAreaEditor;
    LocationId: ServiceLookupEditor;
    Remarks: StringEditor;
    Discontinued: BooleanEditor;
}

export class WarehousesForm extends PrefixedContext {
    static readonly formKey = 'Default.Warehouses';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!WarehousesForm.init)  {
            WarehousesForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;
            var w2 = ServiceLookupEditor;
            var w3 = BooleanEditor;

            initFormType(WarehousesForm, [
                'WarehouseName', w0,
                'Description', w1,
                'LocationId', w2,
                'Remarks', w0,
                'Discontinued', w3
            ]);
        }
    }
}