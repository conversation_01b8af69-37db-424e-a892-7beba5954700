﻿CREATE TABLE [dbo].[CustomerContacts] 
(
    [CustomerContactId] INT             NOT NULL    IDENTITY (1,1),
    [CustomerId]        INT             NOT NULL,
    [TitleId]           INT                 NULL,
    [ContactName]       NVARCHAR (300)      NULL,
    [DesignationId]     INT                 NULL,
    [DepartmentId]      INT                 NULL,
    [OfficePhoneNo]     NVARCHAR (50)       NULL,
    [ExtensionNo]       NVARCHAR (50)       NULL,
    [MobileNo]          NVARCHAR (18)       NULL,
    [AlternateNo]       NVARCHAR (18)       NULL,
    [Email]             NVARCHAR (100)      NULL,
    [Status]            BIT             NOT NULL    DEFAULT ((1)),

    CONSTRAINT [PK_CustomerContacts] PRIMARY KEY    CLUSTERED	([CustomerContactId] ASC),
    CONSTRAINT [FK_CustomerContacts_Titles]         FOREIGN KEY ([TitleId])         REFERENCES [dbo].[Titles]       ([TitleId]),
    CONSTRAINT [FK_CustomerContacts_Designations]   FOREIGN KEY ([DesignationId])   REFERENCES [dbo].[Designations] ([DesignationId]),
    CONSTRAINT [FK_CustomerContacts_Departments]    FOREIGN KEY ([DepartmentId])    REFERENCES [dbo].[Departments]  ([DepartmentId])
);
GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[CustomerContacts]([CustomerId] ASC);