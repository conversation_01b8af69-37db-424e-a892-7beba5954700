import { Chart } from "../shared/chartjs-init";

export const ordersByQuarterChart = (canvas: HTMLCanvasElement): Chart<"line"> => {
    // Parse data from the injected script
    const invoicesData = (window as any).invoicesData;
    const labels = invoicesData.map((d: any) => d.Month); // X-axis (Months)
    const invoiceTotals = invoicesData.map((d: any) => d.InvoiceTotal); // Y-axis (Invoice Totals)

    const receiptsData = (window as any).receiptsData;
    const receiptTotals = receiptsData.map((g: any) => g.ReceiptTotal); // Y-axis (Receipts Totals)

    return new Chart(canvas, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Total Invoices',
                    backgroundColor: 'rgba(163, 212, 165, 0.7)', 
                    borderColor: 'rgba(103, 172, 105, 1)', 
                    borderWidth: 4,
                    fill: true,
                    data: invoiceTotals
                },
                {
                    label: 'Total Receipts',
                    backgroundColor: 'rgba(166, 200, 255, 0.7)', 
                    borderColor: 'rgba(106, 160, 255, 1)', 
                    borderWidth: 4,
                    fill: true,
                    data: receiptTotals
                }
            ]
        },
        options: {
            elements: {
                point: {
                    radius: 5,
                    hitRadius: 6
                }
            },
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    ticks: { color: 'rgb(140, 142, 150)' },
                    grid: { display: false, }
                },
                y: {
                    ticks: { color: 'rgb(140, 142, 150)' },
                    grid: {
                        color: 'rgba(140, 142, 150, 0.15)',
                        tickBorderDash: [8, 4]
                    }
                }
            }
        }
    });
};