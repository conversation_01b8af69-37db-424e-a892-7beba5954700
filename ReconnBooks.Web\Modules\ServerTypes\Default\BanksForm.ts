﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface BanksForm {
    BankName: StringEditor;
    BankShortName: StringEditor;
}

export class BanksForm extends PrefixedContext {
    static readonly formKey = 'Default.Banks';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!BanksForm.init)  {
            BanksForm.init = true;

            var w0 = StringEditor;

            initFormType(BanksForm, [
                'BankName', w0,
                'BankShortName', w0
            ]);
        }
    }
}