﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface BanksRow {
    RowNumber?: number;
    BankId?: number;
    BankName?: string;
    BankShortName?: string;
}

export abstract class BanksRow {
    static readonly idProperty = 'BankId';
    static readonly nameProperty = 'BankName';
    static readonly localTextPrefix = 'Default.Banks';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<BanksRow>();
}