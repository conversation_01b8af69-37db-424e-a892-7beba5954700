﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface FinancialYearsRow {
    RowNumber?: number;
    FinancialYearId?: number;
    FinancialYearName?: string;
    FromDate?: string;
    ToDate?: string;
    Remarks?: string;
}

export abstract class FinancialYearsRow {
    static readonly idProperty = 'FinancialYearId';
    static readonly nameProperty = 'FinancialYearName';
    static readonly localTextPrefix = 'Default.FinancialYears';
    static readonly lookupKey = 'Default.FinancialYears';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<FinancialYearsRow>('Default.FinancialYears') }
    static async getLookupAsync() { return getLookupAsync<FinancialYearsRow>('Default.FinancialYears') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<FinancialYearsRow>();
}