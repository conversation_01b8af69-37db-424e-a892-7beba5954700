﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { SecondaryGroupsRow } from "./SecondaryGroupsRow";

export namespace SecondaryGroupsService {
    export const baseUrl = 'Default/SecondaryGroups';

    export declare function Create(request: SaveRequest<SecondaryGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SecondaryGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SecondaryGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SecondaryGroupsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SecondaryGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SecondaryGroupsRow>>;

    export const Methods = {
        Create: "Default/SecondaryGroups/Create",
        Update: "Default/SecondaryGroups/Update",
        Delete: "Default/SecondaryGroups/Delete",
        Retrieve: "Default/SecondaryGroups/Retrieve",
        List: "Default/SecondaryGroups/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>SecondaryGroupsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}