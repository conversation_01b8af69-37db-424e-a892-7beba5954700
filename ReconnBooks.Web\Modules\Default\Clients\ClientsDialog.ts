import { ClientsForm, ClientsRow, ClientsService, StatesRow } from '@/ServerTypes/Default';
import { Decorators, alertDialog } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.ClientsDialog')
@Decorators.panel()
@Decorators.responsive()
export class ClientsDialog extends PendingChangesConfirmDialog<ClientsRow> {
    protected getFormKey() { return ClientsForm.formKey; }
    protected getRowDefinition() { return ClientsRow; }
    protected getService() { return ClientsService.baseUrl; }

    protected form = new ClientsForm(this.idPrefix);

    constructor() {
        super();

        this.form.GSTIN.element.on('input', () => {
            this.extractPlaceOfSupplyState();
            this.form.GSTIN.value = this.form.GSTIN.value.toUpperCase();
        });

        this.form.PAN.element.on('input', () => {
            this.form.PAN.value = this.form.PAN.value.toUpperCase();
        });

        this.form.TANNo.element.on('input', () => {
            this.form.TANNo.value = this.form.TANNo.value.toUpperCase();
        });

        this.form.CINNo.element.on('input', () => {
            this.form.CINNo.value = this.form.CINNo.value.toUpperCase();
        });

        this.form.IECNo.element.on('input', () => {
            this.form.IECNo.value = this.form.IECNo.value.toUpperCase();
        })

        this.form.UdyamNo.element.on('input', () => {
            this.form.UdyamNo.value = this.form.UdyamNo.value.toUpperCase();
        })
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isEditMode()) {
            this.extractPlaceOfSupplyState();
        }
        this.setDialogsLoadedState();
    }

    private async extractPlaceOfSupplyState() {
        var stateCodeNo = this.form.GSTIN.value.substring(0, 2);
        var stateRow = (await StatesRow.getLookupAsync()).items.find(a => a.StateCodeNo === stateCodeNo);
        if (stateRow) {
            this.form.PlaceOfSupplyId.value = stateRow.StateId.toString();
        }
        else {
            this.form.PlaceOfSupplyId.value = null;
        }
    }

    protected validateBeforeSave(): boolean {
        const gstNumberPattern = /^[0-9]{2}.+$/;
        if (!(/^(Un-Registered)?$/i.test(this.form.GSTIN.value) || gstNumberPattern.test(this.form.GSTIN.value))) {
            alertDialog("Please enter as 'Un-Registered' in GSTIN field or enter a valid GSTIN number.");
            return false;
        }
        return super.validateBeforeSave();
    }
}