﻿import { SaveRequest, SaveR<PERSON>ponse, ServiceOptions, DeleteRequest, DeleteR<PERSON>ponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ReturnsFilingFormsRow } from "./ReturnsFilingFormsRow";

export namespace ReturnsFilingFormsService {
    export const baseUrl = 'Default/ReturnsFilingForms';

    export declare function Create(request: SaveRequest<ReturnsFilingFormsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ReturnsFilingFormsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ReturnsFilingFormsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReturnsFilingFormsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ReturnsFilingFormsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ReturnsFilingFormsRow>>;

    export const Methods = {
        Create: "Default/ReturnsFilingForms/Create",
        Update: "Default/ReturnsFilingForms/Update",
        Delete: "Default/ReturnsFilingForms/Delete",
        Retrieve: "Default/ReturnsFilingForms/Retrieve",
        List: "Default/ReturnsFilingForms/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ReturnsFilingFormsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}