﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { DebitNotesRow } from "./DebitNotesRow";

export interface DebitNotesColumns {
    RowNumber: Column<DebitNotesRow>;
    DebitNoteNo: Column<DebitNotesRow>;
    DebitNoteDate: Column<DebitNotesRow>;
    VendorName: Column<DebitNotesRow>;
    FinancialYearName: Column<DebitNotesRow>;
    DebitNoteMonth: Column<DebitNotesRow>;
    PurchaseOrderNo: Column<DebitNotesRow>;
    PurchaseReturnNo: Column<DebitNotesRow>;
    NetTaxableAmount: Column<DebitNotesRow>;
    NetCGSTAmount: Column<DebitNotesRow>;
    NetSGSTAmount: Column<DebitNotesRow>;
    NetIGSTAmount: Column<DebitNotesRow>;
    DebitNoteAmount: Column<DebitNotesRow>;
    Remarks: Column<DebitNotesRow>;
    PreparedByUserUsername: Column<DebitNotesRow>;
    PreparedDate: Column<DebitNotesRow>;
    VerifiedByUserUsername: Column<DebitNotesRow>;
    VerifiedDate: Column<DebitNotesRow>;
    AuthorizedByUserUsername: Column<DebitNotesRow>;
    AuthorizedDate: Column<DebitNotesRow>;
    ModifiedByUserUsername: Column<DebitNotesRow>;
    ModifiedDate: Column<DebitNotesRow>;
    CancelledByUserUsername: Column<DebitNotesRow>;
    CancelledDate: Column<DebitNotesRow>;
    AuthorizedStatus: Column<DebitNotesRow>;
    DebitNoteId: Column<DebitNotesRow>;
}

export class DebitNotesColumns extends ColumnsBase<DebitNotesRow> {
    static readonly columnsKey = 'Default.DebitNotes';
    static readonly Fields = fieldsProxy<DebitNotesColumns>();
}

[IndianNumberFormatter]; // referenced types