﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.CommodityTypesRow>;
using MyRow = ReconnBooks.Default.CommodityTypesRow;

namespace ReconnBooks.Default;

public interface ICommodityTypesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class CommodityTypesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, ICommodityTypesListHandler
{
    public CommodityTypesListHandler(IRequestContext context)
            : base(context)
    {
    }
}