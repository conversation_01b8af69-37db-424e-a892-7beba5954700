﻿import { StringEditor, DateEditor, ServiceLookupEditor, DecimalEditor, LookupEditor, TextAreaEditor, BooleanEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { EmployeesDialog } from "../../Default/Employees/EmployeesDialog";
import { FootNotesDialog } from "../../Default/FootNotes/FootNotesDialog";
import { HeaderNoteDialog } from "../../Default/HeaderNote/HeaderNoteDialog";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { QuotationDetailsGridEditor } from "../../Default/QuotationDetails/QuotationDetailsGridEditor";

export interface QuotationsForm {
    QuotationNo: StringEditor;
    QuotationDate: DateEditor;
    DocumentId: ServiceLookupEditor;
    CustomerId: ServiceLookupEditor;
    ReferenceNo: StringEditor;
    ReferenceDate: DateEditor;
    HeaderNoteId: ServiceLookupEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    QuotationDetailsList: QuotationDetailsGridEditor;
    SalesPerson: ServiceLookupEditor;
    RoundingOff: DecimalEditor;
    GrandTotal: DecimalEditor;
    FootNoteId: ServiceLookupEditor;
    PaymentTermsId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    Taxes: StringEditor;
    DeliveryPeriod: StringEditor;
    Warranty: StringEditor;
    Validity: StringEditor;
    Remarks: TextAreaEditor;
    AuthorizedStatus: BooleanEditor;
    QuotationStatus: BooleanEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class QuotationsForm extends PrefixedContext {
    static readonly formKey = 'Default.Quotations';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!QuotationsForm.init)  {
            QuotationsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = QuotationDetailsGridEditor;
            var w4 = DecimalEditor;
            var w5 = LookupEditor;
            var w6 = TextAreaEditor;
            var w7 = BooleanEditor;
            var w8 = DateTimeEditor;

            initFormType(QuotationsForm, [
                'QuotationNo', w0,
                'QuotationDate', w1,
                'DocumentId', w2,
                'CustomerId', w2,
                'ReferenceNo', w0,
                'ReferenceDate', w1,
                'HeaderNoteId', w2,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'QuotationDetailsList', w3,
                'SalesPerson', w2,
                'RoundingOff', w4,
                'GrandTotal', w4,
                'FootNoteId', w2,
                'PaymentTermsId', w2,
                'FinancialYearId', w5,
                'Taxes', w0,
                'DeliveryPeriod', w0,
                'Warranty', w0,
                'Validity', w0,
                'Remarks', w6,
                'AuthorizedStatus', w7,
                'QuotationStatus', w7,
                'PreparedByUserId', w5,
                'PreparedDate', w8,
                'VerifiedByUserId', w5,
                'VerifiedDate', w8,
                'AuthorizedByUserId', w5,
                'AuthorizedDate', w8,
                'ModifiedByUserId', w5,
                'ModifiedDate', w1,
                'CancelledByUserId', w5,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, HeaderNoteDialog, EmployeesDialog, FootNotesDialog, PaymentTermsDialog]); // referenced dialogs