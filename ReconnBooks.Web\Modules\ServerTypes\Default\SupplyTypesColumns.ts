﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { SupplyTypesRow } from "./SupplyTypesRow";

export interface SupplyTypesColumns {
    RowNumber: Column<SupplyTypesRow>;
    SupplyType: Column<SupplyTypesRow>;
    NatureOfSupply: Column<SupplyTypesRow>;
    SupplyTypeId: Column<SupplyTypesRow>;
    SetDefault: Column<SupplyTypesRow>;
}

export class SupplyTypesColumns extends ColumnsBase<SupplyTypesRow> {
    static readonly columnsKey = 'Default.SupplyTypes';
    static readonly Fields = fieldsProxy<SupplyTypesColumns>();
}