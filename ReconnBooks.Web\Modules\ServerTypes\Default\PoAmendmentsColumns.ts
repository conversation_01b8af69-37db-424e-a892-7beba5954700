﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { PoAmendmentsRow } from "./PoAmendmentsRow";

export interface PoAmendmentsColumns {
    RowNumber: Column<PoAmendmentsRow>;
    POAmendmentNo: Column<PoAmendmentsRow>;
    POAmendmentDate: Column<PoAmendmentsRow>;
    VendorName: Column<PoAmendmentsRow>;
    FinancialYearName: Column<PoAmendmentsRow>;
    POAmendmentMonth: Column<PoAmendmentsRow>;
    PurchaseOrderNo: Column<PoAmendmentsRow>;
    POAmendmentAmount: Column<PoAmendmentsRow>;
    TDSRateTransaction: Column<PoAmendmentsRow>;
    TCSRateNatureOfTransaction: Column<PoAmendmentsRow>;
    RoundingOff: Column<PoAmendmentsRow>;
    NetTaxableAmount: Column<PoAmendmentsRow>;
    NetCGSTAmount: Column<PoAmendmentsRow>;
    NetSGSTAmount: Column<PoAmendmentsRow>;
    NetIGSTAmount: Column<PoAmendmentsRow>;
    GrandTotal: Column<PoAmendmentsRow>;
    Remarks: Column<PoAmendmentsRow>;
    ClientId: Column<PoAmendmentsRow>;
    PreparedByUserUsername: Column<PoAmendmentsRow>;
    PreparedDate: Column<PoAmendmentsRow>;
    VerifiedByUserUsername: Column<PoAmendmentsRow>;
    VerifiedDate: Column<PoAmendmentsRow>;
    AuthorizedByUserUsername: Column<PoAmendmentsRow>;
    AuthorizedDate: Column<PoAmendmentsRow>;
    ModifiedByUserUsername: Column<PoAmendmentsRow>;
    ModifiedDate: Column<PoAmendmentsRow>;
    CancelledByUserUsername: Column<PoAmendmentsRow>;
    CancelledDate: Column<PoAmendmentsRow>;
    AuthorizedStatus: Column<PoAmendmentsRow>;
    PurchaseOrderDetailCommodityDescription: Column<PoAmendmentsRow>;
    POAmendmentId: Column<PoAmendmentsRow>;
}

export class PoAmendmentsColumns extends ColumnsBase<PoAmendmentsRow> {
    static readonly columnsKey = 'Default.PoAmendments';
    static readonly Fields = fieldsProxy<PoAmendmentsColumns>();
}

[IndianNumberFormatter]; // referenced types