﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ReplacementMethodsRow {
    RowNumber?: number;
    ReplacementMethodId?: number;
    ReplacementMethod?: string;
    Description?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class ReplacementMethodsRow {
    static readonly idProperty = 'ReplacementMethodId';
    static readonly nameProperty = 'ReplacementMethod';
    static readonly localTextPrefix = 'Default.ReplacementMethods';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ReplacementMethodsRow>();
}