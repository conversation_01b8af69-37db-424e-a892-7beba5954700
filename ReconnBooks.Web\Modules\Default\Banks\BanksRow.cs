using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("Banks")]
[DisplayName("Banks"), InstanceName("Banks"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[UniqueConstraint(new[] { "BankName", "BankShortName" })]
public sealed partial class BanksRow : Row<BanksRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Bank Id"), Identity, IdProperty]
    public int? BankId { get => fields.BankId[this]; set => fields.BankId[this] = value; }

    [DisplayName("Bank Name"), Size(50), NotNull, QuickSearch, NameProperty]
    public string BankName { get => fields.BankName[this]; set => fields.BankName[this] = value; }

    [DisplayName("Bank Short Name"), Size(15), NotNull]
    public string BankShortName { get => fields.BankShortName[this]; set => fields.BankShortName[this] = value; }
}