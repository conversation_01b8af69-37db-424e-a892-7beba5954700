﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface CustomerContactsRow {
    RowNumber?: number;
    CustomerContactId?: number;
    CustomerId?: number;
    TitleId?: number;
    ContactName?: string;
    DesignationId?: number;
    DepartmentId?: number;
    OfficePhoneNo?: string;
    ExtensionNo?: string;
    MobileNo?: string;
    AlternateNo?: string;
    EMail?: string;
    Status?: boolean;
    CustomerCompanyName?: string;
    TitleOfRespect?: string;
    Designation?: string;
    DepartmentName?: string;
}

export abstract class CustomerContactsRow {
    static readonly idProperty = 'CustomerContactId';
    static readonly nameProperty = 'ContactName';
    static readonly localTextPrefix = 'Default.CustomerContacts';
    static readonly lookupKey = 'Default.CustomerContacts';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CustomerContactsRow>('Default.CustomerContacts') }
    static async getLookupAsync() { return getLookupAsync<CustomerContactsRow>('Default.CustomerContacts') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CustomerContactsRow>();
}