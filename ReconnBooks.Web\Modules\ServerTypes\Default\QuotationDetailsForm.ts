﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { UnitsDialog } from "../../Default/Units/UnitsDialog";

export interface QuotationDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    Quantity: DecimalEditor;
    UnitId: ServiceLookupEditor;
    RevisedQuantity: DecimalEditor;
    UnitPrice: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmountPerUnit: DecimalEditor;
    DiscountAmount: DecimalEditor;
    Amount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    PerUnitIGSTAmount: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    PerUnitCGSTAmount: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    PerUnitSGSTAmount: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    GrossTotal: DecimalEditor;
    PerUnitPrice: DecimalEditor;
    NetAmount: DecimalEditor;
}

export class QuotationDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.QuotationDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!QuotationDetailsForm.init)  {
            QuotationDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;

            initFormType(QuotationDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'Quantity', w4,
                'UnitId', w0,
                'RevisedQuantity', w4,
                'UnitPrice', w4,
                'DiscountPercent', w4,
                'DiscountAmountPerUnit', w4,
                'DiscountAmount', w4,
                'Amount', w4,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w4,
                'NetTaxableAmount', w4,
                'IGSTRate', w4,
                'PerUnitIGSTAmount', w4,
                'NetIGSTAmount', w4,
                'CGSTRate', w4,
                'PerUnitCGSTAmount', w4,
                'NetCGSTAmount', w4,
                'SGSTRate', w4,
                'PerUnitSGSTAmount', w4,
                'NetSGSTAmount', w4,
                'GrossTotal', w4,
                'PerUnitPrice', w4,
                'NetAmount', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, UnitsDialog]); // referenced dialogs