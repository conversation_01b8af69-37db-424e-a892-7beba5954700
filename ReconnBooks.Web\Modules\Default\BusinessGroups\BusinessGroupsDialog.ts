import { BusinessGroupsForm, BusinessGroupsRow, BusinessGroupsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.BusinessGroupsDialog')
export class BusinessGroupsDialog extends PendingChangesConfirmDialog<BusinessGroupsRow> {
    protected getFormKey() { return BusinessGroupsForm.formKey; }
    protected getRowDefinition() { return BusinessGroupsRow; }
    protected getService() { return BusinessGroupsService.baseUrl; }

    protected form = new BusinessGroupsForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}