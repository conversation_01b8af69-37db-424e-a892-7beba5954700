﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { VendorContactsRow } from "./VendorContactsRow";

export namespace VendorContactsService {
    export const baseUrl = 'Default/VendorContacts';

    export declare function Create(request: SaveRequest<VendorContactsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorContactsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorContactsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorContactsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorContactsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorContactsRow>>;

    export const Methods = {
        Create: "Default/VendorContacts/Create",
        Update: "Default/VendorContacts/Update",
        Delete: "Default/VendorContacts/Delete",
        Retrieve: "Default/VendorContacts/Retrieve",
        List: "Default/VendorContacts/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>VendorContactsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}