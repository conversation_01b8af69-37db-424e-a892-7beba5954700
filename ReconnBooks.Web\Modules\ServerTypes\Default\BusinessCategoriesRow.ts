﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface BusinessCategoriesRow {
    RowNumber?: number;
    BusinessCategoryId?: number;
    BusinessCategory?: string;
    Description?: string;
}

export abstract class BusinessCategoriesRow {
    static readonly idProperty = 'BusinessCategoryId';
    static readonly nameProperty = 'BusinessCategory';
    static readonly localTextPrefix = 'Default.BusinessCategories';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<BusinessCategoriesRow>();
}