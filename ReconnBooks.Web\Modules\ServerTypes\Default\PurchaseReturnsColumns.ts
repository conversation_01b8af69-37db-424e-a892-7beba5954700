﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { PurchaseReturnsRow } from "./PurchaseReturnsRow";

export interface PurchaseReturnsColumns {
    RowNumber: Column<PurchaseReturnsRow>;
    PurchaseReturnNo: Column<PurchaseReturnsRow>;
    PurchaseReturnDate: Column<PurchaseReturnsRow>;
    VendorName: Column<PurchaseReturnsRow>;
    PurchaseOrderNo: Column<PurchaseReturnsRow>;
    FinancialYearName: Column<PurchaseReturnsRow>;
    PurchaseReturnMonth: Column<PurchaseReturnsRow>;
    UploadFiles: Column<PurchaseReturnsRow>;
    Remarks: Column<PurchaseReturnsRow>;
    ClientId: Column<PurchaseReturnsRow>;
    PreparedByUserUsername: Column<PurchaseReturnsRow>;
    PreparedDate: Column<PurchaseReturnsRow>;
    VerifiedByUserUsername: Column<PurchaseReturnsRow>;
    VerifiedDate: Column<PurchaseReturnsRow>;
    AuthorizedByUserUsername: Column<PurchaseReturnsRow>;
    AuthorizedDate: Column<PurchaseReturnsRow>;
    ModifiedByUserUsername: Column<PurchaseReturnsRow>;
    ModifiedDate: Column<PurchaseReturnsRow>;
    CancelledByUserUsername: Column<PurchaseReturnsRow>;
    CancelledDate: Column<PurchaseReturnsRow>;
    AuthorizedStatus: Column<PurchaseReturnsRow>;
    PurchaseReturnId: Column<PurchaseReturnsRow>;
}

export class PurchaseReturnsColumns extends ColumnsBase<PurchaseReturnsRow> {
    static readonly columnsKey = 'Default.PurchaseReturns';
    static readonly Fields = fieldsProxy<PurchaseReturnsColumns>();
}