﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface DebitNoteDetailsRow {
    RowNumber?: number;
    DebitNoteDetailId?: number;
    DebitNoteId?: number;
    PurchaseOrderDetailId?: number;
    PurchaseReturnDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    PoQuantity?: number;
    PoUnitId?: number;
    ReturnedQuantity?: number;
    ReturnedUnitId?: number;
    SerialNos?: string;
    UnitPrice?: number;
    UnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
    RejectionReasonId?: number;
    AssessmentRemarks?: string;
    ReplacementMethodId?: number;
    Remarks?: string;
    DebitNoteNo?: string;
    PurchaseOrderDetailCommodityDescription?: string;
    PurchaseReturnDetailCommodityDescription?: string;
    PoUnitUnitName?: string;
    ReturnedUnitUnitName?: string;
    GSTRateRemarks?: string;
    RejectionReason?: string;
    ReplacementMethod?: string;
}

export abstract class DebitNoteDetailsRow {
    static readonly idProperty = 'DebitNoteDetailId';
    static readonly nameProperty = 'DebitNoteDetailId';
    static readonly localTextPrefix = 'Default.DebitNoteDetails';
    static readonly lookupKey = 'Default.DebitNoteDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<DebitNoteDetailsRow>('Default.DebitNoteDetails') }
    static async getLookupAsync() { return getLookupAsync<DebitNoteDetailsRow>('Default.DebitNoteDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DebitNoteDetailsRow>();
}