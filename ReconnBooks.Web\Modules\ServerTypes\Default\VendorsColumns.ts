﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { VendorsRow } from "./VendorsRow";

export interface VendorsColumns {
    RowNumber: Column<VendorsRow>;
    VendorName: Column<VendorsRow>;
    ShortName: Column<VendorsRow>;
    AddressedTo: Column<VendorsRow>;
    GSTIN: Column<VendorsRow>;
    PlaceOfSupplyStateName: Column<VendorsRow>;
    NatureOfSupply: Column<VendorsRow>;
    SupplyType: Column<VendorsRow>;
    PAN: Column<VendorsRow>;
    IECNo: Column<VendorsRow>;
    CINNo: Column<VendorsRow>;
    TAN: Column<VendorsRow>;
    UdyamNo: Column<VendorsRow>;
    BillingAddress: Column<VendorsRow>;
    BillingCityCityName: Column<VendorsRow>;
    BillingPinCode: Column<VendorsRow>;
    CorrespondenceAddress: Column<VendorsRow>;
    CorrespondenceCityCityName: Column<VendorsRow>;
    CorrespondencePinCode: Column<VendorsRow>;
    PhoneNo: Column<VendorsRow>;
    MobileNo: Column<VendorsRow>;
    FaxNo: Column<VendorsRow>;
    HomePage: Column<VendorsRow>;
    EMailId: Column<VendorsRow>;
    BankName: Column<VendorsRow>;
    BranchName: Column<VendorsRow>;
    AccountName: Column<VendorsRow>;
    AccountNumber: Column<VendorsRow>;
    IFSCCode: Column<VendorsRow>;
    BranchCode: Column<VendorsRow>;
    UploadFiles: Column<VendorsRow>;
    ClientName: Column<VendorsRow>;
    VendorId: Column<VendorsRow>;
}

export class VendorsColumns extends ColumnsBase<VendorsRow> {
    static readonly columnsKey = 'Default.Vendors';
    static readonly Fields = fieldsProxy<VendorsColumns>();
}