import { Decorators, LookupEditorBase, LookupEditorOptions } from "@serenity-is/corelib";
import { CommoditiesRow } from "../../ServerTypes/Default";

@Decorators.registerEditor('ReconnBooks.Default.CommodityCodeEditor')
export class CommodityCodeEditor extends LookupEditorBase<LookupEditorOptions, CommoditiesRow> {

    constructor(opt: LookupEditorOptions) {
        super(opt);
    }

    protected getLookupKey() {
        return CommoditiesRow.lookupKey;
        //return 'Main.Items';
    }

    protected getItemText(item, lookup) {
        return item.CommodityCode ? item.CommodityCode : "";
    }

    //TODO: consider cascaded items - currently filtering on all items


    protected cascadeItems(items) {
        var superItems = super.cascadeItems(items);
        return superItems.filter(x => x.CommodityCode);

    }
}
