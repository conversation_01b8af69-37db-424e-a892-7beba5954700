﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface DocumentsForm {
    DocumentName: StringEditor;
    DocumentShortName: StringEditor;
}

export class DocumentsForm extends PrefixedContext {
    static readonly formKey = 'Default.Documents';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DocumentsForm.init)  {
            DocumentsForm.init = true;

            var w0 = StringEditor;

            initFormType(DocumentsForm, [
                'DocumentName', w0,
                'DocumentShortName', w0
            ]);
        }
    }
}