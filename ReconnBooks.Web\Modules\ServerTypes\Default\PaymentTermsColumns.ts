﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { PaymentTermsRow } from "./PaymentTermsRow";

export interface PaymentTermsColumns {
    RowNumber: Column<PaymentTermsRow>;
    PaymentTerms: Column<PaymentTermsRow>;
    PaymentTermsId: Column<PaymentTermsRow>;
}

export class PaymentTermsColumns extends ColumnsBase<PaymentTermsRow> {
    static readonly columnsKey = 'Default.PaymentTerms';
    static readonly Fields = fieldsProxy<PaymentTermsColumns>();
}