﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface DepartmentsRow {
    RowNumber?: number;
    DepartmentId?: number;
    DepartmentName?: string;
    Description?: string;
}

export abstract class DepartmentsRow {
    static readonly idProperty = 'DepartmentId';
    static readonly nameProperty = 'DepartmentName';
    static readonly localTextPrefix = 'Default.Departments';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DepartmentsRow>();
}