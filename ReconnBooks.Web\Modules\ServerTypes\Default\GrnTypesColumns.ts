﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { GrnTypesRow } from "./GrnTypesRow";

export interface GrnTypesColumns {
    RowNumber: Column<GrnTypesRow>;
    GRNTypeName: Column<GrnTypesRow>;
    Description: Column<GrnTypesRow>;
    GRNTypeId: Column<GrnTypesRow>;
}

export class GrnTypesColumns extends ColumnsBase<GrnTypesRow> {
    static readonly columnsKey = 'Default.GrnTypes';
    static readonly Fields = fieldsProxy<GrnTypesColumns>();
}