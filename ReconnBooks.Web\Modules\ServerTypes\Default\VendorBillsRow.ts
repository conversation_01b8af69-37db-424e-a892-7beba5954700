﻿import { fieldsProxy } from "@serenity-is/corelib";
import { VendorBillDetailsRow } from "./VendorBillDetailsRow";

export interface VendorBillsRow {
    RowNumber?: number;
    VendorBillId?: number;
    VendorBillNo?: string;
    VendorBillDate?: string;
    VendorBillMonth?: string;
    VendorId?: number;
    GSTIN?: string;
    PlaceOfSupplyStateName?: string;
    SupplyTypeId?: number;
    SupplyType?: string;
    FinancialYearId?: number;
    PurchaseOrderId?: number;
    SupplyDueDate?: string;
    DeliveryDate?: string;
    VendorBillDetailsList?: VendorBillDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    VendorBillUpload?: string;
    ShippingThru?: string;
    ShippingDocketNo?: string;
    PaymentTermsId?: number;
    PaymentDueDate?: string;
    VendorBillAmount?: number;
    TDSRate?: number;
    TDSAmount?: number;
    TCSRate?: number;
    TCSAmount?: number;
    RoundingOff?: number;
    GrandTotal?: number;
    Remarks?: string;
    VendorBillStatus?: boolean;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    VendorName?: string;
    FinancialYearName?: string;
    PurchaseOrderNo?: string;
    PaymentTerms?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class VendorBillsRow {
    static readonly idProperty = 'VendorBillId';
    static readonly nameProperty = 'VendorBillNo';
    static readonly localTextPrefix = 'Default.VendorBills';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorBillsRow>();
}