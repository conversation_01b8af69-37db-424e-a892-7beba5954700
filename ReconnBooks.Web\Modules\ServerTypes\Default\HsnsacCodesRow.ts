﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface HsnsacCodesRow {
    RowNumber?: number;
    HSNSACCodeId?: number;
    HSNSACCode?: string;
    HSNSACGroup?: string;
    HSNSACDescription?: string;
    GSTRateId?: number;
    GSTRateRemarks?: string;
}

export abstract class HsnsacCodesRow {
    static readonly idProperty = 'HSNSACCodeId';
    static readonly nameProperty = 'HSNSACCode';
    static readonly localTextPrefix = 'Default.HsnsacCodes';
    static readonly lookupKey = 'Default.HsnsacCodes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<HsnsacCodesRow>('Default.HsnsacCodes') }
    static async getLookupAsync() { return getLookupAsync<HsnsacCodesRow>('Default.HsnsacCodes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<HsnsacCodesRow>();
}