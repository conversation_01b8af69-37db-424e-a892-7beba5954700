﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ConsultantBankAccountsRow } from "./ConsultantBankAccountsRow";

export interface ConsultantBankAccountsColumns {
    ConsultantBankAccountId: Column<ConsultantBankAccountsRow>;
    ConsultantId: Column<ConsultantBankAccountsRow>;
    AccountName: Column<ConsultantBankAccountsRow>;
    BankName: Column<ConsultantBankAccountsRow>;
    BranchName: Column<ConsultantBankAccountsRow>;
    AccountNumber: Column<ConsultantBankAccountsRow>;
    IFSCCode: Column<ConsultantBankAccountsRow>;
    BranchCode: Column<ConsultantBankAccountsRow>;
    SwiftCode: Column<ConsultantBankAccountsRow>;
    QRCode: Column<ConsultantBankAccountsRow>;
    Status: Column<ConsultantBankAccountsRow>;
}

export class ConsultantBankAccountsColumns extends ColumnsBase<ConsultantBankAccountsRow> {
    static readonly columnsKey = 'Default.ConsultantBankAccounts';
    static readonly Fields = fieldsProxy<ConsultantBankAccountsColumns>();
}