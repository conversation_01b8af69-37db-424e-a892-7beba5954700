﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { TitlesRow } from "./TitlesRow";

export interface TitlesColumns {
    RowNumber: Column<TitlesRow>;
    TitleId: Column<TitlesRow>;
    TitleOfRespect: Column<TitlesRow>;
    Gender: Column<TitlesRow>;
}

export class TitlesColumns extends ColumnsBase<TitlesRow> {
    static readonly columnsKey = 'Default.Titles';
    static readonly Fields = fieldsProxy<TitlesColumns>();
}