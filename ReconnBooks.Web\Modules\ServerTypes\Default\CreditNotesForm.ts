﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, DateTimeEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CreditNoteDetailsGridEditor } from "../../Default/CreditNoteDetails/CreditNoteDetailsGridEditor";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { InvoicesDialog } from "../../Default/Invoices/InvoicesDialog";
import { SalesReturnsDialog } from "../../Default/SalesReturns/SalesReturnsDialog";

export interface CreditNotesForm {
    CreditNoteNo: StringEditor;
    CreditNoteDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    PlaceOfSupplyStateName: StringEditor;
    InvoiceId: ServiceLookupEditor;
    SalesReturnId: ServiceLookupEditor;
    CreditNoteDetailsList: CreditNoteDetailsGridEditor;
    FinancialYearId: LookupEditor;
    CreditNoteAmount: DecimalEditor;
    Remarks: TextAreaEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    AuthorizedStatus: BooleanEditor;
}

export class CreditNotesForm extends PrefixedContext {
    static readonly formKey = 'Default.CreditNotes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CreditNotesForm.init)  {
            CreditNotesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = CreditNoteDetailsGridEditor;
            var w4 = LookupEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = DateTimeEditor;
            var w8 = BooleanEditor;

            initFormType(CreditNotesForm, [
                'CreditNoteNo', w0,
                'CreditNoteDate', w1,
                'CustomerId', w2,
                'PlaceOfSupplyStateName', w0,
                'InvoiceId', w2,
                'SalesReturnId', w2,
                'CreditNoteDetailsList', w3,
                'FinancialYearId', w4,
                'CreditNoteAmount', w5,
                'Remarks', w6,
                'PreparedByUserId', w4,
                'PreparedDate', w7,
                'VerifiedByUserId', w4,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w4,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w4,
                'ModifiedDate', w1,
                'CancelledByUserId', w4,
                'CancelledDate', w1,
                'AuthorizedStatus', w8
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, InvoicesDialog, SalesReturnsDialog]); // referenced dialogs