﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.BusinessTypesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.BusinessTypesRow;

namespace ReconnBooks.Default;

public interface IBusinessTypesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessTypesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessTypesSaveHandler
{
    public BusinessTypesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}