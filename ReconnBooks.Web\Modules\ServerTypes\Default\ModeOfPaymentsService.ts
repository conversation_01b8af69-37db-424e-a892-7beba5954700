﻿import { SaveRequest, SaveR<PERSON>ponse, ServiceOptions, DeleteRequest, DeleteR<PERSON>ponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ModeOfPaymentsRow } from "./ModeOfPaymentsRow";

export namespace ModeOfPaymentsService {
    export const baseUrl = 'Default/ModeOfPayments';

    export declare function Create(request: SaveRequest<ModeOfPaymentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ModeOfPaymentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ModeOfPaymentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ModeOfPaymentsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ModeOfPaymentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ModeOfPaymentsRow>>;

    export const Methods = {
        Create: "Default/ModeOfPayments/Create",
        Update: "Default/ModeOfPayments/Update",
        Delete: "Default/ModeOfPayments/Delete",
        Retrieve: "Default/ModeOfPayments/Retrieve",
        List: "Default/ModeOfPayments/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ModeOfPaymentsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}