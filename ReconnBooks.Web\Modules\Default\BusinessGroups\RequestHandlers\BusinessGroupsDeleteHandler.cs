﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.BusinessGroupsRow;

namespace ReconnBooks.Default;

public interface IBusinessGroupsDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessGroupsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessGroupsDeleteHandler
{
    public BusinessGroupsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}