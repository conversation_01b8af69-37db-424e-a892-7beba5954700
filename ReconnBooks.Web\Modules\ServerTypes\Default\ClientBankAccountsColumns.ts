﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ClientBankAccountsRow } from "./ClientBankAccountsRow";

export interface ClientBankAccountsColumns {
    ClientBankAccountId: Column<ClientBankAccountsRow>;
    ClientId: Column<ClientBankAccountsRow>;
    AccountName: Column<ClientBankAccountsRow>;
    BankName: Column<ClientBankAccountsRow>;
    BranchName: Column<ClientBankAccountsRow>;
    AccountNumber: Column<ClientBankAccountsRow>;
    IFSCCode: Column<ClientBankAccountsRow>;
    BranchCode: Column<ClientBankAccountsRow>;
    SwiftCode: Column<ClientBankAccountsRow>;
    QRCode: Column<ClientBankAccountsRow>;
    Status: Column<ClientBankAccountsRow>;
}

export class ClientBankAccountsColumns extends ColumnsBase<ClientBankAccountsRow> {
    static readonly columnsKey = 'Default.ClientBankAccounts';
    static readonly Fields = fieldsProxy<ClientBankAccountsColumns>();
}