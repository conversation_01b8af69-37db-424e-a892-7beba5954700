import { Authorization, Decorators, Dialog, EditorUtils, EntityDialog, EntityGrid, WidgetProps, cancelDialogButton, faIcon, getRemoteData, getRemoteDataAsync, gridPageInit, localText, notifySuccess, okDialogButton, resolveUrl, stringFormat, toId } from "@serenity-is/corelib";
import { addPasswordStrengthValidation } from "@serenity-is/extensions";
import { RoleRow, UserColumns, UserForm, UserPermissionService, UserRow, UserService } from "../../ServerTypes/Administration";
import { Texts } from "../../ServerTypes/Texts";
import { PermissionCheckEditor } from "../UserPermission/PermissionCheckEditor";
import { EmployeesRow, UserTypesRow } from "../../ServerTypes/Default";

export default () => gridPageInit(UserGrid)

class UserGrid<P = {}> extends EntityGrid<UserRow, P> {
    protected override getColumnsKey() { return UserColumns.columnsKey; }
    protected override getDialogType() { return UserDialog; }
    protected override getIdProperty() { return UserRow.idProperty; }
    protected override getIsActiveProperty() { return UserRow.isActiveProperty; }
    protected override getLocalTextPrefix() { return UserRow.localTextPrefix; }
    protected override getService() { return UserService.baseUrl; }

    protected override createIncludeDeletedButton() { }

    protected override getColumns() {
        let columns = new UserColumns(super.getColumns());

        columns.ImpersonationToken && (columns.ImpersonationToken.format = ctx => !ctx.value ? "" :
            <a target="_blank" href={resolveUrl(`~/Account/ImpersonateAs?token=${encodeURIComponent(ctx.value)}`)}>
                <i class={faIcon("user-secret", "primary")}></i>
            </a>);

        columns.Roles && (columns.Roles.format = ctx => !ctx?.value?.length ? "" : <span ref={async el => {
            let lookup = await RoleRow.getLookupAsync();
            let roleList = ctx.value.map((x: number) => (lookup.itemById[x] || {}).RoleName || "");
            roleList.sort();
            el.textContent = roleList.join(", ");
        }}><i class={faIcon("spinner")}></i></span>)

        return columns.valueOf();
    }
}

@Decorators.registerClass("ReconnBooks.Administration.UserDialog")
export class UserDialog<P = {}> extends EntityDialog<UserRow, P> {
    protected override getFormKey() { return UserForm.formKey; }
    protected override getIdProperty() { return UserRow.idProperty; }
    protected override getIsActiveProperty() { return UserRow.isActiveProperty; }
    protected override getLocalTextPrefix() { return UserRow.localTextPrefix; }
    protected override getNameProperty() { return UserRow.nameProperty; }
    protected override getService() { return UserService.baseUrl; }

    protected form = new UserForm(this.idPrefix);

    constructor(props: WidgetProps<P>) {
        super(props);

        this.form.EmployeeId.changeSelect2(async a => {
            var EmployeeId = toId(this.form.EmployeeId.value);
            if (EmployeeId != null) {
                var Employee = (await EmployeesRow.getLookupAsync()).itemById[EmployeeId];
                this.form.Email.value = Employee.EmailId;
                this.form.MobilePhoneNumber.value = Employee.PhoneNo;
            }
        })

        this.form.Password.domNode.setAttribute("autocomplete", "new-password");

        addPasswordStrengthValidation(this.form.Password, this.uniqueName);

        this.form.Password.change(() => EditorUtils.setRequired(this.form.PasswordConfirm, this.form.Password.value.length > 0));

        this.form.PasswordConfirm.addValidationRule(this.uniqueName, e => {
            if (this.form.Password.value != this.form.PasswordConfirm.value)
                return localText(Texts.Validation.PasswordConfirmMismatch);
        });
    }

    protected getToolbarButtons() {
        let buttons = super.getToolbarButtons();

        buttons = buttons.filter(x => x.cssClass !== "delete-button");
        buttons.push({
            title: localText(Texts.Site.UserDialog.EditPermissionsButton),
            cssClass: 'edit-permissions-button',
            disabled: this.isNewOrDeleted.bind(this),
            icon: faIcon("lock", "success"),
            onClick: () => {
                UserPermissionDialog({
                    userID: this.entity.UserId,
                    username: this.entity.Username
                });
            }
        });

        return buttons;
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();

        // these fields are only required in new record mode
        EditorUtils.setRequired(this.form.Password, this.isNew());
        EditorUtils.setRequired(this.form.PasswordConfirm, this.isNew());

        if (this.entity.ConsultantId) {
            this.form.ClientList.element.closest('.field').show();
        }
        else {
            this.form.ClientList.element.closest('.field').hide();
        }

        if (this.isEditMode()) {
            EditorUtils.setReadonly(this.form.UserTypeId.element.closest('input'), true);
            EditorUtils.setReadonly(this.form.EmployeeId.element.closest('input'), true);
            if (Authorization.hasPermission("Administration:Clients:Admin")) {
                this.form.ClientId.element.closest('.field').hide();
                this.form.ConsultantId.element.closest('.field').hide();

                //this.form.UserTypesId.changeSelect2(e => {
                var userTypesId = toId(this.form.UserTypeId.value)
                if (userTypesId != null) {
                    var userTypes = (await UserTypesRow.getLookupAsync()).itemById[userTypesId];
                    if (userTypes.UserTypeName == "Client") {

                        this.form.ClientId.element.closest('.field').show();
                        this.form.ConsultantId.element.closest('.field').hide();
                        this.form.EmployeeId.cascadeFrom = "ClientId";

                        EditorUtils.setReadonly(this.form.ClientId.element.closest('input'), true);
                    }
                    else if (userTypes.UserTypeName == "Consultant") {

                        this.form.ClientId.element.closest('.field').hide();
                        this.form.ConsultantId.element.closest('.field').show();
                        this.form.EmployeeId.cascadeFrom = "ConsultantId";
                        EditorUtils.setReadonly(this.form.ConsultantId.element.closest('input'), true);
                    }
                    else {
                        this.form.EmployeeId.value = null;
                        this.form.ClientId.element.closest('.field').hide();
                        this.form.ConsultantId.element.closest('.field').hide();
                    }
                }
                //});
            }
            else {
                this.form.UserTypeId.element.closest('.field').hide();
            }
        }
        else {
            if (Authorization.username == "admin") {

                this.form.ClientId.element.closest('.field').show();
                this.form.ConsultantId.element.closest('.field').show();
                this.form.UserTypeId.element.closest('.field').show();

                this.form.UserTypeId.changeSelect2(async e => {

                    this.form.EmployeeId.value = null;
                    this.form.ClientId.value = null;
                    this.form.ConsultantId.value = null;

                    //var usrTyp = this.form.UserTypeId.element.select2('data');

                    var userTypesId = toId(this.form.UserTypeId.value)
                    if (userTypesId != null) {
                        var userTypes = (await UserTypesRow.getLookupAsync()).itemById[userTypesId];
                        if (userTypes.UserTypeName == "Client") {
                            this.form.ClientId.element.closest('.field').show();
                            this.form.ConsultantId.element.closest('.field').hide();
                            this.form.EmployeeId.cascadeFrom = "ClientId";
                        }
                        else if (userTypes.UserTypeName == "Consultant") {

                            this.form.ClientId.element.closest('.field').hide();
                            this.form.ConsultantId.element.closest('.field').show();
                            this.form.EmployeeId.cascadeFrom = "ConsultantId";
                        }
                        else {
                            this.form.ClientId.element.closest('.field').hide();
                            this.form.ConsultantId.element.closest('.field').hide();
                        }
                    }
                });

                this.form.ClientId.changeSelect2(e => {
                    this.form.EmployeeId.value = '';
                    this.loadClientwiseemployees(toId(this.form.ClientId.value));
                });

                this.form.ConsultantId.changeSelect2(e => {
                    this.loadConsultantwiseemployees(toId(this.form.ConsultantId.value));
                });
            }
            else {
                //this.form.ClientId.element.closest('.field').hide();
                //this.form.ConsultantId.element.closest('.field').hide();
                //this.form.UserTypeId.element.closest('.field').hide();

                var userData = getRemoteData('UserData');
                var clientId = userData.ClientId;
                var consultantId = userData.ConsultantId;

                if (consultantId !== 0) {
                    this.loadConsultantwiseemployees(consultantId);
                }
                else if (clientId !== 0) {
                    this.loadClientwiseemployees(clientId);
                }

                //TODO: Hide UserType for non-superadmins
                this.form.UserTypeId.element.closest('.field').hide();
            }
        }
    }

    protected getPropertyItems() {
        var items = super.getPropertyItems();
        if (!Authorization.hasPermission("Administration:Clients:Admin")
               /* && Q.Authorization.hasPermission("Administration:ClientUsersAndEmployees")*/ && !Authorization.hasPermission("Administration:Clients:User")) {

            //items = items.filter(x => x.name != UserRow.Fields.UserTypesId);
            //items = items.filter(x => x.name != UserRow.Fields.ConsultantUserId);
            items = items.filter(x => x.name != UserRow.Fields.ClientId);
            items = items.filter(x => x.name != UserRow.Fields.ConsultantId);
            //items = items.filter(x => x.name != UserRow.Fields.UserClients);
            //items = items.filter(x => x.name != UserRow.Fields.ClientUserId);
        }
        else if (!Authorization.hasPermission("Administration:Clients:Admin")
            //&& Q.Authorization.hasPermission("Administration:ConsultantUsersAndEmployees")
            && Authorization.hasPermission("Administration:Clients:User")) {

            //items = items.filter(x => x.name != UserRow.Fields.UserTypesId);                               
            //items = items.filter(x => x.name != UserRow.Fields.ClientUserId);
            items = items.filter(x => x.name != UserRow.Fields.ConsultantId);
            items = items.filter(x => x.name != UserRow.Fields.ClientId);
            //items = items.filter(x => x.name != UserRow.Fields.ConsultantUserId);
        }
        return items;
    }

    loadClientwiseemployees(clientid: number) {
        this.form.EmployeeId.items = [];

        var allClientEmployees = EmployeesRow.getLookup().items.filter(u => u.ClientId == clientid);

        allClientEmployees.forEach(s => {
            var userEmployee = UserRow.getLookup().items
                .filter(u => (u.EmployeeId == s.EmployeeId));
            if (userEmployee.length == 0) {
                var newItem = {
                    disabled: false,
                    id: toId(s.EmployeeId),
                    text: s.EmployeeName,
                    source: {}
                };
                this.form.EmployeeId.items.push(newItem);
            }
        });
    }

    loadConsultantwiseemployees(consultantId: number) {
        this.form.EmployeeId.items = [];
        var allConsultantEmployees = EmployeesRow.getLookup().items.filter(u => u.ConsultantId == consultantId);
        allConsultantEmployees.forEach(s => {
            var userEmployee = UserRow.getLookup().items
                .filter(u => u.EmployeeId == s.EmployeeId);
            if (userEmployee.length == 0) {
                var newItem = {
                    disabled: false,
                    id: toId(s.EmployeeId),
                    text: s.EmployeeName,
                    source: {}
                };
                this.form.EmployeeId.items.push(newItem);
            }
        });
    }
}

async function UserPermissionDialog(props: { userID: number, username: string }) {

    let { Entities: permissions, RolePermissions: rolePermissions } = await UserPermissionService.List({
        UserID: props.userID,
        RolePermissions: true
    });

    let implicitPermissions = await getRemoteDataAsync('Administration.ImplicitPermissions');

    var checkEditor: PermissionCheckEditor;
    new Dialog({
        dialogClass: "s-UserPermissionDialog",
        title: stringFormat(Texts.Site.UserPermissionDialog.DialogTitle, props.username),
        buttons: [
            okDialogButton({
                click: async () => {
                    await UserPermissionService.Update({
                        UserID: props.userID,
                        Permissions: checkEditor.value
                    });
                    notifySuccess(Texts.Site.UserPermissionDialog.SaveSuccess);
                }
            }),
            cancelDialogButton()
        ],
        element: el => el.appendChild(<PermissionCheckEditor ref={w => checkEditor = w}
            showRevoke={true} value={permissions} implicitPermissions={implicitPermissions} rolePermissions={rolePermissions} />)
    });
}

