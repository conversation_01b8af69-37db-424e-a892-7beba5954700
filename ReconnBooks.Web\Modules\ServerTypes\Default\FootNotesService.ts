﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { FootNotesRow } from "./FootNotesRow";

export namespace FootNotesService {
    export const baseUrl = 'Default/FootNotes';

    export declare function Create(request: SaveRequest<FootNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<FootNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<FootNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<FootNotesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<FootNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<FootNotesRow>>;

    export const Methods = {
        Create: "Default/FootNotes/Create",
        Update: "Default/FootNotes/Update",
        Delete: "Default/FootNotes/Delete",
        Retrieve: "Default/FootNotes/Retrieve",
        List: "Default/FootNotes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>FootNotesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}