﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface BusinessGroupsRow {
    RowNumber?: number;
    BusinessGroupId?: number;
    BusinessGroup?: string;
    Description?: string;
}

export abstract class BusinessGroupsRow {
    static readonly idProperty = 'BusinessGroupId';
    static readonly nameProperty = 'BusinessGroup';
    static readonly localTextPrefix = 'Default.BusinessGroups';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<BusinessGroupsRow>();
}