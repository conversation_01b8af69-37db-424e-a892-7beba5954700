﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.BusinessCategoriesRow;

namespace ReconnBooks.Default;

public interface IBusinessCategoriesDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessCategoriesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessCategoriesDeleteHandler
{
    public BusinessCategoriesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}