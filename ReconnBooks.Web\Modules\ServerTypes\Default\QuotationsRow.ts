﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { QuotationDetailsRow } from "./QuotationDetailsRow";

export interface QuotationsRow {
    RowNumber?: number;
    QuotationId?: number;
    QuotationNo?: string;
    QuotationDate?: string;
    QuotationMonth?: string;
    DocumentId?: number;
    DocumentName?: string;
    CustomerId?: number;
    CustomerCompanyName?: string;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    CustomerEMailId?: string;
    PlaceOfSupplyStateName?: string;
    ReferenceNo?: string;
    ReferenceDate?: string;
    HeaderNoteId?: number;
    HeaderNote?: string;
    QuotationDetailsList?: QuotationDetailsRow[];
    NetTaxableAmount?: number;
    NetIGSTAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    GrandTotal?: number;
    PaymentTermsId?: number;
    PaymentTerms?: string;
    Taxes?: string;
    DeliveryPeriod?: string;
    Warranty?: string;
    Validity?: string;
    FootNoteId?: number;
    FootNote?: string;
    Remarks?: string;
    SalesPerson?: number;
    SalesPersonEmployeeName?: string;
    FinancialYearId?: number;
    FinancialYearName?: string;
    ClientId?: number;
    ClientName?: string;
    RoundingOff?: number;
    PreparedByUserId?: number;
    PreparedByUserUsername?: string;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedByUserUsername?: string;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedByUserUsername?: string;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedByUserUsername?: string;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledByUserUsername?: string;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    QuotationStatus?: boolean;
}

export abstract class QuotationsRow {
    static readonly idProperty = 'QuotationId';
    static readonly nameProperty = 'QuotationNo';
    static readonly localTextPrefix = 'Default.Quotations';
    static readonly lookupKey = 'Default.Quotations';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<QuotationsRow>('Default.Quotations') }
    static async getLookupAsync() { return getLookupAsync<QuotationsRow>('Default.Quotations') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<QuotationsRow>();
}