﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.AddressedToRow;

namespace ReconnBooks.Default;

public interface IAddressedToDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class AddressedToDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IAddressedToDeleteHandler
{
    public AddressedToDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}