using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.ClientUsers")]
[BasedOnRow(typeof(ClientUsersRow), CheckNames = true)]
public class ClientUsersColumns
{
    [DisplayName("Db.Shared.RecordId"), AlignRight]
    public int ClientUserId { get; set; }
    public string Username { get; set; }
    public string ConsultantName { get; set; }
    public string ClientName { get; set; }
    public bool Status { get; set; }
}