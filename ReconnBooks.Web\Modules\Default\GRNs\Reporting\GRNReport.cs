using Microsoft.Net.Http.Headers;
using ReconnBooks.Administration;
using ReconnBooks.Default;
using ReconnBooks.Modules.Common.Reporting;
using Serenity.Reporting;
using System.Net;
using Microsoft.Extensions.DependencyInjection;
using ReconnBooks.Default.Endpoints;
using ReconnBooks.Modules.Common.Helpers;

namespace ReconnBooks.Modules.Default.GRNs.Reporting;


[Report("GRNReport")]
[ReportDesign("~/Modules/Default/GRNs/Reporting/GRNReport.cshtml")]
//[RequiredPermission(PermissionKeys.Security)]

public class GRNsReport : ReportBase, IReport, ICustomizeHtmlToPdf
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;
    private readonly IServiceProvider serviceProvider;

    public GRNsReport(IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections, IServiceProvider serviceProvider)
    {
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
        this.serviceProvider = serviceProvider;
    }

    protected ISqlConnections SqlConnections => sqlConnections ?? throw new ArgumentNullException(nameof(sqlConnections));

    public object GetData()
    {
        var data = new GRNsReportData();

        using (var connection = SqlConnections.NewFor<GrNsRow>())
        {

            var grnsRetrieveHandler = serviceProvider.GetRequiredService<IGrNsRetrieveHandler>();
            data.GRNs = grnsRetrieveHandler.Retrieve(connection, new RetrieveRequest { EntityId = ID }).Entity;

            var vendorFields = VendorsRow.Fields;
            data.Vendor = connection.TryFirst<VendorsRow>(a =>
            {
                a.SelectTableFields();
                a.SelectNonTableFields();
                a.Select();
                a.Where(vendorFields.VendorId == data.GRNs.VendorId.GetValueOrDefault());
            }) ?? new VendorsRow();

            var clientFields = ClientsRow.Fields;
            data.Client = connection.TryFirst<ClientsRow>(a =>
            {
                a.SelectTableFields();
                a.SelectNonTableFields();
                a.Select();
                a.Where(clientFields.ClientId == data.GRNs.ClientId.GetValueOrDefault());
            }) ?? new ClientsRow();

            var purchaseOrderFields = PurchaseOrdersRow.Fields;
            data.PurchaseOrder = connection.TryFirst<PurchaseOrdersRow>(a =>
            {
                a.Select(purchaseOrderFields.PurchaseOrderDate);
                a.Where(purchaseOrderFields.PurchaseOrderNo == data.GRNs.PurchaseOrderNo);
            }) ?? null;
        }
        return data;
    }

    public void Customize(IHtmlToPdfOptions options)
    {
        // you may customize HTML to PDF converter (WKHTML) parameters here, e.g. 
        // options.MarginsAll = "2cm";
    }
}

public class GRNsReportData
{
    public GrNsRow GRNs { get; set; }
    public VendorsRow Vendor { get; set; }
    public ClientsRow Client { get; set; }
    public PurchaseOrdersRow PurchaseOrder { get; set; }
}








