﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CountriesRow } from "./CountriesRow";

export interface CountriesColumns {
    RowNumber: Column<CountriesRow>;
    CountryId: Column<CountriesRow>;
    CountryName: Column<CountriesRow>;
}

export class CountriesColumns extends ColumnsBase<CountriesRow> {
    static readonly columnsKey = 'Default.Countries';
    static readonly Fields = fieldsProxy<CountriesColumns>();
}