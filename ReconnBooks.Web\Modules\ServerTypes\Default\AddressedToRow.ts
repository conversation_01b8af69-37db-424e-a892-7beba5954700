﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface AddressedToRow {
    RowNumber?: number;
    AddressedToId?: number;
    AddressedTo?: string;
}

export abstract class AddressedToRow {
    static readonly idProperty = 'AddressedToId';
    static readonly nameProperty = 'AddressedTo';
    static readonly localTextPrefix = 'Default.AddressedTo';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<AddressedToRow>();
}