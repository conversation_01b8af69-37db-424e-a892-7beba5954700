import { UqCsColumns, UqCsRow, UqCsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { UqCsDialog } from './UqCsDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.UqCsGrid')
export class UqCsGrid extends EntityGridDialog<UqCsRow, any> {
    protected getColumnsKey() { return UqCsColumns.columnsKey; }
    protected getDialogType() { return UqCsDialog; }
    protected getRowDefinition() { return UqCsRow; }
    protected getService() { return UqCsService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}