using ReconnBooks.Modules.Default.UserTypes;
using Serenity.Navigation;
using Administration = ReconnBooks.Administration.Pages;

[assembly: NavigationMenu(9000, "Administration", icon: "fa-tools")]

[assembly: NavigationLink(9400, "Administration/Data Audit Log", typeof(ReconnBooks.Default.Pages.DataAuditLogPage), icon: "fa-ban")]
[assembly: NavigationLink(9400, "Administration/Exception Log", typeof(Administration.UserPage), icon: "fa-ban", action: "ExceptionLog", Target = "_blank")]
[assembly: NavigationLink(9500, "Administration/Languages", typeof(Administration.LanguagePage), icon: "fa-comments")]
[assembly: NavigationLink(9600, "Administration/Translations", typeof(Administration.TranslationPage), icon: "fa-comment-o")]
[assembly: NavigationLink(9700, "Administration/Roles", typeof(Administration.RolePage), icon: "fa-lock")]

[assembly: NavigationMenu(9800, "Administration/User Management", icon: "fa-tools")]
[assembly: NavigationLink(9805, "Administration/User Management/Employees", typeof(ReconnBooks.Default.Pages.EmployeesPage), icon: "fa-users")]
[assembly: NavigationLink(9810, "Administration/User Management/Users & Permissions", typeof(Administration.UserPage), icon: "fa-users")]



//[assembly: NavigationLink(8310, "Master Settings/Admin/Consultants",      typeof(ReconnBooks.Default.Pages.ConsultantsPage), icon: "null")]
//[assembly: NavigationLink(8320, "Master Settings/Admin/Clients",          typeof(ReconnBooks.Default.Pages.ClientsPage), icon: "null")]
//[assembly: NavigationLink(8330, "Master Settings/Admin/Employees",        typeof(ReconnBooks.Default.Pages.EmployeesPage), icon: "null")]

//[assembly: NavigationLink(9891, "Master Settings/User Management/Settings/Business Categories", typeof(ReconnBooks.Default.Pages.BusinessCategoriesPage),   icon: "null")]
//[assembly: NavigationLink(9892, "Master Settings/User Management/Settings/Business Groups",     typeof(ReconnBooks.Default.Pages.BusinessGroupsPage),       icon: "null")]
//[assembly: NavigationLink(9893, "Master Settings/User Management/Settings/Business Types",      typeof(ReconnBooks.Default.Pages.BusinessTypesPage),        icon: "null")]

//[assembly: NavigationLink(8340, "Master Settings/Admin/Client Users",            typeof(ReconnBooks.Default.Pages.ClientUsersPage), icon: "null")]
//[assembly: NavigationLink(9830, "Administration/User Management/Consultant Users",  typeof(ReconnBooks.Default.Pages.ConsultantUsersPage), icon: "null")]