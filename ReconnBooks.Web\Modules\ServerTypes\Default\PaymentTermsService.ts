﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { PaymentTermsRow } from "./PaymentTermsRow";

export namespace PaymentTermsService {
    export const baseUrl = 'Default/PaymentTerms';

    export declare function Create(request: SaveRequest<PaymentTermsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PaymentTermsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PaymentTermsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PaymentTermsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PaymentTermsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PaymentTermsRow>>;

    export const Methods = {
        Create: "Default/PaymentTerms/Create",
        Update: "Default/PaymentTerms/Update",
        Delete: "Default/PaymentTerms/Delete",
        Retrieve: "Default/PaymentTerms/Retrieve",
        List: "Default/PaymentTerms/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>PaymentTermsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}