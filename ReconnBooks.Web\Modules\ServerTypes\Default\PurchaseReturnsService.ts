﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { PurchaseReturnDetailsRow } from "./PurchaseReturnDetailsRow";
import { PurchaseReturnsRow } from "./PurchaseReturnsRow";

export namespace PurchaseReturnsService {
    export const baseUrl = 'Default/PurchaseReturns';

    export declare function Create(request: SaveRequest<PurchaseReturnsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PurchaseReturnsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PurchaseReturnsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PurchaseReturnsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PurchaseReturnsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PurchaseReturnsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromGRNDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<PurchaseReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PurchaseReturnDetailsRow>>;

    export const Methods = {
        Create: "Default/PurchaseReturns/Create",
        Update: "Default/PurchaseReturns/Update",
        Delete: "Default/PurchaseReturns/Delete",
        Retrieve: "Default/PurchaseReturns/Retrieve",
        List: "Default/PurchaseReturns/List",
        GetNextNumber: "Default/PurchaseReturns/GetNextNumber",
        GetFromGRNDetails: "Default/PurchaseReturns/GetFromGRNDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromGRNDetails'
    ].forEach(x => {
        (<any>PurchaseReturnsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}