﻿CREATE TABLE [dbo].[PurchaseOrderDetails] 
(
    [PurchaseOrderDetailId] INT                 NOT NULL    IDENTITY (1, 1),
    [PurchaseOrderId]       INT                 NOT NULL,
    [CommodityTypeId]       INT                 NOT NULL,
    [CommodityId]           BIGINT              NOT NULL,
    [CommodityDescription]  NVARCHAR (MAX)          NULL,

    [Quantity]              DECIMAL (18, 2)     NOT NULL    CONSTRAINT [DF_PurchaseOrderDetails_Quantity] DEFAULT ((1)),
    [UnitId]                INT                 NOT NULL,
    [UnitPrice]             DECIMAL (18, 2)     NOT NULL    CONSTRAINT [DF_PurchaseOrderDetails_UnitPrice]          DEFAULT ((0)),
    [AmendedQuantity]       DECIMAL (18, 2)         NULL    CONSTRAINT [DF_PurchaseOrderDetails_AmendedQuantity]    DEFAULT ((1)),
    [AmendedUnitId]         INT                     NULL,
    [AmendedPrice]          DECIMAL (18, 2)         NULL    CONSTRAINT [DF_PurchaseOrderDetails_AmendedPrice]       DEFAULT ((0)), 
    [DiscountPercent]       DECIMAL (18, 2) DEFAULT ((0)) NULL,
    [DiscountAmountPerUnit] DECIMAL (18, 2) CONSTRAINT [DF_PurchaseOrderDetails_Disc.AmtPerUnit] DEFAULT ((0)) NULL,
    [NetDiscountAmount]     DECIMAL (18, 2) CONSTRAINT [DF_PurchaseOrderDetails_NetDiscountAmt] DEFAULT ((0)) NULL,
    [PendingQuantity]       DECIMAL (18, 2)     NOT NULL    CONSTRAINT [DF_PurchaseOrderDetails_PendingQuantity]    DEFAULT ((1)),
	
    [GSTRateId]             INT                 NOT NULL,
    [IGSTRate]              DECIMAL (18, 2)         NULL    CONSTRAINT [DF_PurchaseOrderDetails_IGSTRate] DEFAULT ((0)),
    [CGSTRate]              DECIMAL (18, 2)         NULL    CONSTRAINT [DF_PurchaseOrderDetails_CGSTRate] DEFAULT ((0)),
    [SGSTRate]              DECIMAL (18, 2)         NULL    CONSTRAINT [DF_PurchaseOrderDetails_SGSTRate] DEFAULT ((0)),

    [DummyField]            NVARCHAR (200)          NULL,
    [NetPricePerUnit]       DECIMAL (18, 2)     NOT NULL,
    [NetAmount]             DECIMAL (18, 2)     NOT NULL,

    CONSTRAINT [PK_PurchaseOrderDetails] PRIMARY KEY CLUSTERED ([PurchaseOrderDetailId] ASC),
    CONSTRAINT [FK_PurchaseOrderDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_PurchaseOrderDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_PurchaseOrderDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_PurchaseOrderDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);

GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[PurchaseOrderDetails]([PurchaseOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[PurchaseOrderDetails]([CommodityId] ASC);
