﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CustomerDetailsRow } from "./CustomerDetailsRow";

export namespace CustomerDetailsService {
    export const baseUrl = 'Default/CustomerDetails';

    export declare function Create(request: SaveRequest<CustomerDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CustomerDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CustomerDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CustomerDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CustomerDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CustomerDetailsRow>>;

    export const Methods = {
        Create: "Default/CustomerDetails/Create",
        Update: "Default/CustomerDetails/Update",
        Delete: "Default/CustomerDetails/Delete",
        Retrieve: "Default/CustomerDetails/Retrieve",
        List: "Default/CustomerDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CustomerDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}