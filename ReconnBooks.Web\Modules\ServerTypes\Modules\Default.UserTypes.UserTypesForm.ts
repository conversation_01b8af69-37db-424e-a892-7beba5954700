﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface UserTypesForm {
    UserTypeName: StringEditor;
    Description: StringEditor;
}

export class UserTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.UserTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!UserTypesForm.init)  {
            UserTypesForm.init = true;

            var w0 = StringEditor;

            initFormType(UserTypesForm, [
                'UserTypeName', w0,
                'Description', w0
            ]);
        }
    }
}