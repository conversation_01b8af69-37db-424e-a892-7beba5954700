﻿CREATE TABLE [dbo].[DeliveryNotes]
(
    [DeliveryNoteId]        INT             NOT NULL    IDENTITY (1, 1),
    [DeliveryNoteNo]        NVARCHAR (50)   NOT NULL,
    [DeliveryNoteDate]      DATETIME            NULL,
    [CustomerId]            INT             NOT NULL,

    [SupplyTypeId]          INT                 NULL,--     Nature of Supply section
    [FinancialYearId]       INT                 NULL,
    [SalesOrderId]          INT                 NULL,    
    [QuotationId]           INT                 NULL,
    [OrderRefNo]            NVARCHAR (50)       NULL,
    [OrderRefDate]          SMALLDATETIME       NULL,
    [ReasonToTransport]     NVARCHAR (500) NULL,
    [eWayBillNo]            NVARCHAR (50)       NULL,
    [eWayBillNoDate]        SMALLDATETIME       NULL,

    [DeliveryAddress]       NVARCHAR (250)      NULL,
    [DeliveryCityId]        INT                 NULL,
    [DeliveryPinCode]       INT                 NULL,
    [ShippedVia]            NVARCHAR (250)      NULL,
    [ShippingDocketNo]      NVARCHAR (50)       NULL,
    [VehicleNo]             NVARCHAR (20)       NULL,
    [UploadDocketCopy]      NVARCHAR (250)       NULL,
    [Inspection]            NVARCHAR (100)      NULL,
    [Remarks]               NVARCHAR (MAX)      NULL,
     -------------------Authorization Details-------------
    [ClientId]              INT	            NOT NULL    CONSTRAINT [DF_DeliveryNotes_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	                NULL,
    [PreparedDate]          DATETIME            NULL,
    [VerifiedByUserId]      INT                 NULL,
    [VerifiedDate]          DATETIME            NULL,
    [AuthorizedByUserId]    INT                 NULL,
    [AuthorizedDate]        DATETIME            NULL,
    [ModifiedByUserId]      INT                 NULL,
    [ModifiedDate]          DATETIME            NULL,
    [CancelledByUserId]     INT                 NULL,
    [CancelledDate]			DATETIME            NULL,
    [AuthorizedStatus]      BIT             NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details-------------
    CONSTRAINT [FK_DeliveryNotes_PreparedByUsers]   FOREIGN KEY ([PreparedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryNotes_VerfiedByUsers]    FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryNotes_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId])  REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryNotes_ModifiedByUsers]   FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryNotes_CancelledByUsers]  FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryNotes_Clients]	        FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients]([ClientId]),
    -------------------Authorization Details End-------------
    
    CONSTRAINT [PK_DeliveryNotes]  PRIMARY KEY      CLUSTERED   ([DeliveryNoteId] ASC),
    CONSTRAINT [FK_DeliveryNotes_Customers]		    FOREIGN KEY ([CustomerId])		    REFERENCES [dbo].[Customers]		([CustomerId]),
    CONSTRAINT [FK_DeliveryNotes_Quotations]		FOREIGN KEY ([QuotationId])		    REFERENCES [dbo].[Quotations]		([QuotationId]),
    CONSTRAINT [FK_DeliveryNotes_SalesOrders]	    FOREIGN KEY ([SalesOrderId])	    REFERENCES [dbo].[SalesOrders]	    ([SalesOrderId]),
    CONSTRAINT [FK_DeliveryNotes_SupplyTypes]       FOREIGN KEY ([SupplyTypeId])	    REFERENCES [dbo].[SupplyTypes]      ([SupplyTypeId]),
    CONSTRAINT [FK_DeliveryNotes_FinancialYears]    FOREIGN KEY ([FinancialYearId])	    REFERENCES [dbo].[FinancialYears]	([FinancialYearId]),
    CONSTRAINT [FK_DeliveryNotes_DeliveryCities]    FOREIGN KEY ([DeliveryCityId])      REFERENCES [dbo].[Cities]           ([CityId])
);

GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[DeliveryNotes]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [Quotations]
    ON [dbo].[DeliveryNotes]([QuotationId] ASC);
    GO
CREATE NONCLUSTERED INDEX [SalesOrders]
    ON [dbo].[DeliveryNotes]([SalesOrderId] ASC);
GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[DeliveryNotes]([FinancialYearId] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[DeliveryNotes]([ClientId] ASC);

