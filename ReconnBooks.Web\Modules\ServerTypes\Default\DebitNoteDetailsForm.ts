﻿import { ServiceLookupEditor, TextAreaEditor, DecimalEditor, LookupEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";

export interface DebitNoteDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    PoQuantity: DecimalEditor;
    ReturnedQuantity: DecimalEditor;
    UnitPrice: DecimalEditor;
    PoUnitId: LookupEditor;
    ReturnedUnitId: LookupEditor;
    UnitAmount: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmountPerUnit: DecimalEditor;
    NetDiscountAmount: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    TaxableAmountPerUnit: DecimalEditor;
    GSTRateId: LookupEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: DecimalEditor;
    NetPricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
    RejectionReasonId: ServiceLookupEditor;
    AssessmentRemarks: StringEditor;
    ReplacementMethodId: ServiceLookupEditor;
    Remarks: StringEditor;
    PurchaseOrderDetailId: LookupEditor;
    PurchaseReturnDetailId: LookupEditor;
}

export class DebitNoteDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.DebitNoteDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DebitNoteDetailsForm.init)  {
            DebitNoteDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = DecimalEditor;
            var w4 = LookupEditor;
            var w5 = StringEditor;

            initFormType(DebitNoteDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'PoQuantity', w3,
                'ReturnedQuantity', w3,
                'UnitPrice', w3,
                'PoUnitId', w4,
                'ReturnedUnitId', w4,
                'UnitAmount', w3,
                'DiscountPercent', w3,
                'DiscountAmountPerUnit', w3,
                'NetDiscountAmount', w3,
                'NetTaxableAmount', w3,
                'TaxableAmountPerUnit', w3,
                'GSTRateId', w4,
                'IGSTRate', w3,
                'IGSTAmountPerUnit', w3,
                'NetIGSTAmount', w3,
                'CGSTRate', w3,
                'CGSTAmountPerUnit', w3,
                'NetCGSTAmount', w3,
                'SGSTRate', w3,
                'SGSTAmountPerUnit', w3,
                'NetSGSTAmount', w3,
                'DummyField', w3,
                'NetPricePerUnit', w3,
                'NetAmount', w3,
                'RejectionReasonId', w0,
                'AssessmentRemarks', w5,
                'ReplacementMethodId', w0,
                'Remarks', w5,
                'PurchaseOrderDetailId', w4,
                'PurchaseReturnDetailId', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog]); // referenced dialogs