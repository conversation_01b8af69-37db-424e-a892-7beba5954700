﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface BusinessTypesForm {
    BusinessType: StringEditor;
    Description: StringEditor;
}

export class BusinessTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.BusinessTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!BusinessTypesForm.init)  {
            BusinessTypesForm.init = true;

            var w0 = StringEditor;

            initFormType(BusinessTypesForm, [
                'BusinessType', w0,
                'Description', w0
            ]);
        }
    }
}