﻿CREATE TABLE [dbo].[OpeningBalances] (
    [OpeningBalanceId] INT             IDENTITY (1, 1) NOT NULL,
    [FromDate]         DATETIME        NOT NULL,
    [ToDate]           DATETIME        NULL,
    [Ammount]          DECIMAL (18, 2) NOT NULL,
    [LedgerAccountId]  INT             NOT NULL,
    [FinancialYearId]  INT             NOT NULL,
    [Remarks]          NVARCHAR (500)  NULL,
    CONSTRAINT [PK_OpeningBalances] PRIMARY KEY CLUSTERED ([OpeningBalanceId] ASC),
    CONSTRAINT [FK_OpeningBalances_LedgerAccounts] FOREIGN KEY ([LedgerAccountId]) REFERENCES [dbo].[LedgerAccounts] ([LedgerAccountId]),
    CONSTRAINT [FK_OpeningBalances_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId])
);


GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[OpeningBalances]([FinancialYearId] ASC);

