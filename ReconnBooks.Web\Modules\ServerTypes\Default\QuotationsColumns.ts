﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { QuotationsRow } from "./QuotationsRow";

export interface QuotationsColumns {
    RowNumber: Column<QuotationsRow>;
    QuotationNo: Column<QuotationsRow>;
    QuotationDate: Column<QuotationsRow>;
    DocumentName: Column<QuotationsRow>;
    CustomerCompanyName: Column<QuotationsRow>;
    ReferenceNo: Column<QuotationsRow>;
    ReferenceDate: Column<QuotationsRow>;
    NetTaxableAmount: Column<QuotationsRow>;
    NetCGSTAmount: Column<QuotationsRow>;
    NetSGSTAmount: Column<QuotationsRow>;
    NetIGSTAmount: Column<QuotationsRow>;
    GrandTotal: Column<QuotationsRow>;
    HeaderNote: Column<QuotationsRow>;
    PaymentTerms: Column<QuotationsRow>;
    Taxes: Column<QuotationsRow>;
    DeliveryPeriod: Column<QuotationsRow>;
    Warranty: Column<QuotationsRow>;
    Validity: Column<QuotationsRow>;
    FootNote: Column<QuotationsRow>;
    Remarks: Column<QuotationsRow>;
    SalesPersonEmployeeName: Column<QuotationsRow>;
    FinancialYearName: Column<QuotationsRow>;
    QuotationMonth: Column<QuotationsRow>;
    ClientName: Column<QuotationsRow>;
    PreparedByUserUsername: Column<QuotationsRow>;
    PreparedDate: Column<QuotationsRow>;
    VerifiedByUserUsername: Column<QuotationsRow>;
    VerifiedDate: Column<QuotationsRow>;
    AuthorizedByUserUsername: Column<QuotationsRow>;
    AuthorizedDate: Column<QuotationsRow>;
    ModifiedByUserUsername: Column<QuotationsRow>;
    ModifiedDate: Column<QuotationsRow>;
    CancelledByUserUsername: Column<QuotationsRow>;
    CancelledDate: Column<QuotationsRow>;
    AuthorizedStatus: Column<QuotationsRow>;
    QuotationStatus: Column<QuotationsRow>;
    QuotationId: Column<QuotationsRow>;
}

export class QuotationsColumns extends ColumnsBase<QuotationsRow> {
    static readonly columnsKey = 'Default.Quotations';
    static readonly Fields = fieldsProxy<QuotationsColumns>();
}

[IndianNumberFormatter]; // referenced types