﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface CitiesRow {
    RowNumber?: number;
    CityId?: number;
    CityName?: string;
    PINCode?: number;
    DistrictId?: number;
    StateId?: number;
    District?: string;
    StateName?: string;
}

export abstract class CitiesRow {
    static readonly idProperty = 'CityId';
    static readonly nameProperty = 'CityName';
    static readonly localTextPrefix = 'Default.Cities';
    static readonly lookupKey = 'Default.Cities';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CitiesRow>('Default.Cities') }
    static async getLookupAsync() { return getLookupAsync<CitiesRow>('Default.Cities') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CitiesRow>();
}