﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { ClientBankAccountsRow } from "./ClientBankAccountsRow";

export interface ClientsRow {
    RowNumber?: number;
    ClientId?: number;
    ClientName?: string;
    ClientCode?: string;
    ConsultantId?: number;
    Address?: string;
    Address2?: string;
    CityId?: number;
    PINCode?: string;
    PhoneNo?: string;
    FaxNo?: string;
    HomePage?: string;
    Logo?: string;
    TitleId?: number;
    ClientContactName?: string;
    DesignationId?: number;
    MobileNo?: string;
    AlternateNo?: string;
    EMail?: string;
    GSTIN?: string;
    PlaceOfSupplyId?: number;
    NatureOfSupplyId?: number;
    SupplyTypeId?: number;
    PAN?: string;
    IECNo?: string;
    CINNo?: string;
    TANNo?: string;
    ClientBankAccountsList?: ClientBankAccountsRow[];
    TagLine?: string;
    ClientDSC?: string;
    BusinessTypeId?: number;
    BusinessGroupId?: number;
    BusinessCategoryId?: number;
    InvoiceNoFormat?: string;
    Disclaimer?: string;
    UdyamNo?: string;
    CityName?: string;
    TitleOfRespect?: string;
    Designation?: string;
    PlaceOfSupplyStateName?: string;
    PlaceOfSupplyStateCode?: string;
    PlaceOfSupplyStateCodeNo?: string;
    NatureOfSupply?: string;
    SupplyType?: string;
    BusinessType?: string;
    BusinessGroup?: string;
    BusinessCategory?: string;
    ConsultantName?: string;
    EmailServerHost?: string;
    EmailServerUsername?: string;
    EmailServerPassword?: string;
    EmailServerPasswordEncrypted?: string;
    EmailServerPort?: number;
}

export abstract class ClientsRow {
    static readonly idProperty = 'ClientId';
    static readonly nameProperty = 'ClientName';
    static readonly localTextPrefix = 'Default.Clients';
    static readonly lookupKey = 'Default.Clients';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ClientsRow>('Default.Clients') }
    static async getLookupAsync() { return getLookupAsync<ClientsRow>('Default.Clients') }

    static readonly deletePermission = 'Administration:Clients:Admin';
    static readonly insertPermission = 'Administration:Clients:Admin';
    static readonly readPermission = 'Administration:Clients:User';
    static readonly updatePermission = 'Administration:Clients:Admin';

    static readonly Fields = fieldsProxy<ClientsRow>();
}