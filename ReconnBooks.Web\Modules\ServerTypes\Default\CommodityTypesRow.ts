﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface CommodityTypesRow {
    RowNumber?: number;
    CommodityTypeId?: number;
    CommodityType?: string;
    SetDefault?: boolean;
}

export abstract class CommodityTypesRow {
    static readonly idProperty = 'CommodityTypeId';
    static readonly nameProperty = 'CommodityType';
    static readonly localTextPrefix = 'Default.CommodityTypes';
    static readonly lookupKey = 'Default.CommodityTypes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CommodityTypesRow>('Default.CommodityTypes') }
    static async getLookupAsync() { return getLookupAsync<CommodityTypesRow>('Default.CommodityTypes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CommodityTypesRow>();
}