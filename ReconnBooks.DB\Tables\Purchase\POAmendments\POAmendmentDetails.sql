﻿CREATE TABLE [dbo].[POAmendmentDetails] (
    [POAmendmentDetailId]  INT             IDENTITY (1, 1) NOT NULL,
    [POAmendmentId]        INT             NOT NULL,
    [CommodityTypeId]      INT             NOT NULL,
    [CommodityId]          BIGINT          NOT NULL,
    [CommodityDescription] NVARCHAR (MAX)  NULL,
    
    [POQuantity]           DECIMAL (18, 2) NOT NULL,
    [POUnitId]             INT             NOT NULL,
    [POUnitPrice]          DECIMAL (18, 2) NOT NULL,
    [PendingQuantity]      DECIMAL (18, 2) NULL,
    
    [AmendedUnitPrice]     DECIMAL (18, 2) NOT NULL,
    [AmendedUnitAmount]    DECIMAL (18, 2) NULL,
    [AmendedQuantity]      DECIMAL (18, 2) NULL,
    [AmendedUnitId]        INT             NULL,
    
    [GSTRateId]            INT             NOT NULL,
    [IGSTRate]             DECIMAL (18, 2) NULL,
    [CGSTRate]             DECIMAL (18, 2) NULL,
    [SGSTRate]             DECIMAL (18, 2) NULL,
    
    [Du<PERSON><PERSON><PERSON>]           NVARCHAR (200)  NULL,
    [NetPricePerUnit]      DECIMAL (18, 2) NOT NULL,
    [NetAmount]            DECIMAL (18, 2) NOT NULL,

    CONSTRAINT [PK_POAmendmentDetails] PRIMARY KEY CLUSTERED ([POAmendmentDetailId] ASC),
    CONSTRAINT [FK_POAmendmentDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_POAmendmentDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_POAmendmentDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_POAmendmentDetails_POs] FOREIGN KEY ([POAmendmentId]) REFERENCES [dbo].[POAmendments] ([POAmendmentId]),
    CONSTRAINT [FK_POAmendmentDetails_AmendedUnit] FOREIGN KEY ([AmendedUnitId]) REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_POAmendmentDetails_POUnit] FOREIGN KEY ([POUnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [POAmendments]
    ON [dbo].[POAmendmentDetails]([POAmendmentId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[POAmendmentDetails]([CommodityId] ASC);

