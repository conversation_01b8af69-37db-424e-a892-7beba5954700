using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("Cities")]
[DisplayName("Cities"), InstanceName("Cities"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
[UniqueConstraint(new[] { "CityName", "PINCode", "DistrictId", "StateName" })]
public sealed partial class CitiesRow : Row<CitiesRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    const string jDistrict = nameof(jDistrict);
    const string jState = nameof(jState);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("City Id"), Identity, IdProperty, Hidden]
    public int? CityId { get => fields.CityId[this]; set => fields.CityId[this] = value; }

    [DisplayName("City Name"), Size(50), NotNull, QuickSearch, NameProperty]
    public string CityName { get => fields.CityName[this]; set => fields.CityName[this] = value; }

    [DisplayName("Pin Code"), Column("PINCode"), LookupInclude]
    public int? PINCode { get => fields.PINCode[this]; set => fields.PINCode[this] = value; }

    [DisplayName("District"), NotNull, ForeignKey(typeof(DistrictsRow)), LeftJoin(jDistrict), TextualField(nameof(District))]
    [ServiceLookupEditor(typeof(DistrictsRow), InplaceAdd = (true),Service = "Default/Districts/List")]
    public int? DistrictId { get => fields.DistrictId[this]; set => fields.DistrictId[this] = value; }

    [DisplayName("State"), NotNull, ForeignKey(typeof(StatesRow)), LeftJoin(jState), TextualField(nameof(StateName))]
    [ServiceLookupEditor(typeof(StatesRow), InplaceAdd = (true), Service = "Default/States/List")]
    public int? StateId { get => fields.StateId[this]; set => fields.StateId[this] = value; }

    [DisplayName("District"), Origin(jDistrict, nameof(DistrictsRow.District))]
    public string District { get => fields.District[this]; set => fields.District[this] = value; }

    [DisplayName("State State Name"), Origin(jState, nameof(StatesRow.StateName))]
    public string StateName { get => fields.StateName[this]; set => fields.StateName[this] = value; }
}