import { ClientUsersColumns, ClientUsersRow, ClientUsersService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, gridPageInit } from '@serenity-is/corelib';


@Decorators.registerClass('ReconnBooks.Default.ClientUsersGrid')
export class ClientUsersGrid extends EntityGrid<ClientUsersRow> {
    protected getColumnsKey() { return ClientUsersColumns.columnsKey; }
    protected getRowDefinition() { return ClientUsersRow; }
    protected getService() { return ClientUsersService.baseUrl; }

   
    protected getButtons() {
        return [];
    }
}