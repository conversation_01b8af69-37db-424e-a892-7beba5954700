﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface DeliveryNoteDetailsRow {
    RowNumber?: number;
    DeliveryNoteDetailId?: number;
    DeliveryNoteId?: number;
    DeliveryNoteNo?: string;
    CommodityTypeId?: number;
    CommodityType?: string;
    CommodityId?: number;
    CommodityName?: string;
    CommodityCode?: string;
    CommodityDescription?: string;
    Sku?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    Quantity?: number;
    UnitId?: number;
    UnitName?: string;
    DummyField1?: string;
    UnitPrice?: number;
    UnitAmount?: number;
    GSTRateId?: number;
    GSTRateRemarks?: string;
    IGSTRate?: number;
    PerUnitIGSTAmount?: number;
    IGSTAmount?: number;
    CGSTRate?: number;
    PerUnitCGSTAmount?: number;
    CGSTAmount?: number;
    SGSTRate?: number;
    PerUnitSGSTAmount?: number;
    SGSTAmount?: number;
    DummyField?: string;
    PerUnitPrice?: number;
    NetAmount?: number;
}

export abstract class DeliveryNoteDetailsRow {
    static readonly idProperty = 'DeliveryNoteDetailId';
    static readonly nameProperty = 'DeliveryNoteDetailId';
    static readonly localTextPrefix = 'Default.DeliveryNoteDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DeliveryNoteDetailsRow>();
}