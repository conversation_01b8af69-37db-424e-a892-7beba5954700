namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON>ame("HSNSummary")]
[DisplayName("HSN Summary Details"), InstanceName("HSN Summary Details"), GenerateFields]
[Read<PERSON>ermission("Administration:General")]
[<PERSON><PERSON><PERSON><PERSON>er<PERSON>("Administration:General")]
[ServiceLookupPermission("Administration:General")]

public sealed partial class HsnSummaryDetailsRow : Row<HsnSummaryDetailsRow.RowFields>, IIdRow, INameRow

{
    [DisplayName("HSN Summary Detail Id"), Identity, IdProperty, NameProperty]
    public int? HsnSummaryDetailId { get => fields.HsnSummaryDetailId[this]; set => fields.HsnSummaryDetailId[this] = value; }

    [DisplayName(""), Width(30), AlignCenter]
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.Row<PERSON><PERSON>ber[this] = value; }     //Serial Numbering

    public string InvoiceNo { get => fields.InvoiceNo[this]; set => fields.InvoiceNo[this] = value; }

    public DateTime? InvoiceDate { get => fields.InvoiceDate[this]; set => fields.InvoiceDate[this] = value; }

    [DisplayName("HSN/SAC Code"), Width(90)]
    public string HSNSACCode { get => fields.HSNSACCode[this]; set => fields.HSNSACCode[this] = value; }

    [DisplayName("Qty."), Width(60), AlignCenter]
    public decimal? Quantity { get => fields.Quantity[this] ; set => fields.Quantity[this] = value; }

    [DisplayName("Net Taxable Amt."), Width(100)]
    public decimal? NetTaxableAmount { get => fields.NetTaxableAmount[this]; set => fields.NetTaxableAmount[this] = value; }

    [DisplayName("IGST Rate(%)"), Width(90)]
    public decimal? IGSTRate { get => fields.IGSTRate[this]; set => fields.IGSTRate[this] = value; }

    [DisplayName("Net IGST"), Width(90)]
    public decimal? NetIGSTAmount { get => fields.NetIGSTAmount[this]; set => fields.NetIGSTAmount[this] = value; }

    [DisplayName("CGST Rate(%)"), Width(90)]
    public decimal? CGSTRate { get => fields.CGSTRate[this]; set => fields.CGSTRate[this] = value; }

    [DisplayName("Net CGST"), Width(90)]
    public decimal? NetCGSTAmount { get => fields.NetCGSTAmount[this]; set => fields.NetCGSTAmount[this] = value; }

    [DisplayName("SGST Rate(%)"), Width(90)]
    public decimal? SGSTRate { get => fields.SGSTRate[this]; set => fields.SGSTRate[this] = value; }

    [DisplayName("Net SGST"), Width(90)]
    public decimal? NetSGSTAmount { get => fields.NetSGSTAmount[this]; set => fields.NetSGSTAmount[this] = value; }

    [DisplayName("Net Amount"), Width(100)]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }

}
