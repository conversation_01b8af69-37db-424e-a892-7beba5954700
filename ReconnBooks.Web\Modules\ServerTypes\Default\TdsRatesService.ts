﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { TdsRatesRow } from "./TdsRatesRow";

export namespace TdsRatesService {
    export const baseUrl = 'Default/TdsRates';

    export declare function Create(request: SaveRequest<TdsRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<TdsRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<TdsRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<TdsRatesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<TdsRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<TdsRatesRow>>;

    export const Methods = {
        Create: "Default/TdsRates/Create",
        Update: "Default/TdsRates/Update",
        Delete: "Default/TdsRates/Delete",
        Retrieve: "Default/TdsRates/Retrieve",
        List: "Default/TdsRates/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>TdsRatesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}