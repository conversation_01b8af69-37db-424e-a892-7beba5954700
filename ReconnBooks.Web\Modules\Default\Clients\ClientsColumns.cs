using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.Clients")]
[BasedOnRow(typeof(ClientsRow), CheckNames = true)]
public class ClientsColumns
{
    //--------- To Add serial Nos. -----------
    [Disp<PERSON><PERSON><PERSON>("SL.No."), NotMapped, Width(50), AlignCenter]
    public long RowNumber { get; set; }
    //-----------------------
    
    [EditLink, Width(250)]
    [DisplayName("Client Name")]
    public string ClientName { get; set; }

    [EditLink, Width(250)]
    [DisplayName("Consultant")]
    public string ConsultantName { get; set; }

    [EditLink, Width(100)]
    [DisplayName("Short Name")]
    public string ClientCode { get; set; }

    [EditLink, Width(250)]
    [DisplayName("Address")]
    public string Address { get; set; }

    [Hidden]
    public string Address2 { get; set; }

    [DisplayName("City")]
    public string CityName { get; set; }

    [DisplayName("Pin Code")]
    public string PINCode { get; set; }

    [DisplayName("Phone No.")]
    public string PhoneNo { get; set; }

    [DisplayName("Fax No.")]
    public string FaxNo { get; set; }

    [DisplayName("Web Site")]
    public string HomePage { get; set; }
     
    [Hidden]
    public string Logo { get; set; }

    [DisplayName("Contact Person")]
    public string ClientContactName { get; set; }

    [DisplayName("Mobile No.")]
    public string MobileNo { get; set; }

    [DisplayName("Alternate No.")]
    public string AlternateNo { get; set; }

    [DisplayName("Email")]
    public string EMail { get; set; }

    [DisplayName("GST No.")]
    public string GSTIN { get; set; }

    [DisplayName("Company PAN")]
    public string PAN { get; set; }

    [DisplayName("Import/Export No.")]
    public string IECNo { get; set; }

    [DisplayName("Company CIN")]
    public string CINNo { get; set; }

    [DisplayName("Company TAN")]
    public string TANNo { get; set; }

    [DisplayName("Udyam Reg. No.")]
    public string UdyamNo { get; set; }

    [DisplayName("Company Tag Line")]
    public string TagLine { get; set; }

    [Hidden]
    public string ClientDSC { get; set; }
    [Hidden]
    public string InvoiceNoFormat { get; set; }
    [Hidden]
    public string Disclaimer { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int ClientId { get; set; }

}