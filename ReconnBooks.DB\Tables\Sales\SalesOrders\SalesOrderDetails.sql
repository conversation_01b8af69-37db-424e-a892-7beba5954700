﻿CREATE TABLE [dbo].[SalesOrderDetails] (
    [SalesOrderDetailId]    INT             IDENTITY (1, 1) NOT NULL,
    [SalesOrderId]          INT             NOT NULL,
    [CommodityTypeId]       INT             NOT NULL,
    [CommodityId]           BIGINT          NOT NULL,
    [CommodityDescription]  NVARCHAR (MAX)  NULL,
    
    [OfferQuantity]         DECIMAL (18, 2) NULL,
    [OfferUnitId]           INT             NULL,
    [OfferPrice]            DECIMAL (18, 2) NULL,
    [OrderQuantity]         DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_Quantity] DEFAULT ((1)) NOT NULL,
    [OrderUnitId]           INT             NOT NULL,
    [OrderUnitPrice]        DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_UnitPrice] DEFAULT ((0)) NOT NULL,
    
    [DiscountPercent]       DECIMAL (18, 2) DEFAULT ((0)) NULL,
    [DiscountAmountPerUnit] DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_Disc.AmtPerUnit] DEFAULT ((0)) NULL,
    [NetDiscountAmount]     DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_NetDiscountAmt] DEFAULT ((0)) NULL,
    
    [GSTRateId]             INT             NOT NULL,
    [IGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_IGSTRate] DEFAULT ((0)) NULL,
    [CGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_CGSTRate] DEFAULT ((0)) NULL,
    [SGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_SalesOrderDetails_SGSTRate] DEFAULT ((0)) NULL,
    
    [DummyField]            DECIMAL (18, 2) NULL,
    [PricePerUnit]          DECIMAL (18, 2) NULL,
    [NetAmount]             DECIMAL (18, 2) NULL,
    
    CONSTRAINT [PK_SalesOrderDetails] PRIMARY KEY CLUSTERED ([SalesOrderDetailId] ASC),
    CONSTRAINT [FK_SalesOrderDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_SalesOrderDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_SalesOrderDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_SalesOrderDetails_SalesOrders] FOREIGN KEY ([SalesOrderId]) REFERENCES [dbo].[SalesOrders] ([SalesOrderId]),
    CONSTRAINT [FK_SalesOrderDetails_OfferUnits] FOREIGN KEY ([OfferUnitId]) REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_SalesOrderDetails_OrderUnits] FOREIGN KEY ([OrderUnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [SalesOrders]
    ON [dbo].[SalesOrderDetails]([SalesOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[SalesOrderDetails]([CommodityId] ASC);

