﻿CREATE TABLE [dbo].[DeliveryChallans] 
(
    [DeliveryChallanId]     INT             NOT NULL    IDENTITY (1, 1),
    [DeliveryChallanNo]     NVARCHAR (50)   NOT NULL,
    [DeliveryChallanDate]   DATETIME            NULL,
    [CustomerId]            INT             NOT NULL,

    [NatureOfSupplyId]		INT             NOT NULL,--     Nature of Supply
    [SupplyTypeId]          INT                 NULL,--     Nature of Supply section
    [FinancialYearId]       INT                 NULL,
        
    [QuotationId]           INT                 NULL,
    [SalesOrderId]          INT                 NULL,
    [OrderRefNo]            NVARCHAR (50)       NULL,
    [OrderRefDate]          SMALLDATETIME       NULL,
    [eWayBillNo]            NVARCHAR (50)       NULL,
    [eWayBillNoDate]        SMALLDATETIME       NULL,

    [DeliveryAt]            NVARCHAR (250)      NULL,
    [ShipedVia]             NVARCHAR (250)      NULL,
    [ShippingDocketNo]      NVARCHAR (50)       NULL,
    [VehicleNo]             NVARCHAR (20)       NULL,
    [UploadDocketCopy]      NVARCHAR (250)       NULL,
    [Inspection]            NVARCHAR (100)      NULL,
    [Remarks]               NVARCHAR (MAX)      NULL,
     -------------------Authorization Details-------------
    [ClientId]              INT	            NOT NULL    CONSTRAINT [DF_DeliveryChallans_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	                NULL,
    [PreparedDate]          DATETIME            NULL,
    [VerifiedByUserId]      INT                 NULL,
    [VerifiedDate]          DATETIME            NULL,
    [AuthorizedByUserId]    INT                 NULL,
    [AuthorizedDate]        DATETIME            NULL,
    [ModifiedByUserId]      INT                 NULL,
    [ModifiedDate]          DATETIME            NULL,
    [CancelledByUserId]     INT                 NULL,
    [CancelledDate]			DATETIME            NULL,
    [AuthorizationStatus]   BIT             NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details-------------
    CONSTRAINT [FK_DeliveryChallans_PreparedByUsers]      FOREIGN KEY ([PreparedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryChallans_VerfiedByUsers]       FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryChallans_AuthorizedByUsers]    FOREIGN KEY ([AuthorizedByUserId])REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryChallans_ModifiedByUsers]      FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryChallans_CancelledByUsers]     FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_DeliveryChallans_Clients]	          FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients]([ClientId]),
    -------------------Authorization Details End-------------
    
    CONSTRAINT [PK_DeliveryChallans]  PRIMARY KEY CLUSTERED     ([DeliveryChallanId] ASC),
    CONSTRAINT [FK_DeliveryChallans_Customers]		FOREIGN KEY ([CustomerId])		    REFERENCES [dbo].[Customers]		([CustomerId]),
    CONSTRAINT [FK_DeliveryChallans_Quotations]		FOREIGN KEY ([QuotationId])		    REFERENCES [dbo].[Quotations]		([QuotationId]),
    CONSTRAINT [FK_DeliveryChallans_SalesOrders]	FOREIGN KEY ([SalesOrderId])	    REFERENCES [dbo].[SalesOrders]	    ([SalesOrderId]),
    CONSTRAINT [FK_DeliveryChallans_NatureOfSupply]	FOREIGN KEY ([NatureOfSupplyId])    REFERENCES [dbo].[NatureOfSupply]   ([NatureOfSupplyId]),
    CONSTRAINT [FK_DeliveryChallans_SupplyTypes]    FOREIGN KEY ([SupplyTypeId])	    REFERENCES [dbo].[SupplyTypes]      ([SupplyTypeId]),
    CONSTRAINT [FK_DeliveryChallans_FinancialYears]	FOREIGN KEY ([FinancialYearId])	    REFERENCES [dbo].[FinancialYears]	([FinancialYearId]),
);

GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[DeliveryChallans]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [Quotations]
    ON [dbo].[DeliveryChallans]([QuotationId] ASC);
    GO
CREATE NONCLUSTERED INDEX [SalesOrders]
    ON [dbo].[DeliveryChallans]([SalesOrderId] ASC);
GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[DeliveryChallans]([FinancialYearId] ASC);
GO
CREATE NONCLUSTERED INDEX [NatureOfSupply]
    ON [dbo].[DeliveryChallans]([NatureOfSupplyId] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[DeliveryChallans]([ClientId] ASC);
