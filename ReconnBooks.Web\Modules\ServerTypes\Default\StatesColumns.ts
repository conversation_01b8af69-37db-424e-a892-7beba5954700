﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { StatesRow } from "./StatesRow";

export interface StatesColumns {
    RowNumber: Column<StatesRow>;
    StateId: Column<StatesRow>;
    StateName: Column<StatesRow>;
    StateCode: Column<StatesRow>;
    StateCodeNo: Column<StatesRow>;
    CountryName: Column<StatesRow>;
}

export class StatesColumns extends ColumnsBase<StatesRow> {
    static readonly columnsKey = 'Default.States';
    static readonly Fields = fieldsProxy<StatesColumns>();
}