﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { CreditNoteDetailsRow } from "./CreditNoteDetailsRow";

export interface CreditNotesRow {
    RowNumber?: number;
    CreditNoteId?: number;
    CreditNoteNo?: string;
    CreditNoteDate?: string;
    CreditNoteMonth?: string;
    CustomerId?: number;
    FinancialYearId?: number;
    InvoiceId?: number;
    SalesReturnId?: number;
    CreditNoteDetailsList?: CreditNoteDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    PlaceOfSupplyStateName?: string;
    CreditNoteAmount?: number;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    FinancialYearName?: string;
    InvoiceNo?: string;
    SalesReturnNo?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class CreditNotesRow {
    static readonly idProperty = 'CreditNoteId';
    static readonly nameProperty = 'CreditNoteNo';
    static readonly localTextPrefix = 'Default.CreditNotes';
    static readonly lookupKey = 'Default.CreditNotes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CreditNotesRow>('Default.CreditNotes') }
    static async getLookupAsync() { return getLookupAsync<CreditNotesRow>('Default.CreditNotes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CreditNotesRow>();
}