﻿import { LookupEditor, StringEditor, TextAreaEditor, ServiceLookupEditor, DecimalEditor, BooleanEditor, MultipleImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { HsnsacCodesDialog } from "../../Default/HsnsacCodes/HsnsacCodesDialog";
import { ProductCategoriesDialog } from "../../Default/ProductCategories/ProductCategoriesDialog";
import { ProductGroupsDialog } from "../../Default/ProductGroups/ProductGroupsDialog";
import { ProductMakeDialog } from "../../Default/ProductMake/ProductMakeDialog";
import { ProductTypesDialog } from "../../Default/ProductTypes/ProductTypesDialog";
import { UnitsDialog } from "../../Default/Units/UnitsDialog";

export interface CommoditiesForm {
    CommodityTypeId: LookupEditor;
    CommodityCode: StringEditor;
    CommodityName: StringEditor;
    CommodityDescription: TextAreaEditor;
    UnitId: LookupEditor;
    UQCQuantityName: StringEditor;
    HSNSACCodeId: ServiceLookupEditor;
    GSTRateId: LookupEditor;
    HSNSACDescription: TextAreaEditor;
    HSNSACGroup: StringEditor;
    SalesPrice: DecimalEditor;
    PurchasePrice: DecimalEditor;
    ListPrice: DecimalEditor;
    MRP: DecimalEditor;
    Remarks: StringEditor;
    ProductCategoryId: ServiceLookupEditor;
    ProductGroupId: ServiceLookupEditor;
    ProductTypeId: ServiceLookupEditor;
    ProductMakeId: ServiceLookupEditor;
    ProductWeight: StringEditor;
    CommodityStatus: BooleanEditor;
    ProductImage: MultipleImageUploadEditor;
}

export class CommoditiesForm extends PrefixedContext {
    static readonly formKey = 'Default.Commodities';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CommoditiesForm.init)  {
            CommoditiesForm.init = true;

            var w0 = LookupEditor;
            var w1 = StringEditor;
            var w2 = TextAreaEditor;
            var w3 = ServiceLookupEditor;
            var w4 = DecimalEditor;
            var w5 = BooleanEditor;
            var w6 = MultipleImageUploadEditor;

            initFormType(CommoditiesForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityName', w1,
                'CommodityDescription', w2,
                'UnitId', w0,
                'UQCQuantityName', w1,
                'HSNSACCodeId', w3,
                'GSTRateId', w0,
                'HSNSACDescription', w2,
                'HSNSACGroup', w1,
                'SalesPrice', w4,
                'PurchasePrice', w4,
                'ListPrice', w4,
                'MRP', w4,
                'Remarks', w1,
                'ProductCategoryId', w3,
                'ProductGroupId', w3,
                'ProductTypeId', w3,
                'ProductMakeId', w3,
                'ProductWeight', w1,
                'CommodityStatus', w5,
                'ProductImage', w6
            ]);
        }
    }
}

queueMicrotask(() => [UnitsDialog, HsnsacCodesDialog, ProductCategoriesDialog, ProductGroupsDialog, ProductTypesDialog, ProductMakeDialog]); // referenced dialogs