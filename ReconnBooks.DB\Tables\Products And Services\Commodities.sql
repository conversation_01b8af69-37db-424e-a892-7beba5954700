﻿CREATE TABLE [dbo].[Commodities] 
(
    [CommodityId]           BIGINT          NOT NULL    IDENTITY (1, 1),
    [CommodityName]         NVARCHAR (500)  NOT NULL,
    [CommodityCode]         NVARCHAR (50)       NULL,
    [CommodityDescription]  NVARCHAR (MAX)      NULL,
    [CommodityTypeId]       INT             NOT NULL,

    [UnitId]                INT             NOT NULL,
    [HSNSACCodeId]          INT                 NULL,
    [GSTRateId]             INT             NOT NULL,
    [PurchasePrice]         DECIMAL (18, 2)     NULL    CONSTRAINT [DF_Commodities_CostPrice] DEFAULT ((0)),
    [SalesPrice]            DECIMAL (18, 2)     NULL    CONSTRAINT [DF_Commodities_BillingPrice] DEFAULT ((0)),
  
    [ProductCategoryId]  INT                    NULL,
    [ProductGroupId]     INT                    NULL,
    [ProductTypeId]      INT                    NULL,
    [ProductMakeId]      INT                    NULL,
    [ProductWeight]      NVARCHAR (50)          NULL,
    [ProductImage]       NVARCHAR (MAX)         NULL,
    [ProductStatus]      BIT                NOT NULL    CONSTRAINT [DF_Commodities_Status] DEFAULT ((0)),
    [Remarks]            NVARCHAR (MAX)         NULL,
    [ClientId]           INT                NOT NULL    CONSTRAINT [DF_Commodities_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_Commodities] PRIMARY KEY CLUSTERED ([CommodityId] ASC),
    CONSTRAINT [FK_Commodities_CommodityTypes]     FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_Commodities_Units]              FOREIGN KEY ([UnitId])          REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_Commodities_HSNSACCodes]        FOREIGN KEY ([HSNSACCodeId])    REFERENCES [dbo].[HSNSACCodes] ([HSNSACCodeId]),
    CONSTRAINT [FK_Commodities_GSTRates]           FOREIGN KEY ([GSTRateId])       REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_Commodities_ProductGroups]      FOREIGN KEY ([ProductGroupId])  REFERENCES [dbo].[ProductGroups] ([ProductGroupId]),
    CONSTRAINT [FK_Commodities_ProductTypes]       FOREIGN KEY ([ProductTypeId])   REFERENCES [dbo].[ProductTypes] ([ProductTypeId]),
    CONSTRAINT [FK_Commodities_Categories]         FOREIGN KEY ([ProductCategoryId]) REFERENCES [dbo].[ProductCategories] ([ProductCategoryId]),
    CONSTRAINT [FK_Commodities_ProductMake]        FOREIGN KEY ([ProductMakeId])   REFERENCES [dbo].[ProductMake] ([ProductMakeId]),
    CONSTRAINT [FK_Commodities_Clients]            FOREIGN KEY ([ClientId])        REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [CK_Commodities_PurchasePrice]      CHECK ([PurchasePrice]>=(0)),
    CONSTRAINT [CK_Commodities_SalesPrice]         CHECK ([SalesPrice]>=(0))
);

GO
CREATE NONCLUSTERED INDEX [ProductGroups]
    ON [dbo].[Commodities]([ProductGroupId] ASC);
GO
CREATE NONCLUSTERED INDEX [ProductTypes]
    ON [dbo].[Commodities]([ProductTypeId] ASC);
GO
CREATE NONCLUSTERED INDEX [ProductCategories]
    ON [dbo].[Commodities]([ProductCategoryId] ASC);
GO
CREATE NONCLUSTERED INDEX [CommodityName]
    ON [dbo].[Commodities]([CommodityName] ASC);
GO
CREATE NONCLUSTERED INDEX [CommodityCode]
    ON [dbo].[Commodities]([CommodityCode] ASC);