﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ProductTypesRow } from "./ProductTypesRow";

export interface ProductTypesColumns {
    RowNumber: Column<ProductTypesRow>;
    ProductTypeId: Column<ProductTypesRow>;
    ProductType: Column<ProductTypesRow>;
    ClientName: Column<ProductTypesRow>;
}

export class ProductTypesColumns extends ColumnsBase<ProductTypesRow> {
    static readonly columnsKey = 'Default.ProductTypes';
    static readonly Fields = fieldsProxy<ProductTypesColumns>();
}