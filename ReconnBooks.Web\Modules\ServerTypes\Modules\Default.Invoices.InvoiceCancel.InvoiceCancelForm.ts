﻿import { IntegerEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface InvoiceCancelForm {
    InvoiceId: IntegerEditor;
    CancelReason: TextAreaEditor;
}

export class InvoiceCancelForm extends PrefixedContext {
    static readonly formKey = 'Main.InvoiceCancel';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!InvoiceCancelForm.init)  {
            InvoiceCancelForm.init = true;

            var w0 = IntegerEditor;
            var w1 = TextAreaEditor;

            initFormType(InvoiceCancelForm, [
                'InvoiceId', w0,
                'CancelReason', w1
            ]);
        }
    }
}