﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ProductMakeRow } from "./ProductMakeRow";

export interface ProductMakeColumns {
    RowNumber: Column<ProductMakeRow>;
    ProductMakeId: Column<ProductMakeRow>;
    ProductMake: Column<ProductMakeRow>;
    ClientName: Column<ProductMakeRow>;
}

export class ProductMakeColumns extends ColumnsBase<ProductMakeRow> {
    static readonly columnsKey = 'Default.ProductMake';
    static readonly Fields = fieldsProxy<ProductMakeColumns>();
}