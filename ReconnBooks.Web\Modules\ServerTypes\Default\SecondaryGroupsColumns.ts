﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { SecondaryGroupsRow } from "./SecondaryGroupsRow";

export interface SecondaryGroupsColumns {
    RowNumber: Column<SecondaryGroupsRow>;
    SecondaryGroupCode: Column<SecondaryGroupsRow>;
    SecondaryGroupName: Column<SecondaryGroupsRow>;
    PrimaryGroupName: Column<SecondaryGroupsRow>;
    Remarks: Column<SecondaryGroupsRow>;
    SecondaryGroupId: Column<SecondaryGroupsRow>;
}

export class SecondaryGroupsColumns extends ColumnsBase<SecondaryGroupsRow> {
    static readonly columnsKey = 'Default.SecondaryGroups';
    static readonly Fields = fieldsProxy<SecondaryGroupsColumns>();
}