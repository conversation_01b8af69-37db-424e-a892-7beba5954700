﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface DesignationsForm {
    Designation: StringEditor;
    Description: StringEditor;
}

export class DesignationsForm extends PrefixedContext {
    static readonly formKey = 'Default.Designations';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DesignationsForm.init)  {
            DesignationsForm.init = true;

            var w0 = StringEditor;

            initFormType(DesignationsForm, [
                'Designation', w0,
                'Description', w0
            ]);
        }
    }
}