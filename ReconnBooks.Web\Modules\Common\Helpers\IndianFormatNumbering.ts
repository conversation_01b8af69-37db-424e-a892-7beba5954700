import { Decorators, Formatter } from "@serenity-is/corelib";
import { FormatterContext } from "@serenity-is/sleekgrid";

@Decorators.registerFormatter('ReconnBooks.Default.IndianNumberFormatter')
export class IndianNumberFormatter implements Formatter {
    format(ctx: FormatterContext): string {
        if (ctx.value == null) {
            return '';
        }

        // Convert number to Indian format (Lakh and Crore)
        let x =  (ctx.value as number).toLocaleString('en-IN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
            
        return x;
    }
}







