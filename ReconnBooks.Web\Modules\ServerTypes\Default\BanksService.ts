﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { BanksRow } from "./BanksRow";

export namespace BanksService {
    export const baseUrl = 'Default/Banks';

    export declare function Create(request: SaveRequest<BanksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<BanksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<BanksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<BanksRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<BanksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<BanksRow>>;

    export const Methods = {
        Create: "Default/Banks/Create",
        Update: "Default/Banks/Update",
        Delete: "Default/Banks/Delete",
        Retrieve: "Default/Banks/Retrieve",
        List: "Default/Banks/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>BanksService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}