﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { TdsRatesRow } from "./TdsRatesRow";

export interface TdsRatesColumns {
    RowNumber: Column<TdsRatesRow>;
    Section: Column<TdsRatesRow>;
    Transaction: Column<TdsRatesRow>;
    TDSRate: Column<TdsRatesRow>;
    Limit: Column<TdsRatesRow>;
    Deductee: Column<TdsRatesRow>;
    WefDate: Column<TdsRatesRow>;
    FinancialYearName: Column<TdsRatesRow>;
    IsDefault: Column<TdsRatesRow>;
    Remarks: Column<TdsRatesRow>;
    TDSRateId: Column<TdsRatesRow>;
}

export class TdsRatesColumns extends ColumnsBase<TdsRatesRow> {
    static readonly columnsKey = 'Default.TdsRates';
    static readonly Fields = fieldsProxy<TdsRatesColumns>();
}