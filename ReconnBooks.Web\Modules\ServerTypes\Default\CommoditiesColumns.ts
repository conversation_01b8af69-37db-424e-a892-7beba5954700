﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CommoditiesRow } from "./CommoditiesRow";

export interface CommoditiesColumns {
    RowNumber: Column<CommoditiesRow>;
    CommodityType: Column<CommoditiesRow>;
    CommodityName: Column<CommoditiesRow>;
    CommodityCode: Column<CommoditiesRow>;
    UnitName: Column<CommoditiesRow>;
    CommodityDescription: Column<CommoditiesRow>;
    HSNSACCode: Column<CommoditiesRow>;
    GstRateRemarks: Column<CommoditiesRow>;
    PurchasePrice: Column<CommoditiesRow>;
    SalesPrice: Column<CommoditiesRow>;
    ProductCategoryCategoryName: Column<CommoditiesRow>;
    ProductGroup: Column<CommoditiesRow>;
    ProductType: Column<CommoditiesRow>;
    ProductMake: Column<CommoditiesRow>;
    ProductWeight: Column<CommoditiesRow>;
    ProductImage: Column<CommoditiesRow>;
    CommodityStatus: Column<CommoditiesRow>;
    CommodityId: Column<CommoditiesRow>;
    Remarks: Column<CommoditiesRow>;
    ClientId: Column<CommoditiesRow>;
}

export class CommoditiesColumns extends ColumnsBase<CommoditiesRow> {
    static readonly columnsKey = 'Default.Commodities';
    static readonly Fields = fieldsProxy<CommoditiesColumns>();
}