﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ConsultantsRow } from "./ConsultantsRow";

export interface ConsultantsColumns {
    RowNumber: Column<ConsultantsRow>;
    ConsultantName: Column<ConsultantsRow>;
    ConsultantCode: Column<ConsultantsRow>;
    Address: Column<ConsultantsRow>;
    CityName: Column<ConsultantsRow>;
    PINCode: Column<ConsultantsRow>;
    PhoneNo: Column<ConsultantsRow>;
    FaxNo: Column<ConsultantsRow>;
    HomePage: Column<ConsultantsRow>;
    Logo: Column<ConsultantsRow>;
    TitleOfRespect: Column<ConsultantsRow>;
    ContactPerson: Column<ConsultantsRow>;
    Designation: Column<ConsultantsRow>;
    MobileNo: Column<ConsultantsRow>;
    AlternateNo: Column<ConsultantsRow>;
    EMail: Column<ConsultantsRow>;
    GSTIN: Column<ConsultantsRow>;
    PlaceOfSupplyStateName: Column<ConsultantsRow>;
    NatureOfSupply: Column<ConsultantsRow>;
    SupplyType: Column<ConsultantsRow>;
    PAN: Column<ConsultantsRow>;
    IECNo: Column<ConsultantsRow>;
    UdyamNo: Column<ConsultantsRow>;
    CINNo: Column<ConsultantsRow>;
    TANNo: Column<ConsultantsRow>;
    BusinessType: Column<ConsultantsRow>;
    BusinessGroup: Column<ConsultantsRow>;
    BusinessCategory: Column<ConsultantsRow>;
    InvoiceNoFormat: Column<ConsultantsRow>;
    Disclaimer: Column<ConsultantsRow>;
    ConsultantId: Column<ConsultantsRow>;
}

export class ConsultantsColumns extends ColumnsBase<ConsultantsRow> {
    static readonly columnsKey = 'Default.Consultants';
    static readonly Fields = fieldsProxy<ConsultantsColumns>();
}