﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.HsnSummaryRow;

namespace ReconnBooks.Default;

public interface IHsnSummaryDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> { }

public class HsnSummaryDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IHsnSummaryDeleteHandler
{
    public HsnSummaryDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}