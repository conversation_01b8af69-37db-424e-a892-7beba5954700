﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { RejectionReasonsRow } from "./RejectionReasonsRow";

export namespace RejectionReasonsService {
    export const baseUrl = 'Default/RejectionReasons';

    export declare function Create(request: SaveRequest<RejectionReasonsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<RejectionReasonsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<RejectionReasonsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<RejectionReasonsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<RejectionReasonsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<RejectionReasonsRow>>;

    export const Methods = {
        Create: "Default/RejectionReasons/Create",
        Update: "Default/RejectionReasons/Update",
        Delete: "Default/RejectionReasons/Delete",
        Retrieve: "Default/RejectionReasons/Retrieve",
        List: "Default/RejectionReasons/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>RejectionReasonsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}