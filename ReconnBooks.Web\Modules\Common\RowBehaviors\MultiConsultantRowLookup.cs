using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using ReconnBooks.Modules.Default.Consultants;


namespace ReconnBooks.Modules.Common.RowBehaviors;

public class MultiConsultantRowLookup<TRow> :
            RowLookupScript<TRow>
            where TRow : class, IRow, IMultiConsultantRow, new()
{
    public ITwoLevelCache TwoLevelCache { get; }
    public IUserAccessor UserAccessor { get; }

    public MultiConsultantRowLookup(ISqlConnections sqlConnections, ITwoLevelCache twoLevelCache, IUserAccessor userAccessor) : base(sqlConnections)
    {
        Expiration = TimeSpan.FromDays(-1);
        TwoLevelCache = twoLevelCache ?? throw new ArgumentNullException(nameof(twoLevelCache));
        UserAccessor = userAccessor ?? throw new ArgumentNullException(nameof(userAccessor));
    }

    protected override void PrepareQuery(SqlQuery query)
    {
        base.PrepareQuery(query);
        AddConsultantFilter(query);
    }

    protected void AddConsultantFilter(SqlQuery query)
    {
        var username = UserAccessor.User?.Identity?.Name;
        if (username == "admin")
            return;

        var r = new TRow();
        query.Where(r.ConsultantIdField == UserAccessor.User.GetConsultantId().GetValueOrDefault());
    }

    public override string GetScript()
    {
        return TwoLevelCache.GetLocalStoreOnly("MultiConsultantLookup:" +
                this.ScriptName + ":" +
                UserAccessor.User.GetConsultantId(),
                TimeSpan.FromHours(1),
            new TRow().GetFields().GenerationKey, () =>
            {
                return base.GetScript();
            });
    }
}
