﻿CREATE TABLE [dbo].[SalesOrders] 
(
    [SalesOrderId]      INT             NOT NULL    IDENTITY (1, 1),
    [SalesOrderNo]      NVARCHAR (50)   NOT NULL,
    [SalesOrderDate]    DATETIME            NULL,
    [CustomerId]        INT             NOT NULL,
    
    [SupplyTypeId]      INT                 NULL,
    [FinancialYearId]   INT                 NULL,

    [QuotationId]       INT                 NULL,
    [OrderRefNo]        NVARCHAR (50)       NULL,       --Customer Order reference No.
    [OrderRefDate]      DATETIME            NULL,       --Customer Order reference Date.
    [DeliveryDueDate]   DATETIME           NULL,
    [UploadOrderCopy]   NVARCHAR(250)       NULL,       --user can upload Customer Order Copy image

    [PaymentTermsId]    INT                 NULL,
    [GrandTotal]         DECIMAL (18, 2) NULL,
    [Inspection]        NVARCHAR (100)      NULL,
    [ShippingAddress]   NVARCHAR (MAX)      NULL,
    [ShippingCityId]    INT                 NULL,
    [ShippingPinCode]   INT                 NULL,

    [Remarks]           NVARCHAR (MAX)      NULL,

    -------------------Authorization Details-------------
    [ClientId]              INT	        NOT NULL    CONSTRAINT [DF_SalesOrder_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	            NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]			DATETIME        NULL,
    [AuthorizedStatus]      BIT         NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details----------------------
    CONSTRAINT [FK_SalesOrders_PreparedByUsers]     FOREIGN KEY ([PreparedByUserId])    REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesOrders_VerfiedByUsers]      FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesOrders_AuthorizedByUsers]   FOREIGN KEY ([AuthorizedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesOrders_ModifiedByUsers]     FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesOrders_CancelledByUsers]    FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesOrders_Clients]	            FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients] ([ClientId]),
    -------------------Authorization Details End------------------

    CONSTRAINT [PK_SalesOrders] PRIMARY KEY     CLUSTERED ([SalesOrderId] ASC),
    CONSTRAINT [FK_SalesOrders_Customers]       FOREIGN KEY ([CustomerId])          REFERENCES [dbo].[Customers]    ([CustomerId]),
    CONSTRAINT [FK_SalesOrders_Quotations]      FOREIGN KEY ([QuotationId])         REFERENCES [dbo].[Quotations]   ([QuotationId]),

    CONSTRAINT [FK_SalesOrders_SupplyTypes]     FOREIGN KEY ([SupplyTypeId])        REFERENCES [dbo].[SupplyTypes]  ([SupplyTypeId]),
    CONSTRAINT [FK_SalesOrders_FinancialYears]  FOREIGN KEY ([FinancialYearId])     REFERENCES [dbo].[FinancialYears]([FinancialYearId]),
    CONSTRAINT [FK_SalesOrders_PaymentTerms]    FOREIGN KEY ([PaymentTermsId])      REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
	CONSTRAINT [FK_SalesOrders_ShippingCities]  FOREIGN KEY ([ShippingCityId])      REFERENCES [dbo].[Cities]       ([CityId])
);

GO
CREATE NONCLUSTERED INDEX [CustomerId]
    ON [dbo].[SalesOrders]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [QuotationId]
    ON [dbo].[SalesOrders]([QuotationId] ASC);
GO
CREATE NONCLUSTERED INDEX [FinancialYear]
    ON [dbo].[SalesOrders]([FinancialYearId] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[SalesOrders]([ClientId] ASC);