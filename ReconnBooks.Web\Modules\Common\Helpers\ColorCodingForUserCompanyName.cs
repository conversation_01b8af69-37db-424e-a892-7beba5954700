using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;

public class ColorCodingForUserCompanyName(IHttpContextAccessor httpContextAccessor)
{
    private readonly IHttpContextAccessor httpContextAccessor = httpContextAccessor;

    private static readonly Dictionary<string, List<string>> ThemeColors = new()
    {
        ["azure-light"] = new()
        {
            "#39a7b9", "#2d3542", "#4a4a8e", "#3cacbf", "#b33f32", "#9c78c8", "#606570", "#777777", "#9880bd", "#c9d0da", "#7f8bb5", "#0c6190", "#6f42c1"
        },
        ["cosmos-dark"] = new()
        {
            "#836ba8", "#6f82c5", "#8693c2", "#8075af", "#7ec6cd", "#3fb7ff", "#6eb5ff", "#c7ccd3", "#5b6a7b", "#7c90a5", "#2f3d4e", "#354151", "#536078"
        },
        ["glassy-light"] = new()
        {
            "#669281", "#0e3293", "#003478", "#039989", "#4f5e06", "#278d00", "#306a4e", "#066624", "#4a4a8e", "#4f5e06", "#555555", "#777777", "#9880bd"
        }
    };

    public string GenerateColorCode(string name, HashSet<string> usedColors)
    {
        var context = httpContextAccessor.HttpContext;
        var themeCookie = context?.Request.Cookies["ProThemeSelection"];
        var theme = !string.IsNullOrEmpty(themeCookie) ? themeCookie : "azure-light";

        var colors = ThemeColors.TryGetValue(theme, out var themeColors) ? themeColors : ThemeColors["azure-light"];

        int hash = Math.Abs(name.GetHashCode());
        int index = hash % colors.Count;

        // Ensure the color is unique
        while (usedColors.Contains(colors[index]))
        {
            index = (index + 1) % colors.Count;
        }

        string selectedColor = colors[index];
        usedColors.Add(selectedColor);

        return selectedColor;
    }
}
