import { BanksColumns, BanksRow, BanksService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { BanksDialog } from './BanksDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.BanksGrid')
export class BanksGrid extends EntityGridDialog<BanksRow, any> {
    protected getColumnsKey() { return BanksColumns.columnsKey; }
    protected getDialogType() { return BanksDialog; }
    protected getRowDefinition() { return BanksRow; }
    protected getService() { return BanksService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}