﻿CREATE TABLE [dbo].[QuotationAnnexure] 
(
    [QuotationAnnexId]  INT                 NOT NULL    IDENTITY (1, 1),
    [QuotationId]       INT                 NOT NULL,
    [QuotationDetailId] INT                     NULL,   -- can be used if there is a need to use the Products and Services details
    [Annexure]          NVARCHAR (MAX)          NULL,
    [Remarks]           NVARCHAR (MAX)          NULL,
    [UploadDocuments]   NTEXT                   NULL,

    CONSTRAINT [PK_QuotationAnnexure] PRIMARY KEY CLUSTERED ([QuotationAnnexId] ASC),
    CONSTRAINT [FK_QuotationAnnex_Quotations] FOREIGN KEY ([QuotationId]) REFERENCES [dbo].[Quotations] ([QuotationId]),
);
GO
CREATE NONCLUSTERED INDEX [QuotationAnnex]
    ON [dbo].[QuotationAnnexure]([QuotationId] ASC);
