﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface CustomerDetailsRow {
    CustomerDetailId?: number;
    LastContactDate?: string;
    LastContactedByEmployeeId?: number;
    Email?: string;
    SendBulletin?: boolean;
    LastContactedByEmployeeFirstName?: string;
}

export abstract class CustomerDetailsRow {
    static readonly idProperty = 'CustomerDetailId';
    static readonly nameProperty = 'Email';
    static readonly localTextPrefix = 'Default.CustomerDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CustomerDetailsRow>();
}