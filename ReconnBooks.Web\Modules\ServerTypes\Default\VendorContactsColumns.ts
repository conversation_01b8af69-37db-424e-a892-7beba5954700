﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { VendorContactsRow } from "./VendorContactsRow";

export interface VendorContactsColumns {
    RowNumber: Column<VendorContactsRow>;
    VendorName: Column<VendorContactsRow>;
    TitleOfRespect: Column<VendorContactsRow>;
    ContactName: Column<VendorContactsRow>;
    Designation: Column<VendorContactsRow>;
    DepartmentName: Column<VendorContactsRow>;
    MobileNo: Column<VendorContactsRow>;
    Email: Column<VendorContactsRow>;
    AlternateNo: Column<VendorContactsRow>;
    OfficePhoneNo: Column<VendorContactsRow>;
    ExtensionNo: Column<VendorContactsRow>;
    Status: Column<VendorContactsRow>;
    VendorContactId: Column<VendorContactsRow>;
}

export class VendorContactsColumns extends ColumnsBase<VendorContactsRow> {
    static readonly columnsKey = 'Default.VendorContacts';
    static readonly Fields = fieldsProxy<VendorContactsColumns>();
}