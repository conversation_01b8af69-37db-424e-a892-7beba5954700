﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.BusinessGroupsRow>;
using MyRow = ReconnBooks.Default.BusinessGroupsRow;

namespace ReconnBooks.Default;

public interface IBusinessGroupsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessGroupsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessGroupsRetrieveHandler
{
    public BusinessGroupsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}