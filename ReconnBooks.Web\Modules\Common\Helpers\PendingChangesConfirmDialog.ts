import { Decorators, EntityDialog } from "@serenity-is/corelib";
import { DialogUtils } from "@serenity-is/extensions";
import { EntityDialogBase } from "./EntityDialogBase";

Decorators.registerClass()
export class PendingChangesConfirmDialog<TEntity> extends EntityDialogBase<TEntity> {

    protected loadedState: string;

    getSaveState() {
        try {
            return JSON.stringify(this.getSaveEntity());
        }
        catch (e) {
            return null;
        }
    }

    protected setDialogsLoadedState() {
        this.loadedState = this.getSaveState();
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();

        DialogUtils.pendingChangesConfirmation(this.element, () => this.getSaveState() != this.loadedState);
    }
}
