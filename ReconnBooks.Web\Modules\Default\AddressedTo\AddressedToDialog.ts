import { AddressedToForm, AddressedToRow, AddressedToService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.AddressedToDialog')
export class AddressedToDialog extends PendingChangesConfirmDialog<AddressedToRow> {
    protected getFormKey() { return AddressedToForm.formKey; }
    protected getRowDefinition() { return AddressedToRow; }
    protected getService() { return AddressedToService.baseUrl; }

    protected form = new AddressedToForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}