﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface CreditNoteDetailsRow {
    RowNumber?: number;
    CreditNoteDetailId?: number;
    CreditNoteId?: number;
    InvoiceDetailId?: number;
    SalesReturnDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    InvoiceQuantity?: number;
    InvoiceUnitId?: number;
    ReturnedQuantity?: number;
    ReturnedUnitId?: number;
    SerialNos?: string;
    UnitPrice?: number;
    UnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
    RejectionReasonId?: number;
    AssessmentRemarks?: string;
    ReplacementMethodId?: number;
    Remarks?: string;
    CreditNoteNo?: string;
    InvoiceDetailCommodityDescription?: string;
    SalesReturnDetailCommodityDescription?: string;
    InvoiceUnitUnitName?: string;
    ReturnedUnitUnitName?: string;
    GSTRateRemarks?: string;
    RejectionReason?: string;
    ReplacementMethod?: string;
}

export abstract class CreditNoteDetailsRow {
    static readonly idProperty = 'CreditNoteDetailId';
    static readonly nameProperty = 'CreditNoteDetailId';
    static readonly localTextPrefix = 'Default.CreditNoteDetails';
    static readonly lookupKey = 'Default.CreditNoteDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CreditNoteDetailsRow>('Default.CreditNoteDetails') }
    static async getLookupAsync() { return getLookupAsync<CreditNoteDetailsRow>('Default.CreditNoteDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CreditNoteDetailsRow>();
}