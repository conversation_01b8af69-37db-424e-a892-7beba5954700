﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface RejectionReasonsForm {
    RejectionReason: StringEditor;
    Description: StringEditor;
}

export class RejectionReasonsForm extends PrefixedContext {
    static readonly formKey = 'Default.RejectionReasons';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!RejectionReasonsForm.init)  {
            RejectionReasonsForm.init = true;

            var w0 = StringEditor;

            initFormType(RejectionReasonsForm, [
                'RejectionReason', w0,
                'Description', w0
            ]);
        }
    }
}