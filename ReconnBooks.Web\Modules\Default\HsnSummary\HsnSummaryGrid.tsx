import { Aggregators, alertDialog, Decorators, EntityGrid, LookupEditor, toId } from '@serenity-is/corelib';
import { HsnSummaryColumns, HsnSummaryRow, HsnSummaryService } from '../../ServerTypes/Default';
import { ExcelExportHelper, PdfExportHelper } from '@serenity-is/extensions';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { GroupItemMetadataProvider } from '@serenity-is/sleekgrid';
import { HsnSummaryDialog } from './HsnSummaryDialog';

@Decorators.registerClass('ReconnBooks.Default.HsnSummaryGrid')
export class HsnSummaryGrid extends EntityGrid<HsnSummaryRow> {
    protected getColumnsKey() { return HsnSummaryColumns.columnsKey; }
    protected getRowDefinition() { return HsnSummaryRow; }
    protected getDialogType() { return HsnSummaryDialog; }
    protected getService() { return HsnSummaryService.baseUrl; }
    protected getIdProperty() { return 'HsnCode'; }

    protected getButtons() {
        let buttons = super.getButtons();

        // Remove the "Add" button
        buttons = buttons.filter(x => x.cssClass !== "add-button");

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: HsnSummaryService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));

        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            //onViewSubmit: () => { setTimeout(() => { this.view.expandAllGroups(1); }, 100); return this.onViewSubmit(); },
            title: "PDF"
        }));

        return buttons;
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        // Set current financial year
        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }

        // Set current month
        const currentMonth = new Date().toLocaleString('default', { month: 'short' });
        this.findQuickFilter(LookupEditor, "InvoiceMonth").values = [currentMonth];
    }

    protected editItem(hsnCode: any) {
        const financialYearId = this.findQuickFilter(LookupEditor, "FinancialYearId").value;
        const invoiceMonth = this.findQuickFilter(LookupEditor, "InvoiceMonth").value;

        HsnSummaryService.Retrieve({
            EntityId: hsnCode,
            FinancialYearId: toId(financialYearId),
            InvoiceMonth: invoiceMonth
        }, response => {
            new HsnSummaryDialog().loadEntityAndOpenDialog(response.Entity);
        });
        return;
    }
}