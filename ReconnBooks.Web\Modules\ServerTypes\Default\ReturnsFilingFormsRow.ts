﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ReturnsFilingFormsRow {
    RowNumber?: number;
    ReturnsFilingFormId?: number;
    ReturnsFilingForm?: string;
    NatureOfSupplyId?: number;
    SupplyTypeId?: number;
    Description?: string;
    Filer?: string;
    Frequency?: string;
    DueDate?: string;
    Remarks?: string;
    NatureOfSupply?: string;
    SupplyType?: string;
}

export abstract class ReturnsFilingFormsRow {
    static readonly idProperty = 'ReturnsFilingFormId';
    static readonly nameProperty = 'ReturnsFilingForm';
    static readonly localTextPrefix = 'Default.ReturnsFilingForms';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ReturnsFilingFormsRow>();
}