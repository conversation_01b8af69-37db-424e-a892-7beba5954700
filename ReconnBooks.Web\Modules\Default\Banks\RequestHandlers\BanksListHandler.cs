﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.BanksRow>;
using MyRow = ReconnBooks.Default.BanksRow;

namespace ReconnBooks.Default;

public interface IBanksListHandler : IList<PERSON><PERSON>ler<MyRow, MyRequest, MyResponse> {}

public class BanksListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IBanksListHandler
{
    public BanksListHandler(IRequestContext context)
            : base(context)
    {
    }
}