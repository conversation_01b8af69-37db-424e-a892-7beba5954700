﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.AddressedToRow>;
using MyRow = ReconnBooks.Default.AddressedToRow;

namespace ReconnBooks.Default;

public interface IAddressedToRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class AddressedToRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IAddressedToRetrieveHandler
{
    public AddressedToRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}