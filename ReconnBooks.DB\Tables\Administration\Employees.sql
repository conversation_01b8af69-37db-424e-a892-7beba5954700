﻿CREATE TABLE [dbo].[Employees] (
    [EmployeeId]      INT            IDENTITY (1, 1) NOT NULL,
    [UserTypeId]      INT            NULL,
    [ClientId]        INT            NULL,
    [ConsultantId]    INT            NULL,
    [TitleId]         INT            NULL,
    [EmployeeName]    NVARCHAR (250) NOT NULL,
    [DateOfBirth]     SMALLDATETIME  NULL,
    [HireDate]        SMALLDATETIME  NULL,
    [DesignationId]   INT            NULL,
    [DepartmentId]    INT            NULL,
    [Address]         NVARCHAR (MAX) NOT NULL,
    [CityId]          INT            NULL,
    [PostalCode]      NVARCHAR (10)  NULL,
    [MobileNo]        NVARCHAR (18)  NULL,
    [AlternateNo]     NVARCHAR (18)  NULL,
    [PhoneNo]         NVARCHAR (50)  NULL,
    [EmailId]         NVARCHAR (100) NULL,
    [Notes]           NVARCHAR (MAX) NULL,
    [UploadDocuments] NTEXT          NULL,
    CONSTRAINT [PK_Employees] PRIMARY KEY CLUSTERED ([EmployeeId] ASC),
    CONSTRAINT [FK_Employees_Titles] FOREIGN KEY ([TitleId]) REFERENCES [dbo].[Titles] ([TitleId]),
    CONSTRAINT [FK_Employees_Designations] FOREIGN KEY ([DesignationId]) REFERENCES [dbo].[Designations] ([DesignationId]),
    CONSTRAINT [FK_Employees_Departments] FOREIGN KEY ([DepartmentId]) REFERENCES [dbo].[Departments] ([DepartmentId]),
    CONSTRAINT [FK_Employees_Cities] FOREIGN KEY ([CityId]) REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Employees_UserTypes] FOREIGN KEY ([UserTypeId]) REFERENCES [dbo].[UserTypes] ([UserTypeId]),
    CONSTRAINT [FK_Employees_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_Employees_Consultants] FOREIGN KEY ([ConsultantId]) REFERENCES [dbo].[Consultants] ([ConsultantId])
);


GO
CREATE NONCLUSTERED INDEX [EmployeeName]
    ON [dbo].[Employees]([EmployeeName] ASC);


GO
CREATE NONCLUSTERED INDEX [City]
    ON [dbo].[Employees]([CityId] ASC);


GO
CREATE NONCLUSTERED INDEX [PostalCode]
    ON [dbo].[Employees]([PostalCode] ASC);


GO
CREATE NONCLUSTERED INDEX [Designations]
    ON [dbo].[Employees]([DesignationId] ASC);


GO
CREATE NONCLUSTERED INDEX [Departments]
    ON [dbo].[Employees]([DepartmentId] ASC);


GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[Employees]([ClientId] ASC);

