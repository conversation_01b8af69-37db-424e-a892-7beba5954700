﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { PrimaryGroupsRow } from "./PrimaryGroupsRow";

export namespace PrimaryGroupsService {
    export const baseUrl = 'Default/PrimaryGroups';

    export declare function Create(request: SaveRequest<PrimaryGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PrimaryGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PrimaryGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PrimaryGroupsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PrimaryGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PrimaryGroupsRow>>;

    export const Methods = {
        Create: "Default/PrimaryGroups/Create",
        Update: "Default/PrimaryGroups/Update",
        Delete: "Default/PrimaryGroups/Delete",
        Retrieve: "Default/PrimaryGroups/Retrieve",
        List: "Default/PrimaryGroups/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>PrimaryGroupsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}