﻿CREATE TABLE [dbo].[Clients] 
(
    [ClientId]		    INT             NOT NULL	IDENTITY (1,1),
    [ClientName]        NVARCHAR (500)  NOT NULL,
    [ClientCode]		NVARCHAR (6)    NOT NULL,
    [Address]	        NVARCHAR (MAX)  NOT NULL,
    [Address2]	        NVARCHAR (250)      NULL,
    [CityId]			INT             NOT NULL,
    [PINCode]		    NVARCHAR (10)       NULL,
    [PhoneNo]			NVARCHAR (50)       NULL,
    [FaxNo]				NVARCHAR (50)       NULL,
    [CompanyEmail]		NVARCHAR (150)      NULL,
    [HomePage]			NVARCHAR (150)      NULL,
    [Logo]				NVARCHAR (MAX)      NULL,
    [TagLine]           NVARCHAR (MAX)      NULL,

    [GSTIN]		        NVARCHAR (30)       NULL,
    [PlaceOfSupplyId]   INT             NOT NULL    DEFAULT ((1)),
    [NatureOfSupplyId]  INT                 NULL,
    [SupplyTypeId]      INT                 NULL,
    [PAN]               NVARCHAR (30)       NULL,
    [UdyamNo]           NVARCHAR (100) NULL,
    [IECNo]             NVARCHAR (30)       NULL,
    [CINNo]             NVARCHAR (50)       NULL,
    [TANNo]               NVARCHAR (80)       NULL,       --Tax Deduction and Collection Account Number

    [TitleId]			INT                 NULL,
    [ClientContactName]	NVARCHAR (100)      NULL,
    [DesignationId]		INT                 NULL,
    [MobileNo]		    NVARCHAR (18)       NULL,
    [AlternateNo]		NVARCHAR (18)       NULL,
    [Email]			    NVARCHAR (150)      NULL,
    
    [BankId]            INT                 NULL, 
    [BranchName]        NVARCHAR (50)       NULL,
    [AccountName]               NVARCHAR (50)       NULL,
    [AccountNumber]             NVARCHAR (50)       NULL,
    [IFSCCode]                  NVARCHAR (50)       NULL, 
    [BranchCode]                NVARCHAR (50)       NULL,

    [ClientDSC]                 NVARCHAR (MAX)      NULL,
    [UploadDocuments]           NVARCHAR (MAX)      NULL,

    [BusinessTypeId]            INT                 NULL,   --We need to set the Business Type like "IT Industry, Manufacturing, Service Industry, travel Indusry etc.
    [BusinessGroupId]           INT                 NULL,   --Similar to the Type, we can set Group
    [BusinessCategoryId]        INT                 NULL,   --Similar to the Type, we can set Category

    [ConsultantId]              INT             NOT NULL    CONSTRAINT [DF_Clients_ConsultantId]	Default((0)),
	[InvoiceNoFormat]           NVARCHAR(20)        NULL, 
    [Disclaimer]                NVARCHAR(MAX)       NULL,

-- Email Server Settings
    [EmailServerHost]      NVARCHAR (100)      NULL,
    [EmailServerUsername]  NVARCHAR (100)      NULL,
    [EmailServerPasswordEncrypted] NVARCHAR (MAX)  NULL,
    [EmailServerPort]     INT                 NULL    DEFAULT 587,
	
	CONSTRAINT [PK_Clients] PRIMARY KEY     CLUSTERED	([ClientId] ASC), 
    CONSTRAINT [FK_Clients_Cities]          FOREIGN KEY ([CityId])          REFERENCES [Cities]([CityId]), 
	 
    CONSTRAINT [FK_Clients_Banks]           FOREIGN KEY ([BankId])          REFERENCES [Banks]([BankId]), 
    CONSTRAINT [FK_Clients_Designations]    FOREIGN KEY ([DesignationId])   REFERENCES [Designations]([DesignationId]), 
    CONSTRAINT [FK_Clients_Titles]          FOREIGN KEY ([TitleId])         REFERENCES [Titles]([TitleId]),
    CONSTRAINT [FK_Clients_Consultants]     FOREIGN KEY ([ConsultantId])    REFERENCES [Consultants]([ConsultantId]),
    --This field pertains to GST Invoice generation needs to set in the begining itself by default as onetime setting.
    CONSTRAINT [FK_Clients_PlaceOfSupply]   FOREIGN KEY ([PlaceOfSupplyId]) REFERENCES [States]([StateId]),
    CONSTRAINT [FK_Clients_NatureOfSupply]  FOREIGN KEY ([NatureOfSupplyId])REFERENCES [NatureOfSupply]([NatureOfSupplyId]),
    CONSTRAINT [FK_Clients_SupplyTypes]     FOREIGN KEY ([SupplyTypeId])    REFERENCES [SupplyTypes]([SupplyTypeId]),
    --these fields we are capturing to get the various business types onboarded into ReconnGST website.
    CONSTRAINT [FK_Clients_BusinessTypes]       FOREIGN KEY ([BusinessTypeId])          REFERENCES [BusinessTypes]([BusinessTypeId]),
    CONSTRAINT [FK_Clients_BusinessGroups]      FOREIGN KEY ([BusinessGroupId])         REFERENCES [BusinessGroups]([BusinessGroupId]),
    CONSTRAINT [FK_Clients_BusinessCategories]  FOREIGN KEY ([BusinessCategoryId])      REFERENCES [BusinessCategories]([BusinessCategoryId]),
);
GO
CREATE NONCLUSTERED INDEX [ClientCity]
    ON [dbo].[Clients]([CityId] ASC);
GO
CREATE NONCLUSTERED INDEX [ClientCompanyName]
    ON [dbo].[Clients]([ClientName] ASC);
GO
CREATE NONCLUSTERED INDEX [ClientPinCode]
    ON [dbo].[Clients]([PINCode] ASC);
GO
CREATE NONCLUSTERED INDEX [BusinessTypeId]
    ON [dbo].[Clients]([BusinessTypeId] ASC);
GO
CREATE NONCLUSTERED INDEX [BusinessGroupId]
    ON [dbo].[Clients]([BusinessGroupId] ASC);
GO
CREATE NONCLUSTERED INDEX [BusinessCategoryId]
    ON [dbo].[Clients]([BusinessCategoryId] ASC);

GO
CREATE UNIQUE NONCLUSTERED INDEX [Clients]
    ON [dbo].[Clients]([ClientId] ASC);

GO
CREATE UNIQUE NONCLUSTERED INDEX [Client]
    ON [dbo].[Clients]([ClientId] ASC);

