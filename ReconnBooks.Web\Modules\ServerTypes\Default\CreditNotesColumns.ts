﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { CreditNotesRow } from "./CreditNotesRow";

export interface CreditNotesColumns {
    RowNumber: Column<CreditNotesRow>;
    CreditNoteNo: Column<CreditNotesRow>;
    CreditNoteDate: Column<CreditNotesRow>;
    CustomerCompanyName: Column<CreditNotesRow>;
    FinancialYearName: Column<CreditNotesRow>;
    CreditNoteMonth: Column<CreditNotesRow>;
    InvoiceNo: Column<CreditNotesRow>;
    SalesReturnNo: Column<CreditNotesRow>;
    NetTaxableAmount: Column<CreditNotesRow>;
    NetCGSTAmount: Column<CreditNotesRow>;
    NetSGSTAmount: Column<CreditNotesRow>;
    NetIGSTAmount: Column<CreditNotesRow>;
    CreditNoteAmount: Column<CreditNotesRow>;
    Remarks: Column<CreditNotesRow>;
    PreparedByUserUsername: Column<CreditNotesRow>;
    PreparedDate: Column<CreditNotesRow>;
    VerifiedByUserUsername: Column<CreditNotesRow>;
    VerifiedDate: Column<CreditNotesRow>;
    AuthorizedByUserUsername: Column<CreditNotesRow>;
    AuthorizedDate: Column<CreditNotesRow>;
    ModifiedByUserUsername: Column<CreditNotesRow>;
    ModifiedDate: Column<CreditNotesRow>;
    CancelledByUserUsername: Column<CreditNotesRow>;
    CancelledDate: Column<CreditNotesRow>;
    AuthorizedStatus: Column<CreditNotesRow>;
    CreditNoteId: Column<CreditNotesRow>;
}

export class CreditNotesColumns extends ColumnsBase<CreditNotesRow> {
    static readonly columnsKey = 'Default.CreditNotes';
    static readonly Fields = fieldsProxy<CreditNotesColumns>();
}

[IndianNumberFormatter]; // referenced types