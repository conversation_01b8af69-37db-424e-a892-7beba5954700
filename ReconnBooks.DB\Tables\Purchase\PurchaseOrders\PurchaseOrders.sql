﻿CREATE TABLE [dbo].[PurchaseOrders] (
    [PurchaseOrderId]    INT             IDENTITY (1, 1) NOT NULL,
    [PurchaseOrderNo]    NVARCHAR (50)   NOT NULL,
    [PurchaseOrderDate]  DATETIME        NULL,
    [VendorId]           INT             NOT NULL,
    [SupplyTypeId]       INT             NOT NULL,
    [FinancialYearId]    INT             NOT NULL,
    [ReferenceNo]        NVARCHAR (50)   NULL,
    [ReferenceDate]      SMALLDATETIME   NULL,
    [HeaderNoteId]       INT             NULL,
    
    [TDSRateId]          INT             NULL,
    [TDSAmount]          DECIMAL (18, 2) NULL,
    [TCSRateId]          INT             NULL,
    [TCSAmount]          DECIMAL (18, 2) NULL,
    [POAmount]           DECIMAL (18, 2) NULL,
    [RoundingOff]        DECIMAL (18, 2) NULL,
    [GrandTotal]         DECIMAL (18, 2) NULL,
    
    [DeliveryAddress]    NVARCHAR (150)  NULL,
    [DeliveryCityId]     INT             NULL,
    [DeliveryPinCode]    INT             NULL,
    [DeliveryDueDate]    DATETIME        NULL,
    [PaymentTermsId]     INT             NULL,
    [PaymentDueDate]     DATETIME        NULL,
    
    [Taxes]              NVARCHAR (250)  NULL,
    [Inspection]         NVARCHAR (MAX)  NULL,
    [FootNoteId]         INT             NULL,
    [UploadDocuments]    NVARCHAR (MAX)  NULL,
    [Remarks]            NVARCHAR (MAX)  NULL,
    [POStatus]           BIT             DEFAULT ((0)) NOT NULL,
    [ClientId]           INT             CONSTRAINT [DF_PurchaseOrders_ClientId] DEFAULT ((0)) NOT NULL,
    
    [PreparedByUserId]   INT             NULL,
    [PreparedDate]       DATETIME        NULL,
    [VerifiedByUserId]   INT             NULL,
    [VerifiedDate]       DATETIME        NULL,
    [AuthorizedByUserId] INT             NULL,
    [AuthorizedDate]     DATETIME        NULL,
    [ModifiedByUserId]   INT             NULL,
    [ModifiedDate]       DATETIME        NULL,
    [CancelledByUserId]  INT             NULL,
    [CancelledDate]      DATETIME        NULL,
    [AuthorizedStatus]   BIT             DEFAULT ((0)) NOT NULL,
    
    CONSTRAINT [PK_PurchaseOrders] PRIMARY KEY CLUSTERED ([PurchaseOrderId] ASC),
    CONSTRAINT [FK_PurchaseOrders_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_PurchaseOrders_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseOrders_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseOrders_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseOrders_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseOrders_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseOrders_Vendors] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendors] ([VendorId]),
    CONSTRAINT [FK_PurchaseOrders_PaymentTerms] FOREIGN KEY ([PaymentTermsId]) REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
    CONSTRAINT [FK_PurchaseOrders_SupplyTypes] FOREIGN KEY ([SupplyTypeId]) REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_PurchaseOrders_HeaderNote] FOREIGN KEY ([HeaderNoteId]) REFERENCES [dbo].[HeaderNote] ([HeaderNoteId]),
    CONSTRAINT [FK_PurchaseOrders_FootNotes] FOREIGN KEY ([FootNoteId]) REFERENCES [dbo].[FootNotes] ([FootNoteId]),
    CONSTRAINT [FK_PurchaseOrders_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_PurchaseOrders_TDSRates] FOREIGN KEY ([TDSRateId]) REFERENCES [dbo].[TDSRates] ([TDSRateId]),
    CONSTRAINT [FK_PurchaseOrders_TCSRates] FOREIGN KEY ([TCSRateId]) REFERENCES [dbo].[TCSRates] ([TCSRateId]),
    CONSTRAINT [FK_PurchaseOrders_DeliveryCities] FOREIGN KEY ([DeliveryCityId]) REFERENCES [dbo].[Cities] ([CityId])
);


GO
CREATE NONCLUSTERED INDEX [SupplyTypes]
    ON [dbo].[PurchaseOrders]([SupplyTypeId] ASC);


GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[PurchaseOrders]([VendorId] ASC);


GO
CREATE NONCLUSTERED INDEX [PurchaseOrderDate]
    ON [dbo].[PurchaseOrders]([PurchaseOrderDate] ASC);


GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[PurchaseOrders]([FinancialYearId] ASC);


GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[PurchaseOrders]([ClientId] ASC);

