using Microsoft.AspNetCore.Hosting;
using Serenity.Reporting;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;

namespace ReconnBooks.Modules.Common.Reporting;

public class ReconnHtmlToPdfLauncher(IHtmlToPdfOptions options = null, IWebHostEnvironment webHostEnvironment = null) : PuppeteerHtmlToPdf(options)
{
    protected override async Task<string> GetExecutablePathAsync()
    {
        if (!string.IsNullOrEmpty(ExecutablePath))
        {
            if (!File.Exists(ExecutablePath))
                throw new InvalidOperationException(string.Format(CultureInfo.CurrentCulture,
                    "Can't find Chrome/Firefox executable which is required for PDF generation at the specified location: {0}",
                    Path.GetDirectoryName(ExecutablePath)));

            return ExecutablePath;
        }

        var revision = await DownloadBrowserAsync();

        var executablePath = new PhysicalFileSystem().Combine(
                            webHostEnvironment?.ContentRootPath,
                            "App_Data", "chrome", "ChromeHeadlessShell",
                            $"{revision.Platform}-{revision.BuildId}", $"chrome-headless-shell-{revision.Platform.GetName().ToLower()}",
                            "chrome-headless-shell.exe"
        );

        return executablePath;
    }
}
