﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.CitiesRow>;
using MyRow = ReconnBooks.Default.CitiesRow;

namespace ReconnBooks.Default;

public interface ICitiesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class CitiesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, ICitiesListHandler
{
    public CitiesListHandler(IRequestContext context)
            : base(context)
    {
    }
}