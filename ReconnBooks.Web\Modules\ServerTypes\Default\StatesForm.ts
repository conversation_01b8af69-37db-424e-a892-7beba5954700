﻿import { StringEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CountriesDialog } from "../../Default/Countries/CountriesDialog";

export interface StatesForm {
    StateName: StringEditor;
    StateCode: StringEditor;
    StateCodeNo: StringEditor;
    CountryId: ServiceLookupEditor;
}

export class StatesForm extends PrefixedContext {
    static readonly formKey = 'Default.States';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!StatesForm.init)  {
            StatesForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;

            initFormType(StatesForm, [
                'StateName', w0,
                'StateCode', w0,
                'StateCodeNo', w0,
                'CountryId', w1
            ]);
        }
    }
}

queueMicrotask(() => [CountriesDialog]); // referenced dialogs