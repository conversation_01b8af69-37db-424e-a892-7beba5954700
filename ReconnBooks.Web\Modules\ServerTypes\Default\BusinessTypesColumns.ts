﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { BusinessTypesRow } from "./BusinessTypesRow";

export interface BusinessTypesColumns {
    RowNumber: Column<BusinessTypesRow>;
    BusinessTypeId: Column<BusinessTypesRow>;
    BusinessType: Column<BusinessTypesRow>;
    Description: Column<BusinessTypesRow>;
}

export class BusinessTypesColumns extends ColumnsBase<BusinessTypesRow> {
    static readonly columnsKey = 'Default.BusinessTypes';
    static readonly Fields = fieldsProxy<BusinessTypesColumns>();
}