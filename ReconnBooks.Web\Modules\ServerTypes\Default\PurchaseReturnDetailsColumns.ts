﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { PurchaseReturnDetailsRow } from "./PurchaseReturnDetailsRow";

export interface PurchaseReturnDetailsColumns {
    RowNumber: Column<PurchaseReturnDetailsRow>;
    PurchaseReturnNo: Column<PurchaseReturnDetailsRow>;
    CommodityName: Column<PurchaseReturnDetailsRow>;
    CommodityCode: Column<PurchaseReturnDetailsRow>;
    CommodityType: Column<PurchaseReturnDetailsRow>;
    CommodityDescription: Column<PurchaseReturnDetailsRow>;
    RejectedQuantity: Column<PurchaseReturnDetailsRow>;
    UnitName: Column<PurchaseReturnDetailsRow>;
    RejectedAmount: Column<PurchaseReturnDetailsRow>;
    RejectedItemSerialNo: Column<PurchaseReturnDetailsRow>;
    RejectionReason: Column<PurchaseReturnDetailsRow>;
    AssessmentRemarks: Column<PurchaseReturnDetailsRow>;
    PurchaseOrderDetailCommodityDescription: Column<PurchaseReturnDetailsRow>;
    GRNDetailCommodityDescription: Column<PurchaseReturnDetailsRow>;
    ReplacementMethod: Column<PurchaseReturnDetailsRow>;
    Remarks: Column<PurchaseReturnDetailsRow>;
    PurchaseReturnDetailId: Column<PurchaseReturnDetailsRow>;
}

export class PurchaseReturnDetailsColumns extends ColumnsBase<PurchaseReturnDetailsRow> {
    static readonly columnsKey = 'Default.PurchaseReturnDetails';
    static readonly Fields = fieldsProxy<PurchaseReturnDetailsColumns>();
}