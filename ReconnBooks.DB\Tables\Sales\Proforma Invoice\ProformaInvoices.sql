﻿CREATE TABLE [dbo].[ProformaInvoices] 
(
    [ProformaInvoiceId]         INT             NOT NULL    IDENTITY (1,1),
    [ProformaInvoiceNo]         NVARCHAR (50)   NOT NULL,
    [ProformaInvoiceDate]       DATETIME            NULL,
    [CustomerId]                INT             NOT NULL,
    [SupplyTypeId]              INT             NOT NULL,

    [QuotationId]               INT                 NULL,
    [SalesOrderId]              INT                 NULL,
    [OrderRefNo]                NVARCHAR (50)       NULL,       --Customer Order reference No.
    [OrderRefDate]              SMALLDATETIME       NULL,       --Customer Order reference Date.
    
    [ProformaInvoiceAmt]        DECIMAL (18, 2)     NULL,
    [RoundingOff]               DECIMAL (18, 2)     NULL,
    [GrandTotal]                DECIMAL (18, 2)     NULL,

    [DeliveryNoteId]            INT                 NULL,
    [PaymentTermsId]            INT                 NULL,
    [PaymentDueDate]            DATETIME            NULL,

    [DeliveryAddress]           NVARCHAR (250)      NULL,
    [DeliveryCityId]            INT                 NULL,
    [DeliveryPinCode]           INT                 NULL,
    [ShippedVia]                NVARCHAR (250)      NULL,
    [DocketNo]                  NVARCHAR (50)       NULL,
    [VehicleNo]                 NVARCHAR (20)       NULL,
    [UploadFiles]               NVARCHAR (MAX)      NULL,
    [Inspection]                NVARCHAR (100)      NULL,
    [FinancialYearId]           INT                 NULL,
    [Remarks]                   NVARCHAR (MAX)      NULL,
    [ClientId]                  INT	            NOT NULL    CONSTRAINT [DF_ProformaInvoices_ClientId]	DEFAULT ((0)),
    -------------------Authorization Details-------------
    [PreparedByUserId]          INT	                NULL,
    [PreparedDate]              DATETIME            NULL,
    [VerifiedByUserId]          INT                 NULL,
    [VerifiedDate]              DATETIME            NULL,
    [AuthorizedByUserId]        INT                 NULL,
    [AuthorizedDate]            DATETIME            NULL,
    [ModifiedByUserId]          INT                 NULL,
    [ModifiedDate]              DATETIME            NULL,
    [CancelledByUserId]         INT                 NULL,
    [CancelledDate]			    DATETIME            NULL,
    [AuthorizedStatus]          BIT             NOT NULL    DEFAULT ((0)),
    
    CONSTRAINT [PK_ProformaInvoices] PRIMARY KEY CLUSTERED ([ProformaInvoiceId] ASC),
    CONSTRAINT [FK_ProformaInvoices_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_ProformaInvoices_DeliveryNotes] FOREIGN KEY ([DeliveryNoteId]) REFERENCES [dbo].[DeliveryNotes] ([DeliveryNoteId]),
    CONSTRAINT [FK_ProformaInvoices_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_ProformaInvoices_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_ProformaInvoices_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_ProformaInvoices_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_ProformaInvoices_Quotations] FOREIGN KEY ([QuotationId]) REFERENCES [dbo].[Quotations] ([QuotationId]),
    CONSTRAINT [FK_ProformaInvoices_SupplyTypes] FOREIGN KEY ([SupplyTypeId]) REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_ProformaInvoices_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_ProformaInvoices_PaymentTerms] FOREIGN KEY ([PaymentTermsId]) REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
    CONSTRAINT [FK_ProformaInvoices_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_ProformaInvoices_DeliveryCities] FOREIGN KEY ([DeliveryCityId]) REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_ProformaInvoices_Customers] FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[Customers] ([CustomerId])
);

GO
CREATE NONCLUSTERED INDEX [SupplyTypes]
    ON [dbo].[ProformaInvoices]([SupplyTypeId] ASC);


GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[ProformaInvoices]([CustomerId] ASC);


GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[ProformaInvoices]([FinancialYearId] ASC);


GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[ProformaInvoices]([ClientId] ASC);

