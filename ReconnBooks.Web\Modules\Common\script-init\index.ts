import { Culture, getjQuery } from "@serenity-is/corelib";
import "./colorbox-init";
import "./errorhandling-init";
import "./flatpickr-init";
import "./idletimeout-init";
import "./languages-init";
import "./namespaces-init";
import "./sleekgrid-init";
import { ClientUserAssociationDialog } from "../../Default/ClientUsers/ClientUserAssociationDialog";



Culture.dateOrder = 'dmy';
Culture.dateFormat = 'dd/MM/yyyy';
Culture.dateSeparator = '-';

//let $ = getjQuery();
//$.datepicker.setDefaults({
//    dateFormat: 'dd-mm-yy'
//});


if (document.getElementById('ChangeClient')) {
    document.getElementById('ChangeClient').addEventListener('click', () => {
        new ClientUserAssociationDialog().loadNewAndOpenDialog();
    });
}

// ========================
// Sidebar Height Adjustment
// ========================
document.addEventListener('DOMContentLoaded', function () {
    function updateSidebarHeight() {
        requestAnimationFrame(() => {
            setTimeout(() => {
                const bottomBar = document.getElementById('s-sidebar-user-info');
                const topSection = document.getElementById('s-sidebar-top');

                if (bottomBar && topSection) {
                    const bottomBarHeight = bottomBar.getBoundingClientRect().height;
                    const viewportHeight = window.visualViewport ? window.visualViewport.height : window.innerHeight;
                    const isFullScreen = window.innerWidth > 1200;

                    let availableHeight = isFullScreen
                        ? viewportHeight - bottomBarHeight
                        : viewportHeight - (bottomBarHeight + 45);

                    availableHeight = Math.max(availableHeight, 150);

                    topSection.style.height = `${availableHeight}px`;
                    topSection.style.overflowY = 'auto';
                }
            }, 50);
        });
    }

    window.addEventListener('load', updateSidebarHeight);
    window.addEventListener('resize', updateSidebarHeight);
    window.addEventListener('orientationchange', updateSidebarHeight);
    if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', updateSidebarHeight);
    }
});
