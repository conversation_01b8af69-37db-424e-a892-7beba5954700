﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ProformaInvoiceDetailsRow } from "./ProformaInvoiceDetailsRow";

export namespace ProformaInvoiceDetailsService {
    export const baseUrl = 'Default/ProformaInvoiceDetails';

    export declare function Create(request: SaveRequest<ProformaInvoiceDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProformaInvoiceDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProformaInvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProformaInvoiceDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProformaInvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProformaInvoiceDetailsRow>>;

    export const Methods = {
        Create: "Default/ProformaInvoiceDetails/Create",
        Update: "Default/ProformaInvoiceDetails/Update",
        Delete: "Default/ProformaInvoiceDetails/Delete",
        Retrieve: "Default/ProformaInvoiceDetails/Retrieve",
        List: "Default/ProformaInvoiceDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ProformaInvoiceDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}