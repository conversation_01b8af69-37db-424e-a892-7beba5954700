import { TodoItem } from "./todo-item";
import { TodoList } from "./todo-list";
import { Todo } from "./todo-types";

declare global {
    interface Window {
        todoData: Todo[];
    }
}

function loadTodos(): Todo[] {
    let stored = localStorage.getItem("todos");
    if (!stored) return [];
    try {
        const raw = JSON.parse(stored);
        return raw.map((t: any) => ({
            text: t.text,
            done: t.done,
            createdAt: new Date(t.createdAt)
        }));
    } catch {
        return [];
    }
}

window.todoData = loadTodos();

export const TodoCard = () => {
    let input: HTMLInputElement;
    let listContainer: HTMLDivElement;

    function addTask(text: string) {
        const newTodo: Todo = {
            text,
            createdAt: new Date(),
            done: false
        };
        window.todoData.push(newTodo);
        localStorage.setItem("todos", JSON.stringify(window.todoData));

        // Re-render the list
        listContainer.replaceChildren(TodoList({ todos: window.todoData }));
    }

    return <div class="card s-dashboard-card s-dashboard-todo">
        <div class="card-header pb-0">
            <h3 class="card-title">Tasks</h3>
        </div>
        <div class="card-body" ref={el => {
            listContainer = el!;
            listContainer.replaceChildren(TodoList({ todos: window.todoData }));
        }} />
        <div class="card-footer pt-0">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="type another task"
                    ref={el => input = el!}
                    onKeyDown={e => {
                        if (e.key === "Enter") {
                            const text = input.value?.trim();
                            if (text?.length) {
                                addTask(text);
                                input.value = '';
                            }
                        }
                    }} />
                <button class="btn btn-primary" onClick={() => {
                    const text = input.value?.trim();
                    if (text?.length) {
                        addTask(text);
                        input.value = '';
                    }
                }}>Add</button>
            </div>
        </div>
    </div>;
};
