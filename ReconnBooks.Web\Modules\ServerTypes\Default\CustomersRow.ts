﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { CustomerContactsRow } from "./CustomerContactsRow";

export interface CustomersRow {
    RowNumber?: number;
    CustomerId?: number;
    CompanyName?: string;
    AddressedToId?: number;
    CompanyCode?: string;
    GSTIN?: string;
    PlaceOfSupplyId?: number;
    NatureOfSupplyId?: number;
    SupplyTypeId?: number;
    PAN?: string;
    IECNo?: string;
    BillingAddress?: string;
    BillingCityId?: number;
    BillingPinCode?: string;
    MailingAddress?: string;
    MailingCityId?: number;
    MailingPINCode?: string;
    PhoneNo?: string;
    EMailId?: string;
    FaxNo?: string;
    MobileNo?: string;
    HomePage?: string;
    UdyamNo?: string;
    CustomerContactsList?: CustomerContactsRow[];
    BankId?: number;
    BranchName?: string;
    AccountName?: string;
    AccountNumber?: string;
    IFSCCode?: string;
    BranchCode?: string;
    ClientId?: number;
    AddressedTo?: string;
    PlaceOfSupplyStateName?: string;
    PlaceOfSupplyStateCode?: string;
    PlaceOfSupplyStateCodeNo?: string;
    NatureOfSupply?: string;
    SupplyType?: string;
    BillingCityCityName?: string;
    MailingCityCityName?: string;
    BankName?: string;
    ClientName?: string;
}

export abstract class CustomersRow {
    static readonly idProperty = 'CustomerId';
    static readonly nameProperty = 'CompanyName';
    static readonly localTextPrefix = 'Default.Customers';
    static readonly lookupKey = 'Default.Customers';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CustomersRow>('Default.Customers') }
    static async getLookupAsync() { return getLookupAsync<CustomersRow>('Default.Customers') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CustomersRow>();
}