﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ProductGroupsRow {
    RowNumber?: number;
    ProductGroupId?: number;
    ProductGroup?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class ProductGroupsRow {
    static readonly idProperty = 'ProductGroupId';
    static readonly nameProperty = 'ProductGroup';
    static readonly localTextPrefix = 'Default.ProductGroups';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProductGroupsRow>();
}