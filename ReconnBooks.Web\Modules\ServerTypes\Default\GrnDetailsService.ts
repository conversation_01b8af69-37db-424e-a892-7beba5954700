﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GrnDetailsRow } from "./GrnDetailsRow";

export namespace GrnDetailsService {
    export const baseUrl = 'Default/GrnDetails';

    export declare function Create(request: SaveRequest<GrnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GrnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GrnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GrnDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GrnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GrnDetailsRow>>;

    export const Methods = {
        Create: "Default/GrnDetails/Create",
        Update: "Default/GrnDetails/Update",
        Delete: "Default/GrnDetails/Delete",
        Retrieve: "Default/GrnDetails/Retrieve",
        List: "Default/GrnDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>GrnDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}