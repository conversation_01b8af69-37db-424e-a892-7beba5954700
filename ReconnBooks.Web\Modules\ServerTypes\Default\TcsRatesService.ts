﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { TcsRatesRow } from "./TcsRatesRow";

export namespace TcsRatesService {
    export const baseUrl = 'Default/TcsRates';

    export declare function Create(request: SaveRequest<TcsRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<TcsRatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<TcsRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<TcsRatesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<TcsRatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<TcsRatesRow>>;

    export const Methods = {
        Create: "Default/TcsRates/Create",
        Update: "Default/TcsRates/Update",
        Delete: "Default/TcsRates/Delete",
        Retrieve: "Default/TcsRates/Retrieve",
        List: "Default/TcsRates/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>TcsRatesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}