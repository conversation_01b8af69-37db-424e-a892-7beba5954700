﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.ClientBankAccountsRow>;
using MyRow = ReconnBooks.Default.ClientBankAccountsRow;

namespace ReconnBooks.Default;

public interface IClientBankAccountsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientBankAccountsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IClientBankAccountsRetrieveHandler
{
    public ClientBankAccountsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}