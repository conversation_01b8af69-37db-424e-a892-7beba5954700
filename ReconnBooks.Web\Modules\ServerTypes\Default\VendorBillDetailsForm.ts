﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, LookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";

export interface VendorBillDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    ProductSerialNos: StringEditor;
    SKU: StringEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    BillQuantity: DecimalEditor;
    UnitId: LookupEditor;
    UnitPrice: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmount: DecimalEditor;
    UnitAmount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: StringEditor;
    NetPricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
    PurchaseOrderDetailId: LookupEditor;
}

export class VendorBillDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.VendorBillDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!VendorBillDetailsForm.init)  {
            VendorBillDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;
            var w5 = LookupEditor;

            initFormType(VendorBillDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'ProductSerialNos', w3,
                'SKU', w3,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'BillQuantity', w4,
                'UnitId', w5,
                'UnitPrice', w4,
                'DiscountPercent', w4,
                'DiscountAmount', w4,
                'UnitAmount', w4,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w4,
                'NetTaxableAmount', w4,
                'IGSTRate', w4,
                'IGSTAmountPerUnit', w4,
                'NetIGSTAmount', w4,
                'CGSTRate', w4,
                'CGSTAmountPerUnit', w4,
                'NetCGSTAmount', w4,
                'SGSTRate', w4,
                'SGSTAmountPerUnit', w4,
                'NetSGSTAmount', w4,
                'DummyField', w3,
                'NetPricePerUnit', w4,
                'NetAmount', w4,
                'PurchaseOrderDetailId', w5
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog]); // referenced dialogs