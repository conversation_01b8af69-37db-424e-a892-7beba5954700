﻿CREATE TABLE [dbo].[Warehouses]
(
    [WarehouseId]   INT             NOT NULL    IDENTITY (1, 1),
    [WarehouseName] NVARCHAR (200)  NOT NULL,
    [Description]   NVARCHAR (500)      NULL,
    [LocationId]    INT             NOT NULL,
    [Remarks]       NVARCHAR (MAX)      NULL,
    [Discontinued]  BIT             NOT NULL    CONSTRAINT [DF_Warehouses_Discontinued]   DEFAULT ((0)),
    [ClientId]      INT             NOT NULL    CONSTRAINT [DF_Warehouses_ClientId]       DEFAULT ((0)),
   
   CONSTRAINT [PK_Warehouses]   PRIMARY KEY CLUSTERED ([WarehouseId] ASC),
   CONSTRAINT [FK_Warehouses_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
   CONSTRAINT [FK_Warehouses_Locations] FOREIGN KEY ([LocationId]) REFERENCES [dbo].[Locations] ([LocationId])

);
