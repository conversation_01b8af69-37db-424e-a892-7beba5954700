﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.BanksRow;

namespace ReconnBooks.Default;

public interface IBanksDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class BanksDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IBanksDeleteHandler
{
    public BanksDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}