using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.BusinessTypes")]
[BasedOnRow(typeof(BusinessTypesRow), CheckNames = true)]
public class BusinessTypesColumns
{
    public long RowNumber { get; set; }
    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int BusinessTypeId { get; set; }

    [EditLink, Width(200)]
    public string BusinessType { get; set; }

    [EditLink, Width(300)]
    public string Description { get; set; }
}