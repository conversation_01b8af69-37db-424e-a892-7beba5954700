﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface DistrictsRow {
    RowNumber?: number;
    DistrictId?: number;
    District?: string;
    DistrictCode?: string;
    Headquarters?: string;
    StateId?: number;
    StateName?: string;
}

export abstract class DistrictsRow {
    static readonly idProperty = 'DistrictId';
    static readonly nameProperty = 'District';
    static readonly localTextPrefix = 'Default.Districts';
    static readonly lookupKey = 'Default.Districts';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<DistrictsRow>('Default.Districts') }
    static async getLookupAsync() { return getLookupAsync<DistrictsRow>('Default.Districts') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DistrictsRow>();
}