﻿import { StringEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { AddressedToDialog } from "../../Default/AddressedTo/AddressedToDialog";
import { BanksDialog } from "../../Default/Banks/BanksDialog";
import { CitiesDialog } from "../../Default/Cities/CitiesDialog";
import { CustomerContactsGridEditor } from "../../Default/CustomerContacts/CustomerContactsGridEditor";

export interface CustomersForm {
    CompanyName: StringEditor;
    CompanyCode: StringEditor;
    AddressedToId: ServiceLookupEditor;
    BillingAddress: StringEditor;
    BillingCityId: ServiceLookupEditor;
    BillingPinCode: StringEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyId: ServiceLookupEditor;
    NatureOfSupplyId: ServiceLookupEditor;
    SupplyTypeId: ServiceLookupEditor;
    PAN: StringEditor;
    IECNo: StringEditor;
    UdyamNo: StringEditor;
    PhoneNo: StringEditor;
    MobileNo: StringEditor;
    EMailId: StringEditor;
    FaxNo: StringEditor;
    HomePage: StringEditor;
    MailingAddress: StringEditor;
    MailingCityId: ServiceLookupEditor;
    MailingPINCode: StringEditor;
    CustomerContactsList: CustomerContactsGridEditor;
    BankId: ServiceLookupEditor;
    BranchName: StringEditor;
    AccountName: StringEditor;
    AccountNumber: StringEditor;
    IFSCCode: StringEditor;
    BranchCode: StringEditor;
}

export class CustomersForm extends PrefixedContext {
    static readonly formKey = 'Default.Customers';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CustomersForm.init)  {
            CustomersForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = CustomerContactsGridEditor;

            initFormType(CustomersForm, [
                'CompanyName', w0,
                'CompanyCode', w0,
                'AddressedToId', w1,
                'BillingAddress', w0,
                'BillingCityId', w1,
                'BillingPinCode', w0,
                'GSTIN', w0,
                'PlaceOfSupplyId', w1,
                'NatureOfSupplyId', w1,
                'SupplyTypeId', w1,
                'PAN', w0,
                'IECNo', w0,
                'UdyamNo', w0,
                'PhoneNo', w0,
                'MobileNo', w0,
                'EMailId', w0,
                'FaxNo', w0,
                'HomePage', w0,
                'MailingAddress', w0,
                'MailingCityId', w1,
                'MailingPINCode', w0,
                'CustomerContactsList', w2,
                'BankId', w1,
                'BranchName', w0,
                'AccountName', w0,
                'AccountNumber', w0,
                'IFSCCode', w0,
                'BranchCode', w0
            ]);
        }
    }
}

queueMicrotask(() => [AddressedToDialog, CitiesDialog, BanksDialog]); // referenced dialogs