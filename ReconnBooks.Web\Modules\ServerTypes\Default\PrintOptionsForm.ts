﻿import { BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface PrintOptionsForm {
    Original: BooleanEditor;
    Duplicate: BooleanEditor;
    Triplicate: BooleanEditor;
    TransporterCopy: BooleanEditor;
    SupplierCopy: BooleanEditor;
    ExtraCopy: BooleanEditor;
}

export class PrintOptionsForm extends PrefixedContext {
    static readonly formKey = 'Default.PrintOptions';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PrintOptionsForm.init)  {
            PrintOptionsForm.init = true;

            var w0 = BooleanEditor;

            initFormType(PrintOptionsForm, [
                'Original', w0,
                'Duplicate', w0,
                'Triplicate', w0,
                'TransporterCopy', w0,
                'SupplierCopy', w0,
                'ExtraCopy', w0
            ]);
        }
    }
}