﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { HsnsacCodesRow } from "./HsnsacCodesRow";

export interface HsnsacCodesColumns {
    RowNumber: Column<HsnsacCodesRow>;
    HSNSACCode: Column<HsnsacCodesRow>;
    HSNSACDescription: Column<HsnsacCodesRow>;
    HSNSACGroup: Column<HsnsacCodesRow>;
    GSTRateRemarks: Column<HsnsacCodesRow>;
    HSNSACCodeId: Column<HsnsacCodesRow>;
}

export class HsnsacCodesColumns extends ColumnsBase<HsnsacCodesRow> {
    static readonly columnsKey = 'Default.HsnsacCodes';
    static readonly Fields = fieldsProxy<HsnsacCodesColumns>();
}