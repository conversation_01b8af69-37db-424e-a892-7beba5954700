﻿import { DecimalEditor, DateEditor, BooleanEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface GstRatesForm {
    IGSTPercent: DecimalEditor;
    IGSTCessPercent: DecimalEditor;
    CGSTPercent: DecimalEditor;
    CGSTCessPercent: DecimalEditor;
    SGSTPercent: DecimalEditor;
    SGSTCessPercent: DecimalEditor;
    WefDate: DateEditor;
    Current: BooleanEditor;
    Remarks: TextAreaEditor;
}

export class GstRatesForm extends PrefixedContext {
    static readonly formKey = 'Default.GstRates';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!GstRatesForm.init)  {
            GstRatesForm.init = true;

            var w0 = DecimalEditor;
            var w1 = DateEditor;
            var w2 = BooleanEditor;
            var w3 = TextAreaEditor;

            initFormType(GstRatesForm, [
                'IGSTPercent', w0,
                'IGSTCessPercent', w0,
                'CGSTPercent', w0,
                'CGSTCessPercent', w0,
                'SGSTPercent', w0,
                'SGSTCessPercent', w0,
                'WefDate', w1,
                'Current', w2,
                'Remarks', w3
            ]);
        }
    }
}