using Microsoft.AspNetCore.Http;
using ReconnBooks.Administration;
using ReconnBooks.Default;
using ReconnBooks.Default.Endpoints;
using ReconnBooks.Modules.Common.Navigation;
using Serenity.Navigation;
using System.Net;

namespace ReconnBooks.AppServices;

public interface ISidebarModelFactory
{
    ISidebarModel Create();
}

public class SidebarModelFactory : ISidebarModelFactory
{
    private readonly INavigationModelFactory _navigationModelFactory;
    private readonly ISqlConnections _sqlConnections;
    private readonly IUserAccessor _userAccessor;
    private readonly IUserRetrieveService _userRetrieveService;
    private readonly IClientUsersListHandler clientUsersListhandler;
    private readonly IPermissionService permissions;
    private readonly IHttpContextAccessor httpContextAccessor;

    public SidebarModelFactory(INavigationModelFactory navigationModelFactory,
        ISqlConnections sqlConnections,
        IUserAccessor userAccessor,
        IUserRetrieveService userRetrieveService,
        IClientUsersListHandler clientUsersListhandler,
        IPermissionService permissions, 
        IHttpContextAccessor httpContextAccessor)
    {
        _navigationModelFactory = navigationModelFactory;
        _sqlConnections = sqlConnections;
        _userAccessor = userAccessor;
        _userRetrieveService = userRetrieveService;
        this.clientUsersListhandler = clientUsersListhandler;
        this.permissions = permissions;
        this.httpContextAccessor = httpContextAccessor;
    }

    public ISidebarModel Create()
    {
        // Use the NavigationModelFactory to build the base navigation model
        var navigationModel = _navigationModelFactory.Create();
        //todo: call ClientUserEndpoint's ActiveClient
        var sidebarModel = new SidebarModel
        {
            NavigationModel = navigationModel
        };

        if (permissions.HasPermission(PermissionKeys.Consultants.User))
        {
            var clientUserEndpoint = new ClientUsersEndpoint(_sqlConnections, _userAccessor, _userRetrieveService);
            var activeClient = clientUserEndpoint.ActiveClient(clientUsersListhandler);

            sidebarModel.ActiveClient = new ClientDetails
            {
                ClientId = activeClient.ClientId,
                ClientName = activeClient.ClientName,
                ClientCode = activeClient.ClientCode,
                ClientLogo = activeClient.ClientLogo
            };
        }
        

        if (_userAccessor.User?.GetUserDefinition(_userRetrieveService) is UserDefinition user)
        {
            if(user.ConsultantId != null)
            {
                sidebarModel.UserCompanyName = user.ConsultantName;
                sidebarModel.UserCompanyLogo = user.ConsultantLogo;
            }
            else
            {
                sidebarModel.UserCompanyName = user.ClientName;
                sidebarModel.UserCompanyLogo = user.ClientLogo;
            }
            sidebarModel.UserDisplayName = user.DisplayName;
            sidebarModel.UserLogo = user.UserImage;

            string userCompanyName = sidebarModel.UserCompanyName ?? string.Empty;
            string userDisplayName = sidebarModel.UserDisplayName ?? string.Empty;
            string userLogo = sidebarModel.UserLogo ?? string.Empty;
            string clientName = user.ClientName ?? string.Empty;
            
            // Function to extract initials
            string GetInitials(string name)
            {
                string initials = string.Empty;

                var words = name.Split(' ')
                                .Where(w => !string.IsNullOrEmpty(w) && char.IsLetter(w[0]))
                                .ToList();

                if (words.Count > 0)
                {
                    initials += words[0][0];

                    if (words.Count > 1)
                    {
                        if (words[1].Length == 1 && words.Count > 2)
                        {
                            initials += words[2][0];
                        }
                        else
                        {
                            initials += words[1][0];
                        }
                    }
                }

                return initials.ToUpper();
            }

            // Assigning initials
            sidebarModel.UserCompanyNameInitials = GetInitials(userCompanyName);
            sidebarModel.UserDisplayNameInitials = GetInitials(userDisplayName);
            sidebarModel.ClientNameInitials = GetInitials(clientName);

            // Assigning colors
            HashSet<string> usedColors = new HashSet<string>();

            sidebarModel.UserCompanyColorCode = new ColorCodingForUserCompanyName(httpContextAccessor).GenerateColorCode(userCompanyName, usedColors);
            sidebarModel.UserDisplayNameColorCode = new ColorCodingForUserCompanyName(httpContextAccessor).GenerateColorCode(userDisplayName, usedColors);
            sidebarModel.ClientNameColorCode = new ColorCodingForUserCompanyName(httpContextAccessor).GenerateColorCode(clientName, usedColors);
        }
        return sidebarModel;
    }
}
