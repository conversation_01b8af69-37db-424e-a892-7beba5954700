﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ProductMakeRow } from "./ProductMakeRow";

export namespace ProductMakeService {
    export const baseUrl = 'Default/ProductMake';

    export declare function Create(request: SaveRequest<ProductMakeRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProductMakeRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProductMakeRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProductMakeRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProductMakeRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProductMakeRow>>;

    export const Methods = {
        Create: "Default/ProductMake/Create",
        Update: "Default/ProductMake/Update",
        Delete: "Default/ProductMake/Delete",
        Retrieve: "Default/ProductMake/Retrieve",
        List: "Default/ProductMake/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ProductMakeService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}