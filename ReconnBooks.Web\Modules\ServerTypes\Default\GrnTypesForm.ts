﻿import { StringEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface GrnTypesForm {
    GRNTypeName: StringEditor;
    Description: TextAreaEditor;
}

export class GrnTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.GrnTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!GrnTypesForm.init)  {
            GrnTypesForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;

            initFormType(GrnTypesForm, [
                'GRNTypeName', w0,
                'Description', w1
            ]);
        }
    }
}