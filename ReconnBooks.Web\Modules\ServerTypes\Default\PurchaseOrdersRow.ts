﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { PurchaseOrderDetailsRow } from "./PurchaseOrderDetailsRow";

export interface PurchaseOrdersRow {
    RowNumber?: number;
    PurchaseOrderId?: number;
    PurchaseOrderNo?: string;
    PurchaseOrderDate?: string;
    PurchaseOrderMonth?: string;
    VendorId?: number;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    VendorEMailId?: string;
    PlaceOfSupplyStateName?: string;
    ShipToCustomerId?: number;
    CustomerCompanyName?: string;
    ShipToCustomerName?: string;
    ShippingAddress?: string;
    ShippingCityName?: string;
    ShippingPinCode?: string;
    ShippingGSTIN?: string;
    ShippingPlaceOfSupplyStateName?: string;
    SupplyTypeId?: number;
    SupplyType?: string;
    FinancialYearId?: number;
    ReferenceNo?: string;
    ReferenceDate?: string;
    HeaderNoteId?: number;
    PurchaseOrderDetailsList?: PurchaseOrderDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    POAmount?: number;
    RoundingOff?: number;
    GrandTotal?: number;
    TDSRateId?: number;
    TCSRateId?: number;
    TDSAmount?: number;
    TCSAmount?: number;
    DeliveryDueDate?: string;
    PaymentTermsId?: number;
    PaymentDueDate?: string;
    Inspection?: string;
    Taxes?: string;
    FootNoteId?: number;
    UploadDocuments?: string;
    Remarks?: string;
    POStatus?: boolean;
    ClientId?: number;
    PreparedByUserId?: number;
    ClientName?: string;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    VendorName?: string;
    FinancialYearName?: string;
    HeaderNote?: string;
    PaymentTerms?: string;
    FootNote?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class PurchaseOrdersRow {
    static readonly idProperty = 'PurchaseOrderId';
    static readonly nameProperty = 'PurchaseOrderNo';
    static readonly localTextPrefix = 'Default.PurchaseOrders';
    static readonly lookupKey = 'Default.PurchaseOrders';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PurchaseOrdersRow>('Default.PurchaseOrders') }
    static async getLookupAsync() { return getLookupAsync<PurchaseOrdersRow>('Default.PurchaseOrders') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PurchaseOrdersRow>();
}