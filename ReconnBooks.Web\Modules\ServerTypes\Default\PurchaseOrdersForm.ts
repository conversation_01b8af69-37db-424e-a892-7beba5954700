﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, BooleanEditor, MultipleImageUploadEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { HeaderNoteDialog } from "../../Default/HeaderNote/HeaderNoteDialog";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { PurchaseOrderDetailsGridEditor } from "../../Default/PurchaseOrderDetails/PurchaseOrderDetailsGridEditor";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface PurchaseOrdersForm {
    PurchaseOrderNo: StringEditor;
    PurchaseOrderDate: DateEditor;
    VendorId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    ReferenceNo: StringEditor;
    ReferenceDate: DateEditor;
    HeaderNoteId: ServiceLookupEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    ShipToCustomerId: ServiceLookupEditor;
    ShippingAddress: StringEditor;
    ShippingCityName: StringEditor;
    ShippingPinCode: StringEditor;
    ShippingGSTIN: StringEditor;
    ShippingPlaceOfSupplyStateName: StringEditor;
    PurchaseOrderDetailsList: PurchaseOrderDetailsGridEditor;
    TDSRateId: ServiceLookupEditor;
    TDSAmount: DecimalEditor;
    POAmount: DecimalEditor;
    TCSRateId: ServiceLookupEditor;
    TCSAmount: DecimalEditor;
    RoundingOff: DecimalEditor;
    DeliveryDueDate: DateEditor;
    PaymentDueDate: DateEditor;
    GrandTotal: DecimalEditor;
    PaymentTermsId: LookupEditor;
    Taxes: StringEditor;
    Inspection: StringEditor;
    Remarks: TextAreaEditor;
    FootNoteId: ServiceLookupEditor;
    POStatus: BooleanEditor;
    AuthorizedStatus: BooleanEditor;
    UploadDocuments: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class PurchaseOrdersForm extends PrefixedContext {
    static readonly formKey = 'Default.PurchaseOrders';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PurchaseOrdersForm.init)  {
            PurchaseOrdersForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = PurchaseOrderDetailsGridEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = BooleanEditor;
            var w8 = MultipleImageUploadEditor;
            var w9 = DateTimeEditor;

            initFormType(PurchaseOrdersForm, [
                'PurchaseOrderNo', w0,
                'PurchaseOrderDate', w1,
                'VendorId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'ReferenceNo', w0,
                'ReferenceDate', w1,
                'HeaderNoteId', w2,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'ShipToCustomerId', w2,
                'ShippingAddress', w0,
                'ShippingCityName', w0,
                'ShippingPinCode', w0,
                'ShippingGSTIN', w0,
                'ShippingPlaceOfSupplyStateName', w0,
                'PurchaseOrderDetailsList', w4,
                'TDSRateId', w2,
                'TDSAmount', w5,
                'POAmount', w5,
                'TCSRateId', w2,
                'TCSAmount', w5,
                'RoundingOff', w5,
                'DeliveryDueDate', w1,
                'PaymentDueDate', w1,
                'GrandTotal', w5,
                'PaymentTermsId', w3,
                'Taxes', w0,
                'Inspection', w0,
                'Remarks', w6,
                'FootNoteId', w2,
                'POStatus', w7,
                'AuthorizedStatus', w7,
                'UploadDocuments', w8,
                'PreparedByUserId', w3,
                'PreparedDate', w9,
                'VerifiedByUserId', w3,
                'VerifiedDate', w9,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w9,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, SupplyTypesDialog, HeaderNoteDialog, CustomersDialog, PaymentTermsDialog]); // referenced dialogs