﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface VendorPaymentDetailsRow {
    RowNumber?: number;
    VendorPaymentDetailId?: number;
    VendorPaymentId?: number;
    PurchaseOrderId?: number;
    VendorBillId?: number;
    VendorBillDetailId?: number;
    AmountPaid?: number;
    VendorPaymentPaymentVoucherNo?: string;
    PurchaseOrderNo?: string;
    VendorBillNo?: string;
}

export abstract class VendorPaymentDetailsRow {
    static readonly idProperty = 'VendorPaymentDetailId';
    static readonly localTextPrefix = 'Default.VendorPaymentDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorPaymentDetailsRow>();
}