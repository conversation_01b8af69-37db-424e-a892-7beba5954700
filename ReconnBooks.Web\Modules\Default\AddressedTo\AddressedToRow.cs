using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableN<PERSON>("AddressedTo")]
[DisplayName("Addressed To"), InstanceName("Addressed To"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[UniqueConstraint(new[] { "AddressedTo" })]
public sealed partial class AddressedToRow : Row<AddressedToRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Addressed To Id"), Identity, IdProperty,Hidden]
    public int? AddressedToId { get => fields.AddressedToId[this]; set => fields.AddressedToId[this] = value; }

    [DisplayName("Addressed To"), Size(50), NotNull, QuickSearch, NameProperty]
    public string AddressedTo { get => fields.AddressedTo[this]; set => fields.AddressedTo[this] = value; }
}