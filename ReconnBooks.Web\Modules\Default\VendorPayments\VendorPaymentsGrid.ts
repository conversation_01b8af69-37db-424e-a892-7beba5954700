import { VendorPaymentsColumns, VendorPaymentsRow, VendorPaymentsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, LookupEditor, WidgetProps } from '@serenity-is/corelib';
import { VendorPaymentsDialog } from './VendorPaymentsDialog';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { ExcelExportHelper, PdfExportHelper, ReportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";

@Decorators.registerClass('ReconnBooks.Default.VendorPaymentsGrid')
@Decorators.filterable()

export class VendorPaymentsGrid extends EntityGrid<VendorPaymentsRow, any> {
    protected getColumnsKey() { return VendorPaymentsColumns.columnsKey; }
    protected getDialogType() { return VendorPaymentsDialog; }
    protected getRowDefinition() { return VendorPaymentsRow; }
    protected getService() { return VendorPaymentsService.baseUrl; }

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }
    }

    protected getButtons() {
        var buttons = super.getButtons();
        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: VendorPaymentsService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));
        return buttons;
    }
}