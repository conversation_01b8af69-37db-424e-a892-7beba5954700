using ReconnBooks.Administration;
using ReconnBooks.Modules.Administration.User.Authentication;

namespace ReconnBooks.Common.RowBehaviors;


public class RowNumberedBehavior : IImplicitBehavior, IListBehavior
{
    private Int64Field fldRowIndex;

    public bool ActivateFor(IRow row)
    {
        if (row is not IRowNumberedRow rowNumberedRow)
            return false;

        fldRowIndex = rowNumberedRow.RowNumberField;
        return true;
    }

    public void OnAfterExecuteQuery(IListRequestHandler handler)
    { }

    public void OnApplyFilters(IListRequestHandler handler, SqlQuery query)
    { }

    public void OnBeforeExecuteQuery(IListRequestHandler handler)
    { }

    public void OnPrepareQuery(IListRequestHandler handler, SqlQuery query)
    { }

    public void OnReturn(IListRequestHandler handler)
    {
        long rowIndex = handler.Request.Skip + 1;

        foreach (IRow row in handler.Response.Entities)
        {
            fldRowIndex[row] = rowIndex;
            rowIndex++;
        }
    }

    public void OnValidateRequest(IListRequestHandler handler)
    { }
}

