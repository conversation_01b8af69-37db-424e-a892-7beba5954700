﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { DataAuditLogRow } from "./DataAuditLogRow";
import { DataAuditLogType } from "./DataAuditLogType";

export interface DataAuditLogColumns {
    LogId: Column<DataAuditLogRow>;
    LogType: Column<DataAuditLogRow>;
    LogDate: Column<DataAuditLogRow>;
    Username: Column<DataAuditLogRow>;
    Tablename: Column<DataAuditLogRow>;
    RecordId: Column<DataAuditLogRow>;
    FieldName: Column<DataAuditLogRow>;
    OldValue: Column<DataAuditLogRow>;
    NewValue: Column<DataAuditLogRow>;
}

export class DataAuditLogColumns extends ColumnsBase<DataAuditLogRow> {
    static readonly columnsKey = 'Default.DataAuditLog';
    static readonly Fields = fieldsProxy<DataAuditLogColumns>();
}

[DataAuditLogType]; // referenced types