
namespace ReconnBooks.Administration;

using Serenity.Abstractions;
using Serenity.ComponentModel;
using Serenity.Web;
using System;
using System.Collections.Generic;

[DataScript("Administration.PermissionKeys", Permission = PermissionKeys.Security)]
public class PermissionKeysDataScript : DataScript<IEnumerable<string>>
{
    private readonly IPermissionKeyLister permissionKeyLister;
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;


    public PermissionKeysDataScript(IPermissionKeyLister permissionKeyLister, IUserAccessor userAccessor, IUserRetrieveService userRetriever)
    {
        this.permissionKeyLister = permissionKeyLister ?? throw new ArgumentNullException(nameof(permissionKeyLister));
        this.userAccessor = userAccessor ?? throw new ArgumentNullException(nameof(userAccessor));
        this.userRetriever = userRetriever ?? throw new ArgumentNullException(nameof(userRetriever));
        GroupKey = RoleRow.Fields.GenerationKey;
    }

    protected override IEnumerable<string> GetData()
    {
        var userPermissions =  permissionKeyLister.ListPermissionKeys(includeRoles: false);

        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return userPermissions;
        }

        //Do not provide PermissionKeys.ConsultantS.User to a client admin. client admin should not see or be aware of this permission key.
        if (!(user.Username == "admin" || user.ConsultantId.GetValueOrDefault() > 0))
        {
            userPermissions = userPermissions.Where(permissionKey => permissionKey != PermissionKeys.Consultants.User).ToArray();
        }

        return userPermissions;
    }
}