using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.VendorBillsRow>;
using MyRow = ReconnBooks.Default.VendorBillsRow;

namespace ReconnBooks.Default;

public interface IVendorBillsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class VendorBillsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IVendorBillsListHandler
{
    public VendorBillsListHandler(IRequestContext context)
            : base(context)
    {
    }
    protected override void OnBeforeExecuteQuery()
    {
        Request.IncludeColumns.Add("VendorBillDetailsList");
        base.OnBeforeExecuteQuery();
    }
    protected override void OnReturn()
    {
        base.OnReturn();
        foreach (var entity in Response.Entities)
        {
            entity.NetTaxableAmount = entity.VendorBillDetailsList?.Sum(a => a.NetTaxableAmount.GetValueOrDefault());
            entity.NetCGSTAmount = entity.VendorBillDetailsList?.Sum(a => a.NetCGSTAmount.GetValueOrDefault());
            entity.NetIGSTAmount = entity.VendorBillDetailsList?.Sum(a => a.NetIGSTAmount.GetValueOrDefault());
            entity.NetSGSTAmount = entity.VendorBillDetailsList?.Sum(a => a.NetSGSTAmount.GetValueOrDefault());

        }
    }
}