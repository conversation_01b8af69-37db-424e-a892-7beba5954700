﻿CREATE TABLE [dbo].[DeliveryNoteDetails] (
    [DeliveryNoteDetailId] INT             IDENTITY (1, 1) NOT NULL,
    [DeliveryNoteId]       INT             NOT NULL,
    [CommodityTypeId]      INT             NOT NULL,
    [CommodityId]          BIGINT          NOT NULL,
    [CommodityDescription] NVARCHAR (MAX)  NULL,
    [SKU]                  NVARCHAR (100)  NULL,
    [ProductSerialNos]     NVARCHAR (MAX)  NULL,
    [Quantity]             DECIMAL (18, 2) CONSTRAINT [DF_DeliveryNoteDetails_Quantity] DEFAULT ((1)) NOT NULL,
    [UnitId]               INT             NOT NULL,
    [DummyField1]          NVARCHAR (10)   NULL,
    [UnitPrice]            DECIMAL (18, 2) CONSTRAINT [DF_DeliveryNoteDetails_UnitPrice] DEFAULT ((0)) NOT NULL,
    [GSTRateId]            INT             NOT NULL,
    [IGSTRate]             DECIMAL (18, 2) NULL,
    [CGSTRate]             DECIMAL (18, 2) NULL,
    [SGSTRate]             DECIMAL (18, 2) NULL,
    [Du<PERSON><PERSON><PERSON>]           NVARCHAR (200)  NULL,
    [NetAmount]            DECIMAL (18, 2) NULL,
    CONSTRAINT [PK_DeliveryNoteDetails] PRIMARY KEY CLUSTERED ([DeliveryNoteDetailId] ASC),
    CONSTRAINT [FK_DeliveryNoteDetails_DeliveryNotes] FOREIGN KEY ([DeliveryNoteId]) REFERENCES [dbo].[DeliveryNotes] ([DeliveryNoteId]),
    CONSTRAINT [FK_DeliveryNoteDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_DeliveryNoteDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_DeliveryNoteDetails_CommodityId] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_DeliveryNoteDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [DeliveryNotes]
    ON [dbo].[DeliveryNoteDetails]([DeliveryNoteId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[DeliveryNoteDetails]([CommodityId] ASC);

