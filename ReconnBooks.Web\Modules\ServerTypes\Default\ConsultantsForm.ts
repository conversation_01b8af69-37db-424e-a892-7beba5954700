﻿import { StringEditor, ServiceLookupEditor, ImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { BusinessCategoriesDialog } from "../../Default/BusinessCategories/BusinessCategoriesDialog";
import { BusinessGroupsDialog } from "../../Default/BusinessGroups/BusinessGroupsDialog";
import { BusinessTypesDialog } from "../../Default/BusinessTypes/BusinessTypesDialog";
import { CitiesDialog } from "../../Default/Cities/CitiesDialog";
import { ConsultantBankAccountsGridEditor } from "../../Default/ConsultantBankAccounts/ConsultantBankAccountsGridEditor";
import { DesignationsDialog } from "../../Default/Designations/DesignationsDialog";
import { TitlesDialog } from "../../Default/Titles/TitlesDialog";

export interface ConsultantsForm {
    ConsultantName: StringEditor;
    ConsultantCode: StringEditor;
    Address: StringEditor;
    CityId: ServiceLookupEditor;
    PINCode: StringEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyId: ServiceLookupEditor;
    NatureOfSupplyId: ServiceLookupEditor;
    SupplyTypeId: ServiceLookupEditor;
    PAN: StringEditor;
    IECNo: StringEditor;
    CINNo: StringEditor;
    TANNo: StringEditor;
    UdyamNo: StringEditor;
    TitleId: ServiceLookupEditor;
    ContactPerson: StringEditor;
    DesignationId: ServiceLookupEditor;
    EMail: StringEditor;
    MobileNo: StringEditor;
    AlternateNo: StringEditor;
    PhoneNo: StringEditor;
    FaxNo: StringEditor;
    HomePage: StringEditor;
    ConsultantBankAccountsList: ConsultantBankAccountsGridEditor;
    Logo: ImageUploadEditor;
    TagLine: StringEditor;
    ConsultantDSC: ImageUploadEditor;
    BusinessTypeId: ServiceLookupEditor;
    BusinessGroupId: ServiceLookupEditor;
    BusinessCategoryId: ServiceLookupEditor;
    InvoiceNoFormat: StringEditor;
    Disclaimer: StringEditor;
}

export class ConsultantsForm extends PrefixedContext {
    static readonly formKey = 'Default.Consultants';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ConsultantsForm.init)  {
            ConsultantsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = ConsultantBankAccountsGridEditor;
            var w3 = ImageUploadEditor;

            initFormType(ConsultantsForm, [
                'ConsultantName', w0,
                'ConsultantCode', w0,
                'Address', w0,
                'CityId', w1,
                'PINCode', w0,
                'GSTIN', w0,
                'PlaceOfSupplyId', w1,
                'NatureOfSupplyId', w1,
                'SupplyTypeId', w1,
                'PAN', w0,
                'IECNo', w0,
                'CINNo', w0,
                'TANNo', w0,
                'UdyamNo', w0,
                'TitleId', w1,
                'ContactPerson', w0,
                'DesignationId', w1,
                'EMail', w0,
                'MobileNo', w0,
                'AlternateNo', w0,
                'PhoneNo', w0,
                'FaxNo', w0,
                'HomePage', w0,
                'ConsultantBankAccountsList', w2,
                'Logo', w3,
                'TagLine', w0,
                'ConsultantDSC', w3,
                'BusinessTypeId', w1,
                'BusinessGroupId', w1,
                'BusinessCategoryId', w1,
                'InvoiceNoFormat', w0,
                'Disclaimer', w0
            ]);
        }
    }
}

queueMicrotask(() => [CitiesDialog, TitlesDialog, DesignationsDialog, BusinessTypesDialog, BusinessGroupsDialog, BusinessCategoriesDialog]); // referenced dialogs