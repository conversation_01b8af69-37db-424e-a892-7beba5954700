import { confirmDialog, Decorators, EditorProps, Fluent, getjQuery, getRemoteData, LookupEditorBase, LookupEditorOptions, toId } from "@serenity-is/corelib";
import { ClientUsersRow, ClientUsersService } from "../../ServerTypes/Default";

@Decorators.registerClass()
@Decorators.registerEditor('ReconnBooks.Default.ClientUsers.ClientUserSelector')
@Decorators.panel()
export class ClientUserSelector<P = {}> extends LookupEditorBase<LookupEditorOptions, any> {

    constructor(props: EditorProps<P>) {
        super(props);

        // Fetch active client and set the initial value
        ClientUsersService.ActiveClient({},
            response => {
                this.value = response.ClientId.toString();
            }
        );

        // Set up the change handler for Select2 selection
        this.changeSelect2(async e => {
            var clientUserId = toId(this.element.val());
            var clientUserName = (await ClientUsersRow.getLookupAsync()).itemById[clientUserId].ClientName;

            confirmDialog("You will be working for" + " " + clientUserName,
                () => {
                    // Update the client
                    ClientUsersService.Update({ EntityId: clientUserId },
                        response => {
                            var userData = getRemoteData('UserData');
                            let $ = getjQuery();
                            $?.("#clientNameHeader").text("You are currently working for " + " " + userData.ClientName);
                            $?.("#clientNameP").text(userData.ClientName);
                            $?.("#clientCodeP").text(userData.ClientCode);
                            //TODO: signout from application
                            var currentDoc = location.href.split("/");
                            location.assign(currentDoc[0] + "//" + currentDoc[2]);
                        }
                    );
                },
                {
                    onNo: () => {
                        // Reset the active client if user declines
                        ClientUsersService.ActiveClient({},
                            response => {
                                this.value = response.ClientId.toString();
                            }
                        );
                    }
                });
        });
    }

    // Define the lookup key for this editor
    protected getLookupKey() {
        return 'Administration.ClientUsers';
    }

    protected getItemText(item, lookup) {
        return item.ClientName;
    }
}

