using Serenity.ComponentModel;
using ReconnBooks.Common.RowBehaviors;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("CommodityTypes")]
[DisplayName("Commodity Types"), InstanceName("Commodity Types"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class CommodityTypesRow : Row<CommodityTypesRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Commodity Type Id"), Identity, IdProperty,Hidden]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity Type"), Size(50), NotNull, QuickSearch, NameProperty]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Is Default"), NotNull, LookupInclude]
    public bool? SetDefault { get => fields.SetDefault[this]; set => fields.SetDefault[this] = value; }
}