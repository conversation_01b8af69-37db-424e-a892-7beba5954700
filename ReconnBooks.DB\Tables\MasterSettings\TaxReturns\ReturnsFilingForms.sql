﻿CREATE TABLE [dbo].[ReturnsFilingForms] 
(
    [ReturnsFilingFormId]   INT             NOT NULL    IDENTITY (1, 1),
    [ReturnsFilingForm]     NVARCHAR (200)  NOT NULL,
    [NatureOfSupplyId]      INT                 NULL,
    [SupplyTypeId]          INT                 NULL,
    [Description]           NVARCHAR (200)      NULL,  
    [Filer]                 NVARCHAR (200)      NULL,
    [Frequency]             NVARCHAR (50)       NULL,
    [DueDate]               SMALLDATETIME       NULL,
    [Remarks]               NVARCHAR(MAX)       NULL,

    CONSTRAINT [PK_ReturnsFilingForms]            PRIMARY KEY CLUSTERED     ([ReturnsFilingFormId] ASC),
    CONSTRAINT [FK_ReturnsFilingForms_NatureOfSupply]   FOREIGN KEY ([NatureOfSupplyId])        REFERENCES [dbo].[NatureOfSupply] ([NatureOfSupplyId]),
    CONSTRAINT [FK_ReturnsFilingForms_SupplySections]   FOREIGN KEY ([SupplyTypeId]) REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId])
);


