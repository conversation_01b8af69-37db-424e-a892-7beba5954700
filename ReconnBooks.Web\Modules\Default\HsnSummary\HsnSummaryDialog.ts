import { Decorators, EntityDialog } from "@serenity-is/corelib";
import { HsnSummaryForm, HsnSummaryRow, InvoiceDetailsForm, InvoiceDetailsRow } from "../../ServerTypes/Default";

@Decorators.registerClass('ReconnBooks.Default.HsnSummaryDialog')
@Decorators.responsive()
@Decorators.panel()

export class HsnSummaryDialog extends EntityDialog<HsnSummaryRow> {
    protected getFormKey() { return HsnSummaryForm.formKey; }
    //protected getIdProperty() { return 'HsnCode'; }
    //protected getRowDefinition() { return InvoiceDetailsRow; }

    protected form = new HsnSummaryForm(this.idPrefix);

    protected getToolbarButtons() {
        var buttons = super.getToolbarButtons();
        // Remove all buttons.
        buttons.splice(0, buttons.length);
        return buttons;
    }

    protected getDialogOptions() {
        var opt = super.getDialogOptions();
        opt.title = 'List of HSN Code Wise Invoices';
        return opt;
    }
}