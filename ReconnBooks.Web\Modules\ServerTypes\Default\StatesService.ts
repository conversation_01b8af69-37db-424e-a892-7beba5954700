﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { StatesRow } from "./StatesRow";

export namespace StatesService {
    export const baseUrl = 'Default/States';

    export declare function Create(request: SaveRequest<StatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<StatesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<StatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<StatesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<StatesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<StatesRow>>;

    export const Methods = {
        Create: "Default/States/Create",
        Update: "Default/States/Update",
        Delete: "Default/States/Delete",
        Retrieve: "Default/States/Retrieve",
        List: "Default/States/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>StatesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}