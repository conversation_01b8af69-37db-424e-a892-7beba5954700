﻿import { StringEditor, ServiceLookupEditor, ImageUploadEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { BanksDialog } from "../../Default/Banks/BanksDialog";

export interface ClientBankAccountsForm {
    AccountName: StringEditor;
    BankId: ServiceLookupEditor;
    BranchName: StringEditor;
    AccountNumber: StringEditor;
    IFSCCode: StringEditor;
    BranchCode: StringEditor;
    SwiftCode: StringEditor;
    QRCode: ImageUploadEditor;
    Status: BooleanEditor;
}

export class ClientBankAccountsForm extends PrefixedContext {
    static readonly formKey = 'Default.ClientBankAccounts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ClientBankAccountsForm.init)  {
            ClientBankAccountsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = ImageUploadEditor;
            var w3 = BooleanEditor;

            initFormType(ClientBankAccountsForm, [
                'AccountName', w0,
                'BankId', w1,
                'BranchName', w0,
                'AccountNumber', w0,
                'IFSCCode', w0,
                'BranchCode', w0,
                'SwiftCode', w0,
                'QRCode', w2,
                'Status', w3
            ]);
        }
    }
}

queueMicrotask(() => [BanksDialog]); // referenced dialogs