using Microsoft.AspNetCore.Mvc;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.DependencyInjection;
using MyRow = ReconnBooks.Default.VendorBillsRow;
using ReconnBooks.Modules.Common.Helpers.EmailHelper;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/VendorBills/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class VendorBillsEndpoint : ServiceEndpoint
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;
    private readonly IServiceProvider serviceProvider;
    private readonly ReportEmailHelper _emailHelper;
    public VendorBillsEndpoint(IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections, IServiceProvider serviceProvider, ReportEmailHelper emailHelper)
    {
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
        this.serviceProvider = serviceProvider;
        this._emailHelper = emailHelper;
    }

    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IVendorBillsSaveHandler handler)
    {
        return handler.Create(uow, request);
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IVendorBillsSaveHandler handler)
    {
        return handler.Update(uow, request);
    }
 
    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] IVendorBillsDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] IVendorBillsRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] IVendorBillsListHandler handler)
    {
        return handler.List(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] IVendorBillsListHandler handler,
        [FromServices] IExcelExporter exporter)
    {
        var data = List(connection, request, handler).Entities;
        var bytes = exporter.Export(data, typeof(Columns.VendorBillsColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "VendorBillsList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }

    public ListResponse<VendorBillDetailsRow> GetFromPurchaseOrderDetails(RetrieveRequest request)
    {
        using (var connection = sqlConnections.NewByKey("Default"))
        {
            var purchaseOrderDetailsRowFields = PurchaseOrderDetailsRow.Fields;
            var purchaseOrderRetrieveHandler = serviceProvider.GetRequiredService<IPurchaseOrdersRetrieveHandler>();
            var purchaseOrdersRow = purchaseOrderRetrieveHandler.Retrieve(connection, new RetrieveRequest()
            {
                EntityId = request.EntityId
            });

            int id = 1;
            List<VendorBillDetailsRow> listVendorBillDetailsRow = new List<VendorBillDetailsRow>();

            var commoditiesRowFields = CommoditiesRow.Fields;
            purchaseOrdersRow.Entity.PurchaseOrderDetailsList.ForEach(q =>
            {
                var commodityRow = connection.Single<CommoditiesRow>(new Criteria(commoditiesRowFields.CommodityId) == q.CommodityId.GetValueOrDefault());
                listVendorBillDetailsRow.Add(new VendorBillDetailsRow
                {
                    RowNumber = id++,
                    CommodityId = q.CommodityId,
                    CommodityName = q.CommodityName,
                    CommodityCode = q.CommodityCode,
                    CommodityDescription = q.CommodityDescription,
                    CommodityTypeId = q.CommodityTypeId,
                    CommodityType = q.CommodityType,
                    UnitId = q.UnitId,
                    UnitName = q.UnitName,
                    BillQuantity = q.Quantity,
                    UnitPrice = q.UnitPrice,
                    UnitAmount = q.NetPricePerUnit,
                    NetAmount = q.NetAmount,
                    //DiscountAmount = q.DiscountAmount,
                    NetTaxableAmount = q.NetTaxableAmount,
                    TaxableAmountPerUnit = q.TaxableAmountPerUnit,
                    GSTRateId = q.GSTRateId,
                    GSTRateRemarks = q.GSTRateRemarks,
                    CGSTRate = q.CGSTRate,
                    SGSTRate = q.SGSTRate,
                    IGSTRate = q.IGSTRate,
                    IGSTAmountPerUnit = q.IGSTAmountPerUnit,
                    CGSTAmountPerUnit = q.CGSTAmountPerUnit,
                    SGSTAmountPerUnit = q.SGSTAmountPerUnit,
                    NetCGSTAmount = q.NetCGSTAmount,
                    NetSGSTAmount = q.NetSGSTAmount,
                    NetIGSTAmount = q.NetIGSTAmount,
                    NetPricePerUnit = q.NetPricePerUnit
                });
            });

            return new ListResponse<VendorBillDetailsRow>()
            {
                Entities = listVendorBillDetailsRow
            };
        }
    }
}