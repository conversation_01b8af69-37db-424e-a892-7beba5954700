﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.CommodityTypesRow>;
using MyRow = ReconnBooks.Default.CommodityTypesRow;

namespace ReconnBooks.Default;

public interface ICommodityTypesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class CommodityTypesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, ICommodityTypesRetrieveHandler
{
    public CommodityTypesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}