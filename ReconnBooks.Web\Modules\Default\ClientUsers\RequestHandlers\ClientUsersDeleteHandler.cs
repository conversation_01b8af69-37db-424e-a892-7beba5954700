﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.ClientUsersRow;

namespace ReconnBooks.Default;

public interface IClientUsersDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class ClientUsersDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IClientUsersDeleteHandler
{
    public ClientUsersDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}