﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { SalesReturnDetailsRow } from "./SalesReturnDetailsRow";

export namespace SalesReturnDetailsService {
    export const baseUrl = 'Default/SalesReturnDetails';

    export declare function Create(request: SaveRequest<SalesReturnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SalesReturnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SalesReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SalesReturnDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SalesReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesReturnDetailsRow>>;

    export const Methods = {
        Create: "Default/SalesReturnDetails/Create",
        Update: "Default/SalesReturnDetails/Update",
        Delete: "Default/SalesReturnDetails/Delete",
        Retrieve: "Default/SalesReturnDetails/Retrieve",
        List: "Default/SalesReturnDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>SalesReturnDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}