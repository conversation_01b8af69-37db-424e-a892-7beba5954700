﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.BusinessCategoriesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.BusinessCategoriesRow;

namespace ReconnBooks.Default;

public interface IBusinessCategoriesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessCategoriesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessCategoriesSaveHandler
{
    public BusinessCategoriesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}