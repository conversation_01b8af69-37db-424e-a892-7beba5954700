﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface NarrationsRow {
    RowNumber?: number;
    NarrationId?: number;
    NarrationText?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class NarrationsRow {
    static readonly idProperty = 'NarrationId';
    static readonly nameProperty = 'NarrationText';
    static readonly localTextPrefix = 'Default.Narrations';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<NarrationsRow>();
}