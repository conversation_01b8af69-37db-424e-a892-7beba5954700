﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { SalesRegisterRow } from "./SalesRegisterRow";

export interface SalesRegisterColumns {
    SalesRegisterId: Column<SalesRegisterRow>;
    DocumentNo: Column<SalesRegisterRow>;
    Date: Column<SalesRegisterRow>;
    InvoiceId: Column<SalesRegisterRow>;
    ReceiptId: Column<SalesRegisterRow>;
    CompanyName: Column<SalesRegisterRow>;
    Debit: Column<SalesRegisterRow>;
    Credit: Column<SalesRegisterRow>;
    TDSAmount: Column<SalesRegisterRow>;
    TCSAmount: Column<SalesRegisterRow>;
    Balance: Column<SalesRegisterRow>;
    Narration: Column<SalesRegisterRow>;
}

export class SalesRegisterColumns extends ColumnsBase<SalesRegisterRow> {
    static readonly columnsKey = 'Default.SalesRegister';
    static readonly Fields = fieldsProxy<SalesRegisterColumns>();
}

[IndianNumberFormatter]; // referenced types