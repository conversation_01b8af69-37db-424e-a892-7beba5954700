﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ClientBankAccountsRow {
    ClientBankAccountId?: number;
    ClientId?: number;
    AccountName?: string;
    BankId?: number;
    BankName?: string;
    BranchName?: string;
    AccountNumber?: string;
    IFSCCode?: string;
    BranchCode?: string;
    SwiftCode?: string;
    QRCode?: string;
    Status?: boolean;
}

export abstract class ClientBankAccountsRow {
    static readonly idProperty = 'ClientBankAccountId';
    static readonly nameProperty = 'BranchName';
    static readonly localTextPrefix = 'Default.ClientBankAccounts';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ClientBankAccountsRow>();
}