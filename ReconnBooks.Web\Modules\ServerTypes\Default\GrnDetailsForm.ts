﻿import { ServiceLookupEditor, TextAreaEditor, DecimalEditor, LookupEditor, DateEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";

export interface GrnDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    ReceivedQuantity: DecimalEditor;
    PoQuantity: DecimalEditor;
    AcceptedQuantity: DecimalEditor;
    ReceivedUnitId: LookupEditor;
    PoUnitId: LookupEditor;
    AcceptedUnitId: LookupEditor;
    SupplyDueDate: DateEditor;
    Sku: StringEditor;
    SerialNos: StringEditor;
    LocationId: ServiceLookupEditor;
    WarehouseId: ServiceLookupEditor;
    StoreId: ServiceLookupEditor;
    RackId: ServiceLookupEditor;
    Remarks: StringEditor;
    PurchaseOrderDetailId: LookupEditor;
}

export class GrnDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.GrnDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!GrnDetailsForm.init)  {
            GrnDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = DecimalEditor;
            var w4 = LookupEditor;
            var w5 = DateEditor;
            var w6 = StringEditor;

            initFormType(GrnDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'ReceivedQuantity', w3,
                'PoQuantity', w3,
                'AcceptedQuantity', w3,
                'ReceivedUnitId', w4,
                'PoUnitId', w4,
                'AcceptedUnitId', w4,
                'SupplyDueDate', w5,
                'Sku', w6,
                'SerialNos', w6,
                'LocationId', w0,
                'WarehouseId', w0,
                'StoreId', w0,
                'RackId', w0,
                'Remarks', w6,
                'PurchaseOrderDetailId', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog]); // referenced dialogs