﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { DeliveryNoteDetailsRow } from "./DeliveryNoteDetailsRow";
import { DeliveryNotesRow } from "./DeliveryNotesRow";

export namespace DeliveryNotesService {
    export const baseUrl = 'Default/DeliveryNotes';

    export declare function Create(request: SaveRequest<DeliveryNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DeliveryNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DeliveryNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DeliveryNotesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DeliveryNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DeliveryNotesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromSalesOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<DeliveryNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DeliveryNoteDetailsRow>>;
    export declare function EmailDeliveryNotes(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/DeliveryNotes/Create",
        Update: "Default/DeliveryNotes/Update",
        Delete: "Default/DeliveryNotes/Delete",
        Retrieve: "Default/DeliveryNotes/Retrieve",
        List: "Default/DeliveryNotes/List",
        GetNextNumber: "Default/DeliveryNotes/GetNextNumber",
        GetFromSalesOrderDetails: "Default/DeliveryNotes/GetFromSalesOrderDetails",
        EmailDeliveryNotes: "Default/DeliveryNotes/EmailDeliveryNotes"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromSalesOrderDetails', 
        'EmailDeliveryNotes'
    ].forEach(x => {
        (<any>DeliveryNotesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}