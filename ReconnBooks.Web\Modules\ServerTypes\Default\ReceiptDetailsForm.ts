﻿import { ServiceLookupEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ReceiptDetailsForm {
    InvoiceId: ServiceLookupEditor;
    TDSRateId: ServiceLookupEditor;
    TDSAmount: DecimalEditor;
    TCSRateId: ServiceLookupEditor;
    TCSAmount: DecimalEditor;
    AmountReceived: DecimalEditor;
    BalanceReceivable: DecimalEditor;
    TaxableAmount: DecimalEditor;
}

export class ReceiptDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.ReceiptDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ReceiptDetailsForm.init)  {
            ReceiptDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = DecimalEditor;

            initFormType(ReceiptDetailsForm, [
                'InvoiceId', w0,
                'TDSRateId', w0,
                'TDSAmount', w1,
                'TCSRateId', w0,
                'TCSAmount', w1,
                'AmountReceived', w1,
                'BalanceReceivable', w1,
                'TaxableAmount', w1
            ]);
        }
    }
}