using Microsoft.AspNetCore.Mvc;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using System.Collections.Generic;
using MyRow = ReconnBooks.Default.SalesRegisterRow;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/SalesRegister/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class SalesRegisterEndpoint : ServiceEndpoint
{
    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] ISalesRegisterSaveHandler handler)
    {
        return handler.Create(uow, request);
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] ISalesRegisterSaveHandler handler)
    {
        return handler.Update(uow, request);
    }
 
    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] ISalesRegisterDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] ISalesRegisterRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] ISalesRegisterListHandler handler, 
        [FromServices] IUserRetrieveService userRetrieveService, 
        [FromServices] IUserAccessor userAccessor)
    {
        return ExecuteCustomQuery(connection, request, userRetrieveService, userAccessor);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] ISalesRegisterListHandler handler,
        [FromServices] IExcelExporter exporter, 
        [FromServices] IUserRetrieveService userRetrieveService,
        [FromServices] IUserAccessor userAccessor)
    {
        var data = List(connection, request, handler, userRetrieveService, userAccessor).Entities;
        var bytes = exporter.Export(data, typeof(Columns.SalesRegisterColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "SalesRegisterList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }

    private ListResponse<MyRow> ExecuteCustomQuery(IDbConnection connection, ListRequest request, IUserRetrieveService userRetriever, IUserAccessor userAccessor)
    {
        var response = new ListResponse<MyRow>();

        if (userAccessor.User?.GetUserDefinition(userRetriever) is UserDefinition user)
        {
            var clientId = user.ClientId;

            var sql = @"
            WITH Combined AS (
                    SELECT 
                        r.ReceiptDate AS [Date],
                        CONCAT(r.ReceiptNo, ' - ', r.Narration) AS [Narration],
                        SUM(rd.AmountReceived) AS [Credit],  -- Aggregate receipt amounts
                        NULL AS [Debit],
                        SUM(rd.TDSAmount) AS [TDSAmount],    -- Aggregate TDS amounts
                        SUM(rd.TCSAmount) AS [TCSAmount],    -- Aggregate TCS amounts
                        c.CompanyName AS [CompanyName],
                        r.ReceiptNo AS [DocumentNo],
                        r.ReceiptId AS [ReceiptId],
                        NULL AS [InvoiceId]
                    FROM 
                        Receipts r

                    INNER JOIN 
                        ReceiptDetails rd ON r.ReceiptId = rd.ReceiptId
                    INNER JOIN 
                        Customers c ON r.CustomerId = c.CustomerId
                    WHERE 
                        r.ClientId = @clientId
                    GROUP BY    -- Group by receipt level fields
                        r.ReceiptDate,
                        r.ReceiptNo,
                        r.Narration,
                        c.CompanyName,
                        r.ReceiptId

                    UNION ALL

                    SELECT 
                        i.InvoiceDate AS [Date],
                        i.InvoiceNo AS [Narration],
                        NULL AS [Credit],
                        SUM(id.NetAmount) AS [Debit],
                        NULL AS [TDSAmount],
                        NULL AS [TCSAmount],
                        c.CompanyName AS [CompanyName],
                        i.InvoiceNo AS [DocumentNo],
                        NULL AS [ReceiptId],
                        i.InvoiceId AS [InvoiceId]
                    FROM 
                        Invoices i
                    INNER JOIN 
                        InvoiceDetails id ON i.InvoiceId = id.InvoiceId
                    INNER JOIN 
                        Customers c ON i.CustomerId = c.CustomerId
                    WHERE 
                        i.ClientId = @clientId
                    GROUP BY
                        i.InvoiceDate,
                        i.InvoiceNo,
                        c.CompanyName,
                        i.InvoiceId
                ),
                Ledger AS (
                    SELECT 
                        ROW_NUMBER() OVER (ORDER BY [Date]) AS SalesRegisterId,
                        [Date],
                        [Narration],
                        [Debit],
                        [Credit],
                        [TDSAmount],
                        [TCSAmount],
                        [CompanyName],
                        [DocumentNo],
                        [ReceiptId],
                        [InvoiceId],
                        ABS(SUM(ISNULL([Debit], 0) - ISNULL([Credit], 0) - ISNULL([TDSAmount], 0) - ISNULL([TCSAmount], 0)) OVER (ORDER BY [Date] ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW)) AS [Balance]
                    FROM 
                        Combined
                )
                SELECT 
                    SalesRegisterId,
                    [Date],
                    [Narration],
                    [Debit],
                    [Credit],
                    [TDSAmount],
                    [TCSAmount],
                    [CompanyName],
                    [DocumentNo],
                    [ReceiptId],
                    [InvoiceId],
                    [Balance]
                FROM 
                    Ledger
                ORDER BY 
                    [Date]";

            var results = connection.Query<MyRow>(sql, new { clientId });
            response.Entities = results.ToList();
        }

        return response;
    }
}


