using System.Security.Claims;

namespace ReconnBooks.Modules.Administration.User.Authentication.Claims;

public static class ClaimsPrincipalExtensions
{
    public static int? GetClientId(this ClaimsPrincipal user)
    {
        if (user is null)
            throw new ArgumentNullException(nameof(user));

        var clientClaim = user.Claims.FirstOrDefault(x => x.Type == "ClientId");
        if (clientClaim is null)
            throw new NullReferenceException("ClientId claim not found");

        if (int.TryParse(clientClaim.Value, out int i))
            return i;
        return null;
    }

    public static int? GetConsultantId(this ClaimsPrincipal user)
    {
        if (user is null)
            throw new ArgumentNullException(nameof(user));

        var consultantClaim = user.Claims.FirstOrDefault(x => x.Type == "ConsultantId");
        if (consultantClaim is null)
            throw new NullReferenceException("ConsultantId claim not found");

        if (int.TryParse(consultantClaim.Value, out int i))
            return i;
        return null;
    }
}
