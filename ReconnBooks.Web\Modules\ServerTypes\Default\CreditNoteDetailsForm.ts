﻿import { ServiceLookupEditor, TextAreaEditor, DecimalEditor, LookupEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";

export interface CreditNoteDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    InvoiceQuantity: DecimalEditor;
    ReturnedQuantity: DecimalEditor;
    UnitPrice: DecimalEditor;
    InvoiceUnitId: LookupEditor;
    ReturnedUnitId: LookupEditor;
    UnitAmount: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmountPerUnit: DecimalEditor;
    NetDiscountAmount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: DecimalEditor;
    NetPricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
    RejectionReasonId: ServiceLookupEditor;
    AssessmentRemarks: StringEditor;
    ReplacementMethodId: ServiceLookupEditor;
    Remarks: StringEditor;
    InvoiceDetailId: LookupEditor;
    SalesReturnDetailId: ServiceLookupEditor;
}

export class CreditNoteDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.CreditNoteDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CreditNoteDetailsForm.init)  {
            CreditNoteDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = DecimalEditor;
            var w4 = LookupEditor;
            var w5 = StringEditor;

            initFormType(CreditNoteDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'InvoiceQuantity', w3,
                'ReturnedQuantity', w3,
                'UnitPrice', w3,
                'InvoiceUnitId', w4,
                'ReturnedUnitId', w4,
                'UnitAmount', w3,
                'DiscountPercent', w3,
                'DiscountAmountPerUnit', w3,
                'NetDiscountAmount', w3,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w3,
                'NetTaxableAmount', w3,
                'IGSTRate', w3,
                'IGSTAmountPerUnit', w3,
                'NetIGSTAmount', w3,
                'CGSTRate', w3,
                'CGSTAmountPerUnit', w3,
                'NetCGSTAmount', w3,
                'SGSTRate', w3,
                'SGSTAmountPerUnit', w3,
                'NetSGSTAmount', w3,
                'DummyField', w3,
                'NetPricePerUnit', w3,
                'NetAmount', w3,
                'RejectionReasonId', w0,
                'AssessmentRemarks', w5,
                'ReplacementMethodId', w0,
                'Remarks', w5,
                'InvoiceDetailId', w4,
                'SalesReturnDetailId', w0
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog]); // referenced dialogs