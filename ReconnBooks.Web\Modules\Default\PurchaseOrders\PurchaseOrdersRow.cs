using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.Helpers;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("PurchaseOrders")]
[Di<PERSON><PERSON><PERSON><PERSON>("Purchase Orders"), InstanceName("Purchase Orders"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class PurchaseOrdersRow : Row<PurchaseOrdersRow.RowFields>, IIdRow, INameRow, IRowNumberedRow, IMultiClientRow
{
    const string jVendor = nameof(jVendor);
    const string jDeliveryCity = nameof(jDeliveryCity);
    const string jPurchaseType = nameof(jPurchaseType);
    const string jFinancialYear = nameof(jFinancialYear);
    const string jHeaderNote = nameof(jHeaderNote);
    const string jPaymentTerms = nameof(jPaymentTerms);
    const string jFootNote = nameof(jFootNote);
    const string jPreparedByUser = nameof(jPreparedByUser);
    const string jVerifiedByUser = nameof(jVerifiedByUser);
    const string jAuthorizedByUser = nameof(jAuthorizedByUser);
    const string jModifiedByUser = nameof(jModifiedByUser);
    const string jCancelledByUser = nameof(jCancelledByUser);
    const string jClient = nameof(jClient);
    const string jTDSRate = nameof(jTDSRate);
    const string jSupplyType = nameof(jSupplyType);
    const string jTCSRate = nameof(jTCSRate);
    const string jShipToCustomer = nameof(jShipToCustomer);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Purchase Order Id"), Identity, IdProperty]
    public int? PurchaseOrderId { get => fields.PurchaseOrderId[this]; set => fields.PurchaseOrderId[this] = value; }

    [DisplayName("Purchase Order No."), Size(50), NotNull, QuickSearch, NameProperty, Unique]
    public string PurchaseOrderNo { get => fields.PurchaseOrderNo[this]; set => fields.PurchaseOrderNo[this] = value; }

    [DisplayName("Purchase Order Date")]
    public DateTime? PurchaseOrderDate { get => fields.PurchaseOrderDate[this]; set => fields.PurchaseOrderDate[this] = value; }

    [DisplayName("Month"), Expression("FORMAT(PurchaseOrderDate, 'MMM')"), QuickFilter]
    [LookupEditor(typeof(MonthLookup))]
    public string PurchaseOrderMonth { get => fields.PurchaseOrderMonth[this]; set => fields.PurchaseOrderMonth[this] = value; }

    [DisplayName("Vendor Name"), NotNull, ForeignKey(typeof(VendorsRow)), LeftJoin(jVendor), TextualField(nameof(VendorName))]
    [ServiceLookupEditor(typeof(VendorsRow), InplaceAdd = true, Service = "Default/Vendors/List")]
    public int? VendorId { get => fields.VendorId[this]; set => fields.VendorId[this] = value; }

    // ----------- Fetching Billing Address -----------------
    [DisplayName("Billing Address")]
    [Origin(jVendor, nameof(CustomersRow.BillingAddress)), LookupInclude]
    public string BillingAddress { get => fields.BillingAddress[this]; set => fields.BillingAddress[this] = value; }

    [DisplayName("City")]
    [Origin(jVendor, nameof(CustomersRow.BillingCityCityName)), LookupInclude]
    public string BillingCityCityName { get => fields.BillingCityCityName[this]; set => fields.BillingCityCityName[this] = value; }

    [DisplayName("PIN Code")]
    [Origin(jVendor, nameof(CustomersRow.BillingPinCode)), LookupInclude]
    public string BillingPinCode { get => fields.BillingPinCode[this]; set => fields.BillingPinCode[this] = value; }

    [DisplayName("GST No.")]
    [Origin(jVendor, nameof(CustomersRow.GSTIN)), LookupInclude]
    public string GSTIN { get => fields.GSTIN[this]; set => fields.GSTIN[this] = value; }

    [DisplayName("Vendors Email")]
    [Origin(jVendor, nameof(VendorsRow.EMailId)), LookupInclude]
    public string VendorEMailId { get => fields.VendorEMailId[this]; set => fields.VendorEMailId[this] = value; }

    [DisplayName("Place of Supply")]
    [Origin(jVendor, nameof(CustomersRow.PlaceOfSupplyStateName)), LookupInclude]
    public string PlaceOfSupplyStateName { get => fields.PlaceOfSupplyStateName[this]; set => fields.PlaceOfSupplyStateName[this] = value; }

    [DisplayName("Ship To Company Name"), ForeignKey(typeof(CustomersRow)), LeftJoin(jShipToCustomer), TextualField(nameof(CustomerCompanyName))]
    [ServiceLookupEditor(typeof(CustomersRow), InplaceAdd = true, Service = "Default/Customers/List")]
    public int? ShipToCustomerId { get => fields.ShipToCustomerId[this]; set => fields.ShipToCustomerId[this] = value; }

    [DisplayName("Customer Name"), Origin(jShipToCustomer, nameof(CustomersRow.CompanyName)), QuickSearch, LookupInclude]
    public string CustomerCompanyName { get => fields.CustomerCompanyName[this]; set => fields.CustomerCompanyName[this] = value; }

    [DisplayName("Ship To Company Name"), Origin(jShipToCustomer, nameof(CustomersRow.CompanyName)), QuickSearch, LookupInclude]
    public string ShipToCustomerName { get => fields.ShipToCustomerName[this]; set => fields.ShipToCustomerName[this] = value; }

    [DisplayName("Billing Address")]
    [Origin(jShipToCustomer, nameof(CustomersRow.BillingAddress)), LookupInclude]
    public string ShippingAddress { get => fields.ShippingAddress[this]; set => fields.ShippingAddress[this] = value; }

    [DisplayName("City")]
    [Origin(jShipToCustomer, nameof(CustomersRow.BillingCityCityName)), LookupInclude]
    public string ShippingCityName { get => fields.ShippingCityName[this]; set => fields.ShippingCityName[this] = value; }

    [DisplayName("PIN Code")]
    [Origin(jShipToCustomer, nameof(CustomersRow.BillingPinCode)), LookupInclude]
    public string ShippingPinCode { get => fields.ShippingPinCode[this]; set => fields.ShippingPinCode[this] = value; }

    [DisplayName("GST No.")]
    [Origin(jShipToCustomer, nameof(CustomersRow.GSTIN)), LookupInclude]
    public string ShippingGSTIN { get => fields.ShippingGSTIN[this]; set => fields.ShippingGSTIN[this] = value; }

    [DisplayName("Place of Supply")]
    [Origin(jShipToCustomer, nameof(CustomersRow.PlaceOfSupplyStateName))]
    public string ShippingPlaceOfSupplyStateName { get => fields.ShippingPlaceOfSupplyStateName[this]; set => fields.ShippingPlaceOfSupplyStateName[this] = value; }

    /// --------------------------
    [DisplayName("Supply Type"), NotNull, ForeignKey(typeof(SupplyTypesRow)), LeftJoin(jSupplyType), TextualField(nameof(SupplyType))]
    [ServiceLookupEditor(typeof(SupplyTypesRow), InplaceAdd = true, Service = "Default/SupplyTypes/List")]
    public int? SupplyTypeId { get => fields.SupplyTypeId[this]; set => fields.SupplyTypeId[this] = value; }

    [DisplayName("Supply Type"), Origin(jSupplyType, nameof(SupplyTypesRow.SupplyType))]
    public string SupplyType { get => fields.SupplyType[this]; set => fields.SupplyType[this] = value; }

    [DisplayName("Financial Year"), NotNull, ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }

    [DisplayName("Reference No."), Size(50)]
    public string ReferenceNo { get => fields.ReferenceNo[this]; set => fields.ReferenceNo[this] = value; }

    [DisplayName("Reference Date")]
    public DateTime? ReferenceDate { get => fields.ReferenceDate[this]; set => fields.ReferenceDate[this] = value; }

    [DisplayName("Header Note"), ForeignKey(typeof(HeaderNoteRow)), LeftJoin(jHeaderNote), TextualField(nameof(HeaderNote))]
    [ServiceLookupEditor(typeof(HeaderNoteRow), InplaceAdd = true, Service = "Default/HeaderNote/List")]
    public int? HeaderNoteId { get => fields.HeaderNoteId[this]; set => fields.HeaderNoteId[this] = value; }

    //-------------------------------------------------------------------------
    [MasterDetailRelation(foreignKey: nameof(PurchaseOrderDetailsRow.PurchaseOrderId)), NotMapped]
    public List<PurchaseOrderDetailsRow> PurchaseOrderDetailsList
    {
        get { return Fields.PurchaseOrderDetailsList[this]; }
        set { Fields.PurchaseOrderDetailsList[this] = value; }
    }
    //-------------------------------------------------------------------------
    
    [DisplayName("Net Taxable Amt."), Size(18), Scale(2), NotMapped]
    public decimal? NetTaxableAmount { get => fields.NetTaxableAmount[this]; set => fields.NetTaxableAmount[this] = value; }

    [DisplayName("Net CGST Amt."), Column("NetCGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetCGSTAmount { get => fields.NetCGSTAmount[this]; set => fields.NetCGSTAmount[this] = value; }

    [DisplayName("Net SGST Amt."), Column("NetSGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetSGSTAmount { get => fields.NetSGSTAmount[this]; set => fields.NetSGSTAmount[this] = value; }

    [DisplayName("Net IGST Amt."), Column("NetIGSTAmount"), Size(18), Scale(2), NotMapped]
    public decimal? NetIGSTAmount { get => fields.NetIGSTAmount[this]; set => fields.NetIGSTAmount[this] = value; }

    [DisplayName("PO Amount"), Column("POAmount"), Size(18), Scale(2)]
    public decimal? POAmount { get => fields.POAmount[this]; set => fields.POAmount[this] = value; }

    [DisplayName("Rounding Off"), Size(18), Scale(2)]
    public decimal? RoundingOff { get => fields.RoundingOff[this]; set => fields.RoundingOff[this] = value; }

    [DisplayName("Grand Total"), Size(18), Scale(2), QuickSearch]
    public decimal? GrandTotal { get => fields.GrandTotal[this]; set => fields.GrandTotal[this] = value; }

    [DisplayName("TDSRate (%)"), Column("TDSRateId"), ForeignKey(typeof(TdsRatesRow)), LeftJoin(jTDSRate)]
    [ServiceLookupEditor(typeof(TdsRatesRow), Service = "Default/TDSRates/List",
        IdField = nameof(TdsRatesRow.TDSRateId), TextField = nameof(TdsRatesRow.TDSRateWithSection))]
    public int? TDSRateId { get => fields.TDSRateId[this]; set => fields.TDSRateId[this] = value; }

    [DisplayName("TCSRate (%)"), Column("TCSRateId"), ForeignKey(typeof(TcsRatesRow)), LeftJoin(jTCSRate)]
    [ServiceLookupEditor(typeof(TcsRatesRow), Service = "Default/TCSRates/List", 
        IdField = nameof(TcsRatesRow.TCSRateId), TextField = nameof(TcsRatesRow.TCSRateWithSection))]
    public int? TCSRateId { get => fields.TCSRateId[this]; set => fields.TCSRateId[this] = value; }

    [DisplayName("TDS Amount"), Size(18), Scale(2)]
    public decimal? TDSAmount { get => fields.TDSAmount[this]; set => fields.TDSAmount[this] = value; }

    [DisplayName("TCS Amount"), Size(18), Scale(2)]
    public decimal? TCSAmount { get => fields.TCSAmount[this]; set => fields.TCSAmount[this] = value; }

    [DisplayName("Due Date")]
    public DateTime? DeliveryDueDate { get => fields.DeliveryDueDate[this]; set => fields.DeliveryDueDate[this] = value; }

    [DisplayName("Payment Terms"), ForeignKey(typeof(PaymentTermsRow)), LeftJoin(jPaymentTerms), TextualField(nameof(PaymentTerms))]
    [LookupEditor(typeof(PaymentTermsRow),InplaceAdd = true, Async = true)]
    public int? PaymentTermsId { get => fields.PaymentTermsId[this]; set => fields.PaymentTermsId[this] = value; }

    [DisplayName("Payment Due Date")]
    public DateTime? PaymentDueDate { get => fields.PaymentDueDate[this]; set => fields.PaymentDueDate[this] = value; }

    [DisplayName("Inspection")]
    public string Inspection { get => fields.Inspection[this]; set => fields.Inspection[this] = value; }
  
    [DisplayName("Taxes"), Size(250)]
    public string Taxes { get => fields.Taxes[this]; set => fields.Taxes[this] = value; }

    [DisplayName("Foot Note"), ForeignKey(typeof(FootNotesRow)), LeftJoin(jFootNote), TextualField(nameof(FootNote))]
    [ServiceLookupEditor(typeof(FootNotesRow), Service = "Default/FootNotes/List")]
    public int? FootNoteId { get => fields.FootNoteId[this]; set => fields.FootNoteId[this] = value; }

    [DisplayName("Upload Documents")]
    public string UploadDocuments { get => fields.UploadDocuments[this]; set => fields.UploadDocuments[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("PO Status"), Column("POStatus"), NotNull]
    public bool? POStatus { get => fields.POStatus[this]; set => fields.POStatus[this] = value; }

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Prepared By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jPreparedByUser)]
    [TextualField(nameof(PreparedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? PreparedByUserId { get => fields.PreparedByUserId[this]; set => fields.PreparedByUserId[this] = value; }

    [DisplayName("Client"), Origin(jClient, nameof(ClientsRow.ClientName))]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }

    [DisplayName("Prepared Date"), DateTimeEditor]
    public DateTime? PreparedDate { get => fields.PreparedDate[this]; set => fields.PreparedDate[this] = value; }

    [DisplayName("Verified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jVerifiedByUser)]
    [TextualField(nameof(VerifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? VerifiedByUserId { get => fields.VerifiedByUserId[this]; set => fields.VerifiedByUserId[this] = value; }

    [DisplayName("Verified Date"), DateTimeEditor]
    public DateTime? VerifiedDate { get => fields.VerifiedDate[this]; set => fields.VerifiedDate[this] = value; }

    [DisplayName("Authorized By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jAuthorizedByUser)]
    [TextualField(nameof(AuthorizedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? AuthorizedByUserId { get => fields.AuthorizedByUserId[this]; set => fields.AuthorizedByUserId[this] = value; }

    [DisplayName("Authorized Date"), DateTimeEditor]
    public DateTime? AuthorizedDate { get => fields.AuthorizedDate[this]; set => fields.AuthorizedDate[this] = value; }

    [DisplayName("Modified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jModifiedByUser)]
    [TextualField(nameof(ModifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? ModifiedByUserId { get => fields.ModifiedByUserId[this]; set => fields.ModifiedByUserId[this] = value; }

    [DisplayName("Modified Date")]
    public DateTime? ModifiedDate { get => fields.ModifiedDate[this]; set => fields.ModifiedDate[this] = value; }

    [DisplayName("Cancelled By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jCancelledByUser)]
    [TextualField(nameof(CancelledByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? CancelledByUserId { get => fields.CancelledByUserId[this]; set => fields.CancelledByUserId[this] = value; }

    [DisplayName("Cancelled Date")]
    public DateTime? CancelledDate { get => fields.CancelledDate[this]; set => fields.CancelledDate[this] = value; }

    [DisplayName("Authorize Status"), NotNull]
    public bool? AuthorizedStatus { get => fields.AuthorizedStatus[this]; set => fields.AuthorizedStatus[this] = value; }

    [DisplayName("Vendor Name"), Origin(jVendor, nameof(VendorsRow.VendorName)), QuickSearch]
    public string VendorName { get => fields.VendorName[this]; set => fields.VendorName[this] = value; }

    [DisplayName("Financial Year"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName))]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }

    [DisplayName("Header Note"), Origin(jHeaderNote, nameof(HeaderNoteRow.HeaderNote))]
    public string HeaderNote { get => fields.HeaderNote[this]; set => fields.HeaderNote[this] = value; }

    [DisplayName("Payment Terms"), Origin(jPaymentTerms, nameof(PaymentTermsRow.PaymentTerms))]
    public string PaymentTerms { get => fields.PaymentTerms[this]; set => fields.PaymentTerms[this] = value; }

    [DisplayName("Foot Note"), Origin(jFootNote, nameof(FootNotesRow.FootNote))]
    public string FootNote { get => fields.FootNote[this]; set => fields.FootNote[this] = value; }

    [DisplayName("Prepared By"), Origin(jPreparedByUser, nameof(Administration.UserRow.Username))]
    public string PreparedByUserUsername { get => fields.PreparedByUserUsername[this]; set => fields.PreparedByUserUsername[this] = value; }

    [DisplayName("Verified By"), Origin(jVerifiedByUser, nameof(Administration.UserRow.Username))]
    public string VerifiedByUserUsername { get => fields.VerifiedByUserUsername[this]; set => fields.VerifiedByUserUsername[this] = value; }

    [DisplayName("Authorized By"), Origin(jAuthorizedByUser, nameof(Administration.UserRow.Username))]
    public string AuthorizedByUserUsername { get => fields.AuthorizedByUserUsername[this]; set => fields.AuthorizedByUserUsername[this] = value; }

    [DisplayName("Modified By"), Origin(jModifiedByUser, nameof(Administration.UserRow.Username))]
    public string ModifiedByUserUsername { get => fields.ModifiedByUserUsername[this]; set => fields.ModifiedByUserUsername[this] = value; }

    [DisplayName("Cancelled By"), Origin(jCancelledByUser, nameof(Administration.UserRow.Username))]
    public string CancelledByUserUsername { get => fields.CancelledByUserUsername[this]; set => fields.CancelledByUserUsername[this] = value; }
    public Int32Field ClientIdField => fields.ClientId; //Multitenancy


}

