﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { VendorBillDetailsRow } from "./VendorBillDetailsRow";

export namespace VendorBillDetailsService {
    export const baseUrl = 'Default/VendorBillDetails';

    export declare function Create(request: SaveRequest<VendorBillDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorBillDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorBillDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorBillDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorBillDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorBillDetailsRow>>;

    export const Methods = {
        Create: "Default/VendorBillDetails/Create",
        Update: "Default/VendorBillDetails/Update",
        Delete: "Default/VendorBillDetails/Delete",
        Retrieve: "Default/VendorBillDetails/Retrieve",
        List: "Default/VendorBillDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>VendorBillDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}