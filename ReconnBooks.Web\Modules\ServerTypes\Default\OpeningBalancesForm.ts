﻿import { DateEditor, DecimalEditor, LookupEditor, ServiceLookupEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface OpeningBalancesForm {
    FromDate: DateEditor;
    ToDate: DateEditor;
    Ammount: DecimalEditor;
    FinancialYearId: LookupEditor;
    LedgerAccountId: ServiceLookupEditor;
    Remarks: StringEditor;
}

export class OpeningBalancesForm extends PrefixedContext {
    static readonly formKey = 'Default.OpeningBalances';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!OpeningBalancesForm.init)  {
            OpeningBalancesForm.init = true;

            var w0 = DateEditor;
            var w1 = DecimalEditor;
            var w2 = LookupEditor;
            var w3 = ServiceLookupEditor;
            var w4 = StringEditor;

            initFormType(OpeningBalancesForm, [
                'FromDate', w0,
                'ToDate', w0,
                'Ammount', w1,
                'FinancialYearId', w2,
                'LedgerAccountId', w3,
                'Remarks', w4
            ]);
        }
    }
}