import { CommoditiesColumns, CommoditiesRow, CommoditiesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, WidgetProps } from '@serenity-is/corelib';
import { CommoditiesDialog, ProductExcelImportDialog } from './CommoditiesDialog';
import { ExcelExportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";

@Decorators.registerClass('ReconnBooks.Default.CommoditiesGrid')
@Decorators.filterable()
export class CommoditiesGrid extends EntityGrid<CommoditiesRow> {
    protected getColumnsKey() { return CommoditiesColumns.columnsKey; }
    protected getDialogType() { return CommoditiesDialog; }
    protected getRowDefinition() { return CommoditiesRow; }
    protected getService() { return CommoditiesService.baseUrl; }

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected getButtons() {
        var buttons = super.getButtons();

        const addButton = buttons.find(x => x.cssClass === "add-button");
        if (addButton) {
            addButton.title = "New Product/Service";
        }

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: CommoditiesService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Export to Excel"
        }));

        buttons.push({
            title: 'Import From Excel',
            cssClass: 'export-xlsx-button',
            separator: true,
            onClick: () => {
                // open import dialog, let it handle rest
                var dialog = new ProductExcelImportDialog();
                dialog.element.on('dialogclose', () => {
                    this.refresh();
                    dialog = null;
                });
                dialog.dialogOpen();
            }
        });
        return buttons;
    }
}