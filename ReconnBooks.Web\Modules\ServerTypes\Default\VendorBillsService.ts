﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { VendorBillDetailsRow } from "./VendorBillDetailsRow";
import { VendorBillsRow } from "./VendorBillsRow";

export namespace VendorBillsService {
    export const baseUrl = 'Default/VendorBills';

    export declare function Create(request: SaveRequest<VendorBillsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorBillsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorBillsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorBillsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorBillsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorBillsRow>>;
    export declare function GetFromPurchaseOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<VendorBillDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorBillDetailsRow>>;

    export const Methods = {
        Create: "Default/VendorBills/Create",
        Update: "Default/VendorBills/Update",
        Delete: "Default/VendorBills/Delete",
        Retrieve: "Default/VendorBills/Retrieve",
        List: "Default/VendorBills/List",
        GetFromPurchaseOrderDetails: "Default/VendorBills/GetFromPurchaseOrderDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetFromPurchaseOrderDetails'
    ].forEach(x => {
        (<any>VendorBillsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}