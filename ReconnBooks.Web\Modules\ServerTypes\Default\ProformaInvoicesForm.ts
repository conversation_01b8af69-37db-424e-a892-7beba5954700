﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, MultipleImageUploadEditor, DateTimeEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { DeliveryNotesDialog } from "../../Default/DeliveryNotes/DeliveryNotesDialog";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { ProformaInvoiceDetailsGridEditor } from "../../Default/ProformaInvoiceDetails/ProformaInvoiceDetailsGridEditor";
import { SalesOrdersDialog } from "../../Default/SalesOrders/SalesOrdersDialog";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";

export interface ProformaInvoicesForm {
    ProformaInvoiceNo: StringEditor;
    ProformaInvoiceDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    ShipToCustomerId: ServiceLookupEditor;
    ShippingAddress: StringEditor;
    ShippingCityName: StringEditor;
    ShippingPinCode: StringEditor;
    ShippingGSTIN: StringEditor;
    ShippingPlaceOfSupplyStateName: StringEditor;
    OrderRefNo: StringEditor;
    OrderRefDate: DateEditor;
    SalesOrderId: ServiceLookupEditor;
    DeliveryNoteId: ServiceLookupEditor;
    ProformaInvoiceDetailsList: ProformaInvoiceDetailsGridEditor;
    PaymentTermsId: ServiceLookupEditor;
    ProformaInvoiceAmt: DecimalEditor;
    PaymentDueDate: DateEditor;
    RoundingOff: DecimalEditor;
    DummyField: DecimalEditor;
    GrandTotal: DecimalEditor;
    ShippedVia: StringEditor;
    Inspection: StringEditor;
    VehicleNo: StringEditor;
    DocketNo: StringEditor;
    Remarks: TextAreaEditor;
    UploadFiles: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    AuthorizedStatus: BooleanEditor;
}

export class ProformaInvoicesForm extends PrefixedContext {
    static readonly formKey = 'Default.ProformaInvoices';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProformaInvoicesForm.init)  {
            ProformaInvoicesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = ProformaInvoiceDetailsGridEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = MultipleImageUploadEditor;
            var w8 = DateTimeEditor;
            var w9 = BooleanEditor;

            initFormType(ProformaInvoicesForm, [
                'ProformaInvoiceNo', w0,
                'ProformaInvoiceDate', w1,
                'CustomerId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'ShipToCustomerId', w2,
                'ShippingAddress', w0,
                'ShippingCityName', w0,
                'ShippingPinCode', w0,
                'ShippingGSTIN', w0,
                'ShippingPlaceOfSupplyStateName', w0,
                'OrderRefNo', w0,
                'OrderRefDate', w1,
                'SalesOrderId', w2,
                'DeliveryNoteId', w2,
                'ProformaInvoiceDetailsList', w4,
                'PaymentTermsId', w2,
                'ProformaInvoiceAmt', w5,
                'PaymentDueDate', w1,
                'RoundingOff', w5,
                'DummyField', w5,
                'GrandTotal', w5,
                'ShippedVia', w0,
                'Inspection', w0,
                'VehicleNo', w0,
                'DocketNo', w0,
                'Remarks', w6,
                'UploadFiles', w7,
                'PreparedByUserId', w3,
                'PreparedDate', w8,
                'VerifiedByUserId', w3,
                'VerifiedDate', w8,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w8,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1,
                'AuthorizedStatus', w9
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, SupplyTypesDialog, SalesOrdersDialog, DeliveryNotesDialog, PaymentTermsDialog]); // referenced dialogs