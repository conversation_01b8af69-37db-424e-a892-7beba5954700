﻿CREATE TABLE [dbo].[Receipts]
(
    [ReceiptId]             INT             NOT NULL    IDENTITY (1, 1),
    [ReceiptNo]             NVARCHAR (50)   NOT NULL,
    [ReceiptDate]           DATETIME        NOT NULL,
    [CustomerId]            INT             NOT NULL,
    [ModeOfPaymentId]       INT             NOT NULL,

	[TotalReceivable]       DECIMAL (18, 2)     NULL,
    [OnAccount]             DECIMAL (18, 2)     NULL,
    [TDSRateId]			    INT			        NULL,
    [TCSRateId]             INT                 NULL,

    [AmountReceived]        DECIMAL (18, 2) NOT NULL,
    [FinancialYearId]       INT             NOT NULL,
    [Narration]             NVARCHAR (MAX)      NULL,

    [ChequeDDNo]            NVARCHAR (100)      NULL,
    [ChequeDDDate]          DATETIME            NULL,
    [BankBranchName]        NVARCHAR (250)      NULL,
    [PaymentRefNo]          NVARCHAR (250)      NULL,

    [Remarks]               NVARCHAR (MAX)      NULL,
    [ClientId]              INT	            NOT NULL    CONSTRAINT [DF_Receipts_ClientId]	DEFAULT ((0)),
    [TransactionStatus]     SMALLINT            NULL    DEFAULT ((0)),

    -------------------Authorization Details-------------
    [PreparedByUserId]      INT	                NULL,
    [PreparedDate]          DATETIME            NULL,
    [VerifiedByUserId]      INT                 NULL,
    [VerifiedDate]          DATETIME            NULL,
    [AuthorizedByUserId]    INT                 NULL,
    [AuthorizedDate]        DATETIME            NULL,
    [ModifiedByUserId]      INT                 NULL,
    [ModifiedDate]          DATETIME            NULL,
    [CancelledByUserId]     INT                 NULL,
    [CancelledDate]			DATETIME            NULL,
    [CancelReason]          NVARCHAR (MAX)      NULL,
    [AuthorizationStatus]   BIT             NOT NULL    CONSTRAINT [DF_Receipts_AuthorizationStatus]DEFAULT ((0)),
    -------------------Authorization Details-------------
    CONSTRAINT [FK_Receipts_PreparedByUsers]      FOREIGN KEY ([PreparedByUserId])	    REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Receipts_VerfiedByUsers]       FOREIGN KEY ([VerifiedByUserId])	    REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Receipts_AuthorizedByUsers]    FOREIGN KEY ([AuthorizedByUserId])    REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Receipts_ModifiedByUsers]      FOREIGN KEY ([ModifiedByUserId])	    REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Receipts_CancelledByUsers]     FOREIGN KEY ([CancelledByUserId])	    REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Receipts_Clients]	          FOREIGN KEY ([ClientId])	            REFERENCES	[dbo].[Clients]([ClientId]),
    -------------------Authorization Details End-------------
    CONSTRAINT [PK_Receipts] PRIMARY KEY CLUSTERED ([ReceiptId] ASC),
    CONSTRAINT [FK_Receipts_Customers]		FOREIGN KEY ([CustomerId])	    REFERENCES [dbo].[Customers] ([CustomerId]),
    CONSTRAINT [FK_Receipts_TDSRates]       FOREIGN KEY ([TDSRateId])	    REFERENCES [dbo].[TDSRates]  ([TDSRateId]),
    CONSTRAINT [FK_Receipts_TCSRates] FOREIGN KEY ([TCSRateId]) REFERENCES [dbo].[TCSRates] ([TCSRateId]),
    CONSTRAINT [FK_Receipts_ModeOfPayments] FOREIGN KEY ([ModeOfPaymentId])	REFERENCES [dbo].[ModeOfPayments] ([ModeOfPaymentId]),
    CONSTRAINT [FK_Receipts_FinancialYears] FOREIGN KEY ([FinancialYearId])	REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
);


GO
CREATE NONCLUSTERED INDEX [FinancialYearId]
    ON [dbo].[Receipts]([FinancialYearId] ASC);

GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[Receipts]([ClientId] ASC);

GO
CREATE NONCLUSTERED INDEX [CustomerId]
    ON [dbo].[Receipts]([CustomerId] ASC);