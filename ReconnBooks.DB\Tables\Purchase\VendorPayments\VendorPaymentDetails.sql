﻿CREATE TABLE [dbo].[VendorPaymentDetails]
(
    [VendorPaymentDetailId] INT             IDENTITY (1, 1) NOT NULL,
    [VendorPaymentId]       INT             NOT NULL,
    [PurchaseOrderId]       INT             NOT NULL,
    [VendorBillId]          INT             NULL,
    [VendorBillDetailId]    INT             NOT NULL,
    [AmountPaid]            DECIMAL (18, 2) NULL,
    CONSTRAINT [PK_VendorPaymentDetails] PRIMARY KEY CLUSTERED ([VendorPaymentDetailId] ASC),
    CONSTRAINT [FK_VendorPaymentDetails_VendorBillDetails] FOREIGN KEY ([VendorBillDetailId]) REFERENCES [dbo].[VendorBillDetails] ([VendorBillDetailId]),
    CONSTRAINT [FK_VendorPaymentDetails_VendorPayments] FOREIGN KEY ([VendorPaymentId]) REFERENCES [dbo].[VendorPayments] ([VendorPaymentId]),
    CONSTRAINT [FK_VendorPaymentDetails_PurchaseOrders] FOREIGN KEY ([PurchaseOrderId]) REFERENCES [dbo].[PurchaseOrders] ([PurchaseOrderId]),
    CONSTRAINT [FK_VendorPaymentDetails_VendorBills] FOREIGN KEY ([VendorBillId]) REFERENCES [dbo].[VendorBills] ([VendorBillId])
);


GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[VendorPaymentDetails]([PurchaseOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [VendorBills]
    ON [dbo].[VendorPaymentDetails]([VendorBillId] ASC);


GO
CREATE NONCLUSTERED INDEX [VendorBillDetails]
    ON [dbo].[VendorPaymentDetails]([VendorBillDetailId] ASC);

