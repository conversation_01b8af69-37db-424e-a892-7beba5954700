﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { EmployeesRow } from "./EmployeesRow";

export namespace EmployeesService {
    export const baseUrl = 'Default/Employees';

    export declare function Create(request: SaveRequest<EmployeesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<EmployeesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<EmployeesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<EmployeesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<EmployeesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<EmployeesRow>>;

    export const Methods = {
        Create: "Default/Employees/Create",
        Update: "Default/Employees/Update",
        Delete: "Default/Employees/Delete",
        Retrieve: "Default/Employees/Retrieve",
        List: "Default/Employees/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>EmployeesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}