﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { VendorBillsRow } from "./VendorBillsRow";

export interface VendorBillsColumns {
    RowNumber: Column<VendorBillsRow>;
    VendorBillNo: Column<VendorBillsRow>;
    VendorBillDate: Column<VendorBillsRow>;
    VendorName: Column<VendorBillsRow>;
    GSTIN: Column<VendorBillsRow>;
    PlaceOfSupplyStateName: Column<VendorBillsRow>;
    SupplyType: Column<VendorBillsRow>;
    NetTaxableAmount: Column<VendorBillsRow>;
    NetCGSTAmount: Column<VendorBillsRow>;
    NetSGSTAmount: Column<VendorBillsRow>;
    NetIGSTAmount: Column<VendorBillsRow>;
    GrandTotal: Column<VendorBillsRow>;
    VendorBillAmount: Column<VendorBillsRow>;
    PurchaseOrderNo: Column<VendorBillsRow>;
    SupplyDueDate: Column<VendorBillsRow>;
    DeliveryDate: Column<VendorBillsRow>;
    ShippingThru: Column<VendorBillsRow>;
    ShippingDocketNo: Column<VendorBillsRow>;
    PaymentDueDate: Column<VendorBillsRow>;
    TDSRate: Column<VendorBillsRow>;
    TDSAmount: Column<VendorBillsRow>;
    TCSRate: Column<VendorBillsRow>;
    TCSAmount: Column<VendorBillsRow>;
    VendorBillUpload: Column<VendorBillsRow>;
    PaymentTerms: Column<VendorBillsRow>;
    Remarks: Column<VendorBillsRow>;
    FinancialYearName: Column<VendorBillsRow>;
    VendorBillMonth: Column<VendorBillsRow>;
    PreparedByUserUsername: Column<VendorBillsRow>;
    PreparedDate: Column<VendorBillsRow>;
    VerifiedByUserUsername: Column<VendorBillsRow>;
    VerifiedDate: Column<VendorBillsRow>;
    AuthorizedByUserUsername: Column<VendorBillsRow>;
    AuthorizedDate: Column<VendorBillsRow>;
    ModifiedByUserUsername: Column<VendorBillsRow>;
    ModifiedDate: Column<VendorBillsRow>;
    CancelledByUserUsername: Column<VendorBillsRow>;
    CancelledDate: Column<VendorBillsRow>;
    AuthorizedStatus: Column<VendorBillsRow>;
    VendorBillStatus: Column<VendorBillsRow>;
    VendorBillId: Column<VendorBillsRow>;
}

export class VendorBillsColumns extends ColumnsBase<VendorBillsRow> {
    static readonly columnsKey = 'Default.VendorBills';
    static readonly Fields = fieldsProxy<VendorBillsColumns>();
}

[IndianNumberFormatter]; // referenced types