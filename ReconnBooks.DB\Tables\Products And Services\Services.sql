﻿CREATE TABLE [dbo].[Services] 
(
    [ServiceId]             BIGINT          NOT NULL    IDENTITY (1, 1),
    [ServiceName]           NVARCHAR (500)  NOT NULL,
    [ServiceCode]           NVARCHAR (50)       NULL,
    [ServiceDescription]    NVARCHAR (MAX)      NULL,
    [CommodityTypeId]       INT             NOT NULL,
    [UnitId]                INT             NOT NULL,
    [HSNSACCodeId]          INT                 NULL,
    [GSTRateId]             INT             NOT NULL,
    [ServiceStatus]         BIT             NOT NULL    CONSTRAINT [DF_Services_Status] DEFAULT ((0)),
    [CostPrice]             DECIMAL (18, 2)     NULL    CONSTRAINT [DF_Services_CostPrice] DEFAULT ((0)),
    [BillingPrice]          DECIMAL (18, 2)     NULL    CONSTRAINT [DF_Services_BillingPrice] DEFAULT ((0)),
    [Remarks]               NVARCHAR (MAX)      NULL,
    [ClientId]              INT             NOT NULL    CONSTRAINT [DF_Services_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_Services] PRIMARY KEY    CLUSTERED   ([ServiceId] ASC),
    CONSTRAINT [FK_Services_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_Services_Units]          FOREIGN KEY ([UnitId])          REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_Services_HSNSACCodes]    FOREIGN KEY ([HSNSACCodeId])    REFERENCES [dbo].[HSNSACCodes] ([HSNSACCodeId]),
    CONSTRAINT [FK_Services_GSTRates]       FOREIGN KEY ([GSTRateId])       REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_Services_Clients]        FOREIGN KEY ([ClientId])        REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [CK_Services_CostPrice]      CHECK       ([CostPrice]>=(0)),
    CONSTRAINT [CK_Services_BillingPrice]   CHECK       ([BillingPrice]>=(0))
);

GO
CREATE NONCLUSTERED INDEX [ServiceName]
    ON [dbo].[Services]([ServiceName] ASC);
GO
CREATE NONCLUSTERED INDEX [ServiceCode]
    ON [dbo].[Services]([ServiceCode] ASC);
