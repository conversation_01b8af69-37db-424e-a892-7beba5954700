﻿CREATE TABLE [dbo].[TDSRates] 
(
    [TDSRateId]         INT                 NOT NULL    IDENTITY (1, 1),
    [Transaction]       NVARCHAR (500)          NULL,
    [TDSRate]           DECIMAL (18, 2)     NOT NULL,
    [FinancialYearId]   INT                 NOT NULL,
    [Section]           NVARCHAR (20)           NULL,
    [Limit]             NVARCHAR (MAX)          NULL,
    [Deductee]          NVARCHAR (250)          NULL,
    [wefDate]           SMALLDATETIME           NULL,
    [IsDefault]         BIT                 NOT NULL,
    [Remarks]           NVARCHAR (MAX)          NULL,
    
    CONSTRAINT [PK_TDSRateId] PRIMARY KEY CLUSTERED ([TDSRateId] ASC),
    CONSTRAINT [FK_TDSRates_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId])
);

