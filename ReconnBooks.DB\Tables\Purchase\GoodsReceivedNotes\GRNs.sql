﻿CREATE TABLE [dbo].[GRNs]   --Goods Received Note
(
    [GRNId]                 INT             NOT NULL    IDENTITY (1, 1),
    [GRNNo]                 NVARCHAR (50)   NOT NULL,
    [GRNDate]               DATETIME            NULL,
    [GRNTypeId]             INT             NOT NULL,   --GRN Types may be "By PO, Cash Purchase, Labour Items,Repairs etc." selected from GRN Types master

    [VendorId]              INT             NOT NULL,   --Supplier Name
    [PurchaseOrderId]       INT                 NULL,

    [FinancialYearId]       INT                 NULL,
    [VendorDCInvoiceNo]     NVARCHAR (100)  NOT NULL,
    [VendorDCInvoiceDate]   DATETIME        NOT NULL,

    [ShippedThrough]        NVARCHAR (500)      NULL,   --Shipper name to be entered manually
    [ShippingDocketNo]      NVARCHAR (50)       NULL,
    [DeliveryAddress]       NVARCHAR (300)      NULL,
    [DeliveryCityId]        INT                 NULL,
    [DeliveryPinCode]       INT                 NULL,

    [VehicleNo]             NVARCHAR (50)       NULL,
    [VehicleType]           NVARCHAR (100)      NULL,
    [GatePassNo]            NVARCHAR (100)      NULL,   --Our factory Gate pass details to be entred here,
    [GatePassDate]          DATETIME            NULL,
    [Inspection]            NVARCHAR (MAX)      NULL,
    [ReceivedByEmployeeId]  INT                 NULL,
    [UploadDocuments]		NVARCHAR (MAX)      NULL,
	[Remarks]               NVARCHAR (MAX)      NULL,
    [ClientId]              INT             NOT NULL    CONSTRAINT [DF_GRNs_ClientId] DEFAULT ((0)),

    -------------------Authorization Details-------------
    [PreparedByUserId]          INT	            NULL,
    [PreparedDate]              DATETIME        NULL,
    [VerifiedByUserId]          INT             NULL,
    [VerifiedDate]              DATETIME        NULL,
    [AuthorizedByUserId]        INT             NULL,
    [AuthorizedDate]            DATETIME        NULL,
    [ModifiedByUserId]          INT             NULL,
    [ModifiedDate]              DATETIME        NULL,
    [CancelledByUserId]         INT             NULL,
    [CancelledDate]			    DATETIME        NULL,
    [AuthorizedStatus]          BIT         NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details-------------
    CONSTRAINT [FK_GRNs_PreparedByUsers]    FOREIGN KEY ([PreparedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_GRNs_VerfiedByUsers]     FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_GRNs_AuthorizedByUsers]  FOREIGN KEY ([AuthorizedByUserId])  REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_GRNs_ModifiedByUsers]    FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_GRNs_CancelledByUsers]   FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users] ([UserId]),
    CONSTRAINT [FK_GRNs_Clients]	        FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients]([ClientId]),
    -------------------Authorization Details End-------------
    CONSTRAINT [PK_GRNs]                    PRIMARY KEY CLUSTERED   ([GRNId] ASC),
    CONSTRAINT [FK_GRNs_GRNTypes]           FOREIGN KEY ([GRNTypeId])           REFERENCES [dbo].[GRNTypes]         ([GRNTypeId]),
    CONSTRAINT [FK_GRNs_Vendors]            FOREIGN KEY ([VendorId])            REFERENCES [dbo].[Vendors]          ([VendorId]),
    CONSTRAINT [FK_GRNs_FinancialYears]     FOREIGN KEY ([FinancialYearId])     REFERENCES [dbo].[FinancialYears]   ([FinancialYearId]),
    CONSTRAINT [FK_GRNs_PurchaseOrders]     FOREIGN KEY ([PurchaseOrderId])     REFERENCES [dbo].[PurchaseOrders]   ([PurchaseOrderId]),
    CONSTRAINT [FK_GRNs_Employees]          FOREIGN KEY (ReceivedByEmployeeId)  REFERENCES [dbo].[Employees]        ([EmployeeId]),
    CONSTRAINT [FK_GRNs_DeliveryCities]     FOREIGN KEY (DeliveryCityId)        REFERENCES [dbo].[Cities]           (CityId)
    
);

GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[GRNs]([VendorId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[GRNs]([PurchaseOrderId] ASC);
GO
CREATE NONCLUSTERED INDEX [GRNDate]
    ON [dbo].[GRNs]([GRNDate] ASC);
GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[GRNs]([FinancialYearId] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[GRNs]([ClientId] ASC);