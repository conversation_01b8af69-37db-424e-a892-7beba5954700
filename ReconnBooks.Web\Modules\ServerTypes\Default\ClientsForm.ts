﻿import { StringEditor, ServiceLookupEditor, ImageUploadEditor, IntegerEditor, PasswordEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { BusinessCategoriesDialog } from "../../Default/BusinessCategories/BusinessCategoriesDialog";
import { BusinessGroupsDialog } from "../../Default/BusinessGroups/BusinessGroupsDialog";
import { BusinessTypesDialog } from "../../Default/BusinessTypes/BusinessTypesDialog";
import { CitiesDialog } from "../../Default/Cities/CitiesDialog";
import { ClientBankAccountsGridEditor } from "../../Default/ClientBankAccounts/ClientBankAccountsGridEditor";
import { ConsultantsDialog } from "../../Default/Consultants/ConsultantsDialog";
import { DesignationsDialog } from "../../Default/Designations/DesignationsDialog";
import { TitlesDialog } from "../../Default/Titles/TitlesDialog";

export interface ClientsForm {
    ClientName: StringEditor;
    ClientCode: StringEditor;
    Address: StringEditor;
    Address2: StringEditor;
    CityId: ServiceLookupEditor;
    PINCode: StringEditor;
    ConsultantId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyId: ServiceLookupEditor;
    NatureOfSupplyId: ServiceLookupEditor;
    SupplyTypeId: ServiceLookupEditor;
    PAN: StringEditor;
    IECNo: StringEditor;
    CINNo: StringEditor;
    TANNo: StringEditor;
    UdyamNo: StringEditor;
    TitleId: ServiceLookupEditor;
    ClientContactName: StringEditor;
    DesignationId: ServiceLookupEditor;
    EMail: StringEditor;
    MobileNo: StringEditor;
    AlternateNo: StringEditor;
    PhoneNo: StringEditor;
    FaxNo: StringEditor;
    HomePage: StringEditor;
    ClientBankAccountsList: ClientBankAccountsGridEditor;
    Logo: ImageUploadEditor;
    TagLine: StringEditor;
    ClientDSC: ImageUploadEditor;
    EmailServerHost: StringEditor;
    EmailServerPort: IntegerEditor;
    EmailServerUsername: StringEditor;
    EmailServerPassword: PasswordEditor;
    BusinessTypeId: ServiceLookupEditor;
    BusinessGroupId: ServiceLookupEditor;
    BusinessCategoryId: ServiceLookupEditor;
    InvoiceNoFormat: StringEditor;
    Disclaimer: StringEditor;
}

export class ClientsForm extends PrefixedContext {
    static readonly formKey = 'Default.Clients';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ClientsForm.init)  {
            ClientsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = ClientBankAccountsGridEditor;
            var w3 = ImageUploadEditor;
            var w4 = IntegerEditor;
            var w5 = PasswordEditor;

            initFormType(ClientsForm, [
                'ClientName', w0,
                'ClientCode', w0,
                'Address', w0,
                'Address2', w0,
                'CityId', w1,
                'PINCode', w0,
                'ConsultantId', w1,
                'GSTIN', w0,
                'PlaceOfSupplyId', w1,
                'NatureOfSupplyId', w1,
                'SupplyTypeId', w1,
                'PAN', w0,
                'IECNo', w0,
                'CINNo', w0,
                'TANNo', w0,
                'UdyamNo', w0,
                'TitleId', w1,
                'ClientContactName', w0,
                'DesignationId', w1,
                'EMail', w0,
                'MobileNo', w0,
                'AlternateNo', w0,
                'PhoneNo', w0,
                'FaxNo', w0,
                'HomePage', w0,
                'ClientBankAccountsList', w2,
                'Logo', w3,
                'TagLine', w0,
                'ClientDSC', w3,
                'EmailServerHost', w0,
                'EmailServerPort', w4,
                'EmailServerUsername', w0,
                'EmailServerPassword', w5,
                'BusinessTypeId', w1,
                'BusinessGroupId', w1,
                'BusinessCategoryId', w1,
                'InvoiceNoFormat', w0,
                'Disclaimer', w0
            ]);
        }
    }
}

queueMicrotask(() => [CitiesDialog, ConsultantsDialog, TitlesDialog, DesignationsDialog, BusinessTypesDialog, BusinessGroupsDialog, BusinessCategoriesDialog]); // referenced dialogs