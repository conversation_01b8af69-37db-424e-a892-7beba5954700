﻿import { StringEditor, DateEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface FinancialYearsForm {
    FinancialYearName: StringEditor;
    FromDate: DateEditor;
    ToDate: DateEditor;
    Remarks: StringEditor;
}

export class FinancialYearsForm extends PrefixedContext {
    static readonly formKey = 'Default.FinancialYears';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!FinancialYearsForm.init)  {
            FinancialYearsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;

            initFormType(FinancialYearsForm, [
                'FinancialYearName', w0,
                'FromDate', w1,
                'ToDate', w1,
                'Remarks', w0
            ]);
        }
    }
}