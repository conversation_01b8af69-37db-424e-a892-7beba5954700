﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { InvoiceDetailsRow } from "./InvoiceDetailsRow";

export interface InvoiceDetailsColumns {
    RowNumber: Column<InvoiceDetailsRow>;
    CommodityName: Column<InvoiceDetailsRow>;
    CommodityCode: Column<InvoiceDetailsRow>;
    CommodityType: Column<InvoiceDetailsRow>;
    HSNSACCode: Column<InvoiceDetailsRow>;
    Quantity: Column<InvoiceDetailsRow>;
    RevisedQuantity: Column<InvoiceDetailsRow>;
    UnitName: Column<InvoiceDetailsRow>;
    UnitPrice: Column<InvoiceDetailsRow>;
    NetUnitAmount: Column<InvoiceDetailsRow>;
    DiscountPercent: Column<InvoiceDetailsRow>;
    NetDiscountAmount: Column<InvoiceDetailsRow>;
    TaxableAmountPerUnit: Column<InvoiceDetailsRow>;
    NetTaxableAmount: Column<InvoiceDetailsRow>;
    GSTRateRemarks: Column<InvoiceDetailsRow>;
    IGSTRate: Column<InvoiceDetailsRow>;
    IGSTAmountPerUnit: Column<InvoiceDetailsRow>;
    NetIGSTAmount: Column<InvoiceDetailsRow>;
    CGSTRate: Column<InvoiceDetailsRow>;
    CGSTAmountPerUnit: Column<InvoiceDetailsRow>;
    NetCGSTAmount: Column<InvoiceDetailsRow>;
    SGSTRate: Column<InvoiceDetailsRow>;
    SGSTAmountPerUnit: Column<InvoiceDetailsRow>;
    NetSGSTAmount: Column<InvoiceDetailsRow>;
    NetAmount: Column<InvoiceDetailsRow>;
    NetPricePerUnit: Column<InvoiceDetailsRow>;
    CommodityDescription: Column<InvoiceDetailsRow>;
    InvoiceDetailId: Column<InvoiceDetailsRow>;
    InvoiceNo: Column<InvoiceDetailsRow>;
}

export class InvoiceDetailsColumns extends ColumnsBase<InvoiceDetailsRow> {
    static readonly columnsKey = 'Default.InvoiceDetails';
    static readonly Fields = fieldsProxy<InvoiceDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types