﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, BooleanEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { VendorPaymentDetailsGridEditor } from "../../Default/VendorPaymentDetails/VendorPaymentDetailsGridEditor";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface VendorPaymentsForm {
    PaymentVoucherNo: StringEditor;
    PaymentVoucherDate: DateEditor;
    VendorId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    TotalPayable: DecimalEditor;
    ModeOfPaymentId: ServiceLookupEditor;
    OnAccount: DecimalEditor;
    TDSRateId: ServiceLookupEditor;
    TDSAmount: DecimalEditor;
    TCSRateId: ServiceLookupEditor;
    TCSAmount: DecimalEditor;
    NetPayable: DecimalEditor;
    AmountPaid: DecimalEditor;
    VendorPaymentDetailsList: VendorPaymentDetailsGridEditor;
    Narration: StringEditor;
    ChequeDdNo: StringEditor;
    ChequeDdDate: DateEditor;
    BankBranchName: StringEditor;
    PaymentRefNo: StringEditor;
    Remarks: StringEditor;
    AuthorizedStatus: BooleanEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class VendorPaymentsForm extends PrefixedContext {
    static readonly formKey = 'Default.VendorPayments';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!VendorPaymentsForm.init)  {
            VendorPaymentsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = DecimalEditor;
            var w5 = VendorPaymentDetailsGridEditor;
            var w6 = BooleanEditor;
            var w7 = DateTimeEditor;

            initFormType(VendorPaymentsForm, [
                'PaymentVoucherNo', w0,
                'PaymentVoucherDate', w1,
                'VendorId', w2,
                'FinancialYearId', w3,
                'TotalPayable', w4,
                'ModeOfPaymentId', w2,
                'OnAccount', w4,
                'TDSRateId', w2,
                'TDSAmount', w4,
                'TCSRateId', w2,
                'TCSAmount', w4,
                'NetPayable', w4,
                'AmountPaid', w4,
                'VendorPaymentDetailsList', w5,
                'Narration', w0,
                'ChequeDdNo', w0,
                'ChequeDdDate', w1,
                'BankBranchName', w0,
                'PaymentRefNo', w0,
                'Remarks', w0,
                'AuthorizedStatus', w6,
                'PreparedByUserId', w3,
                'PreparedDate', w7,
                'VerifiedByUserId', w3,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog]); // referenced dialogs