using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.PrintOptions")]
public class PrintOptionsForm
{
    [BooleanEditor]
    [DisplayName("Original Copy")]
    public bool Original { get; set; }
    
    [BooleanEditor]
    [Display<PERSON><PERSON>("Duplicate Copy")]
    public bool Duplicate { get; set; }
    
    [BooleanEditor]
    [Disp<PERSON><PERSON><PERSON>("Triplicate Copy")]
    public bool Triplicate { get; set; }
    
    [BooleanEditor]
    [DisplayName("Transporter Copy")]
    public bool TransporterCopy { get; set; }
    
    [BooleanEditor]
    [DisplayName("Supplier Copy")]
    public bool SupplierCopy { get; set; }
    
    [BooleanEditor]
    [DisplayName("Extra Copy")]
    public bool ExtraCopy { get; set; }
}
