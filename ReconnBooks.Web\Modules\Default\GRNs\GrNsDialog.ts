import { GrNsForm, GrNsRow, GrNsService, VendorsRow, CitiesRow, DocumentsRow} from '@/ServerTypes/Default';
import { Decorators, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.GrNsDialog')
@Decorators.panel()
export class GrNsDialog extends VerifyAuthorizeDialog<GrNsRow> {
    protected getFormKey() { return GrNsForm.formKey; }
    protected getRowDefinition() { return GrNsRow; }
    protected getService() { return GrNsService.baseUrl; }

    protected form = new GrNsForm(this.idPrefix);
    private docType: string;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.GrnDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.form.GrnDetailsList.getGrid().focus();

            const grid = this.form.GrnDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        //--Fetching Customer Billing Address--

        this.form.VendorId.change(a => {
            setTimeout(async () => {
                var VendorId = toId(this.form.VendorId.value);
                if (VendorId != null) {
                    var Vendor = (await VendorsRow.getLookupAsync()).itemById[VendorId];
                    this.form.GSTIN.value = Vendor.GSTIN;
                }
                else {
                    this.clearVendorFields();
                }
            }, 100);
        })

        this.form.PurchaseOrderId.changeSelect2(e => {
            if (this.form.PurchaseOrderId.value === '') {
                // Clear the details in the grid
                this.form.GrnDetailsList.value = [];
            }
            else {
                GrNsService.GetFromPurchaseOrderDetails({
                    EntityId: toId(this.form.PurchaseOrderId.value)
                },
                    response => {
                        this.form.GrnDetailsList.value = response.Entities;
                    });
            }
        });
        //--Financial Year--

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                    this.getNextNumber();
                    this.setDialogsLoadedState();
                });
            }
            else {
                this.getNextNumber();
                this.setDialogsLoadedState();
            }
        }
        this.setDialogsLoadedState();
    }

    //--Cloning a Document (Save As)--
    protected updateInterface() {
        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    //--Document Number Generation--
    private getNextNumber(): any {
        
        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "GRNs")[0].DocumentShortName;
        }
        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.GRNNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.GrnDetailsList.value || this.form.GrnDetailsList.value.length === 0) {
            alertDialog(" GRNs cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }
    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'GRNReport',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearVendorFields() {
        this.form.GSTIN.value = undefined;
        this.form.GrnDetailsList.value = undefined;

    }
}