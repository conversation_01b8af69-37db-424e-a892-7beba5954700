using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ReconnBooks.Web.Modules.Common.ChatSql
{
    [Route("api/chat-sql-test")]
    [ApiController]
    public class ChatSqlTestController : ControllerBase
    {
        private readonly ILogger<ChatSqlTestController> _logger;

        public ChatSqlTestController(ILogger<ChatSqlTestController> logger)
        {
            _logger = logger;
        }

        [HttpGet("ping")]
        public IActionResult Ping()
        {
            return Ok(new { message = "ChatSQL Test Controller is working", timestamp = DateTime.UtcNow });
        }

        [HttpGet("config")]
        public IActionResult GetConfig()
        {
            try
            {
                var config = new
                {
                    hasOpenAIConfig = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("OPENAI_API_KEY")),
                    environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    timestamp = DateTime.UtcNow
                };
                
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting configuration");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}
