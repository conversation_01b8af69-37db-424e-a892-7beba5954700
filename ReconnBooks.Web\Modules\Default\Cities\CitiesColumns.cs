using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.Cities")]
[BasedOnRow(typeof(CitiesRow), CheckNames = true)]
public class CitiesColumns
{
    public long RowNumber { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int CityId { get; set; }

    [EditLink, Width(200)]
    public string CityName { get; set; }

    [EditLink, Width(100)]
    public int PINCode { get; set; }

    [EditLink, Width(150)]
    public string District { get; set; }

    [EditLink, Width(150)]
    public string StateName { get; set; }
}