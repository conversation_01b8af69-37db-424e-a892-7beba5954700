﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.CommoditiesRow>;
using MyRow = ReconnBooks.Default.CommoditiesRow;

namespace ReconnBooks.Default;

public interface ICommoditiesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class CommoditiesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, ICommoditiesListHandler
{
    public CommoditiesListHandler(IRequestContext context)
            : base(context)
    {
    }
}