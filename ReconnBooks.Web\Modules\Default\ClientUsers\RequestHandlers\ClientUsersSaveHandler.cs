using PuppeteerSharp;
using ReconnBooks.Administration;
using Serenity.Services;
using System.Globalization;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.ClientUsersRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.ClientUsersRow;

namespace ReconnBooks.Default;

public interface IClientUsersSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientUsersSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IClientUsersSaveHandler
{
    private readonly IRequestContext context;
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;

    private MyRow.RowFields clientUsersRowFields { get { return MyRow.Fields; } }

    public ClientUsersSaveHandler(IRequestContext context, IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections)
            : base(context)
    {
        this.context = context;
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
    }

    protected override void SetInternalFields()
    {
        base.SetInternalFields();

        if (IsCreate)
        {
            //var userId = Convert.ToInt32(userAccessor.User.GetIdentifier(), CultureInfo.InvariantCulture);

            //foreach (var clientId in Row.ClientList)
            //{
            //    var sameClient = sqlConnections.NewFor<MyRow>().List<MyRow>(q =>
            //    {
            //        q.Select(clientUsersRowFields.ClientName)
            //            .Where(new Criteria(clientUsersRowFields.ClientId) == clientId
            //                    && new Criteria(clientUsersRowFields.UserId) == userId);
            //    });
            //    if (sameClient.IsEmptyOrNull())
            //    {
            //        //request.Entity.ClientId = client;
            //        //saveResponse = new MyRepository().Create(uow, request);
            //    }
            //    else
            //    {
            //        throw new ValidationError("This Client" + sameClient[0].ClientName + "is already present!");
            //    }
            //}
        }
        else
        {
            //var repository = new MyRepository();
            //SaveResponse oldSaveRespone = new SaveResponse(); //We need this variable to return old response if there is something wrong while updating with new clientID
            //var clientUsersRowFields = MyRow.Fields;
            //var activeUserClientRecord = GetActiveUserClientsRow(uow.Connection);

            //if (activeUserClientRecord != null)
            //{
            //    activeUserClientRecord.Status = false;
            //    oldSaveRespone = repository.Update(uow, new SaveRequest<MyRow> { Entity = activeUserClientRecord });
            //}

            //var newUserClientRecord = new UserClientsController().Retrieve(uow.Connection, new RetrieveRequest { EntityId = Convert.ToInt32(request.EntityId) }).Entity;

            //if (newUserClientRecord != null)
            //{
            //    newUserClientRecord.Status = true;
            //    ((UserDefinition)Authorization.UserDefinition).ClientId = newUserClientRecord.ClientId ?? 0;
            //    ((UserDefinition)Authorization.UserDefinition).ClientName = newUserClientRecord.ClientClientName;
            //    ((UserDefinition)Authorization.UserDefinition).ClientCode = newUserClientRecord.ClientClientCode;

            //    //TODO: what should be done if newUserClientRecord == null?
            //    return repository.Update(uow, new SaveRequest<MyRow> { Entity = newUserClientRecord });
            //}

            //return oldSaveRespone;
        }
    }

}