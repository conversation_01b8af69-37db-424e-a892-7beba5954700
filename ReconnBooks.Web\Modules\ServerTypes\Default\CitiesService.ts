﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CitiesRow } from "./CitiesRow";

export namespace CitiesService {
    export const baseUrl = 'Default/Cities';

    export declare function Create(request: SaveRequest<CitiesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CitiesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CitiesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CitiesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CitiesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CitiesRow>>;

    export const Methods = {
        Create: "Default/Cities/Create",
        Update: "Default/Cities/Update",
        Delete: "Default/Cities/Delete",
        Retrieve: "Default/Cities/Retrieve",
        List: "Default/Cities/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CitiesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}