﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { UserTypesRow } from "../Default/UserTypesRow";

export interface UserTypesColumns {
    UserTypeId: Column<UserTypesRow>;
    UserTypeName: Column<UserTypesRow>;
    Description: Column<UserTypesRow>;
}

export class UserTypesColumns extends ColumnsBase<UserTypesRow> {
    static readonly columnsKey = 'Default.UserTypes';
    static readonly Fields = fieldsProxy<UserTypesColumns>();
}