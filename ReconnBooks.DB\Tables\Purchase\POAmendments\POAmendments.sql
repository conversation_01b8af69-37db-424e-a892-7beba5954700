﻿CREATE TABLE [dbo].[POAmendments] (
    [POAmendmentId]         INT             IDENTITY (1, 1) NOT NULL,
    [POAmendmentNo]         NVARCHAR (50)   NOT NULL,
    [POAmendmentDate]       DATETIME        NOT NULL,
    
    [VendorId]              INT             NOT NULL,
    [FinancialYearId]       INT             NOT NULL,
    [PurchaseOrderId]       INT             NULL,
    [PurchaseOrderDetailId] INT             NOT NULL,
    [POAmendmentAmount]     DECIMAL (18, 2) NULL,
    [TDSRateId]             INT             NULL,
    [TCSRateId]             INT             NULL,
    
    [RoundingOff]           DECIMAL (18, 2) NULL,
    [GrandTotal]            DECIMAL (18, 2) NULL,
    [Remarks]               NVARCHAR (MAX)  NULL,
    [ClientId]              INT             CONSTRAINT [DF_POAmendments_ClientId] DEFAULT ((0)) NOT NULL,
    
    [PreparedByUserId]      INT             NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]         DATETIME        NULL,
    [AuthorizedStatus]      BIT             DEFAULT ((0)) NOT NULL,

    CONSTRAINT [PK_POAmendments] PRIMARY KEY CLUSTERED ([POAmendmentId] ASC),
    CONSTRAINT [FK_POAmendments_PurchaseOrderDetails] FOREIGN KEY ([PurchaseOrderDetailId]) REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_POAmendments_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_POAmendments_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_POAmendments_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_POAmendments_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_POAmendments_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_POAmendments_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_POAmendments_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_POAmendments_TDSRates] FOREIGN KEY ([TDSRateId]) REFERENCES [dbo].[TDSRates] ([TDSRateId]),
    CONSTRAINT [FK_POAmendments_TCSRates] FOREIGN KEY ([TCSRateId]) REFERENCES [dbo].[TCSRates] ([TCSRateId]),
    CONSTRAINT [FK_POAmendments_Vendors] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendors] ([VendorId])
);


GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[POAmendments]([VendorId] ASC);


GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[POAmendments]([PurchaseOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[POAmendments]([FinancialYearId] ASC);


GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[POAmendments]([ClientId] ASC);

