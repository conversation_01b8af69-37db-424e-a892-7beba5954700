﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CustomersRow } from "./CustomersRow";

export interface CustomersColumns {
    RowNumber: Column<CustomersRow>;
    CompanyName: Column<CustomersRow>;
    AddressedTo: Column<CustomersRow>;
    CompanyCode: Column<CustomersRow>;
    BillingAddress: Column<CustomersRow>;
    BillingCityCityName: Column<CustomersRow>;
    BillingPinCode: Column<CustomersRow>;
    GSTIN: Column<CustomersRow>;
    PlaceOfSupplyStateName: Column<CustomersRow>;
    NatureOfSupply: Column<CustomersRow>;
    SupplyType: Column<CustomersRow>;
    PAN: Column<CustomersRow>;
    IECNo: Column<CustomersRow>;
    MailingAddress: Column<CustomersRow>;
    MailingCityId: Column<CustomersRow>;
    MailingPINCode: Column<CustomersRow>;
    PhoneNo: Column<CustomersRow>;
    MobileNo: Column<CustomersRow>;
    FaxNo: Column<CustomersRow>;
    HomePage: Column<CustomersRow>;
    EMailId: Column<CustomersRow>;
    BankName: Column<CustomersRow>;
    BranchName: Column<CustomersRow>;
    AccountName: Column<CustomersRow>;
    AccountNumber: Column<CustomersRow>;
    IFSCCode: Column<CustomersRow>;
    BranchCode: Column<CustomersRow>;
    ClientName: Column<CustomersRow>;
    CustomerId: Column<CustomersRow>;
}

export class CustomersColumns extends ColumnsBase<CustomersRow> {
    static readonly columnsKey = 'Default.Customers';
    static readonly Fields = fieldsProxy<CustomersColumns>();
}