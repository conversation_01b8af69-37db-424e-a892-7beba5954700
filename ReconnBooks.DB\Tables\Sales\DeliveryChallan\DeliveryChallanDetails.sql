﻿CREATE TABLE [dbo].[DeliveryChallanDetails]
(
    [DeliveryChallanDetailId]   INT				NOT NULL	IDENTITY (1, 1),
    [DeliveryChallanId]			INT				NOT NULL,
    [CommodityTypeId]		    INT				NOT NULL,

    [ProductId]                 BIGINT			    NULL,
	[ProductDescription]        NVARCHAR (MAX)      NULL,
    [ServiceId]                 BIGINT			    NULL,
	[ServiceDescription]        NVARCHAR (MAX)      NULL,
    
    [Quantity]				    DECIMAL (18,2)	NOT NULL	CONSTRAINT [DF_DCDetails_Quantity]	DEFAULT ((1)),
    [UnitId]		            INT				NOT NULL,
    [SKU]		                NVARCHAR (100)		NULL,	
    [ItemSerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader.
    [ClientId]				    INT				    NULL	CONSTRAINT [DF_DCDetails_ClientId]	DEFAULT ((0)),


    CONSTRAINT [PK_DCDetails] PRIMARY KEY CLUSTERED ([DeliveryChallanDetailId] ASC),
    CONSTRAINT [FK_DCDetails_DCs]	    FOREIGN KEY ([DeliveryChallanId])	REFERENCES [dbo].[DeliveryChallans] ([DeliveryChallanId]),
    CONSTRAINT [FK_DCDetails_CommodityTypes]	FOREIGN KEY ([CommodityTypeId])	        REFERENCES [dbo].[CommodityTypes]([CommodityTypeId]),
    
    CONSTRAINT [FK_DCDetails_Products]	FOREIGN KEY ([ProductId])	REFERENCES [dbo].[Commodities]	([CommodityId]),
    CONSTRAINT [FK_DCDetails_Services]	FOREIGN KEY ([ServiceId])	REFERENCES [dbo].[Services]	([ServiceId]),

    CONSTRAINT [FK_DCDetails_Units]	    FOREIGN KEY ([UnitId])      REFERENCES [dbo].[Units]    ([UnitId]),
    CONSTRAINT [CK_DCQuantity]          CHECK ([Quantity]>(0)),
);
GO
CREATE NONCLUSTERED INDEX [DeliveryChallans]
    ON [dbo].[DeliveryChallanDetails]([DeliveryChallanId] ASC);
GO
CREATE NONCLUSTERED INDEX [Products]
    ON [dbo].[DeliveryChallanDetails]([ProductId] ASC);
GO
CREATE NONCLUSTERED INDEX [Services]
    ON [dbo].[DeliveryChallanDetails]([ServiceId] ASC);