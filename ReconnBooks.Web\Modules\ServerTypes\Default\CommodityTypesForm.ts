﻿import { StringEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface CommodityTypesForm {
    CommodityType: StringEditor;
    SetDefault: BooleanEditor;
}

export class CommodityTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.CommodityTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CommodityTypesForm.init)  {
            CommodityTypesForm.init = true;

            var w0 = StringEditor;
            var w1 = BooleanEditor;

            initFormType(CommodityTypesForm, [
                'CommodityType', w0,
                'SetDefault', w1
            ]);
        }
    }
}