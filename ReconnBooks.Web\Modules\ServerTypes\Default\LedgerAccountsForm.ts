﻿import { IntegerEditor, ServiceLookupEditor, DateEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface LedgerAccountsForm {
    LedgerAccountName: IntegerEditor;
    SecondaryGroupId: ServiceLookupEditor;
    LedgerCreationDate: DateEditor;
}

export class LedgerAccountsForm extends PrefixedContext {
    static readonly formKey = 'Default.LedgerAccounts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!LedgerAccountsForm.init)  {
            LedgerAccountsForm.init = true;

            var w0 = IntegerEditor;
            var w1 = ServiceLookupEditor;
            var w2 = DateEditor;

            initFormType(LedgerAccountsForm, [
                'LedgerAccountName', w0,
                'SecondaryGroupId', w1,
                'LedgerCreationDate', w2
            ]);
        }
    }
}