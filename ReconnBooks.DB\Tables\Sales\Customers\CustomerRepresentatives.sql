﻿CREATE TABLE [dbo].[CustomerRepresentatives] 
(
    [RepresentativeId] INT  NOT NULL    IDENTITY (1, 1),
    [CustomerId]       INT  NOT NULL,
    [EmployeeId]       INT  NOT NULL,

    CONSTRAINT [PK_CustomerRepresentatives] PRIMARY KEY CLUSTERED   ([RepresentativeId] ASC),
    CONSTRAINT [FK_CustomerRepresentatives_Customers]   FOREIGN KEY ([CustomerId])  REFERENCES [dbo].[Customers] ([CustomerId]),
);
GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[CustomerRepresentatives]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [Employees]
    ON [dbo].[CustomerRepresentatives]([EmployeeId] ASC);