﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ModeOfPaymentsRow } from "./ModeOfPaymentsRow";

export interface ModeOfPaymentsColumns {
    RowNumber: Column<ModeOfPaymentsRow>;
    ModeOfPayment: Column<ModeOfPaymentsRow>;
    Description: Column<ModeOfPaymentsRow>;
    ModeOfPaymentId: Column<ModeOfPaymentsRow>;
}

export class ModeOfPaymentsColumns extends ColumnsBase<ModeOfPaymentsRow> {
    static readonly columnsKey = 'Default.ModeOfPayments';
    static readonly Fields = fieldsProxy<ModeOfPaymentsColumns>();
}