using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using ReconnBooks.Modules.Default.Clients;

namespace ReconnBooks.Modules.Common.RowBehaviors;

public class MultiClientRowLookup<TRow> :
            RowLookupScript<TRow>
            where TRow : class, IRow, IMultiClientRow, new()
{
    public ITwoLevelCache TwoLevelCache { get; }
    public IUserAccessor UserAccessor { get; }

    public MultiClientRowLookup(ISqlConnections sqlConnections, ITwoLevelCache twoLevelCache, IUserAccessor userAccessor) : base(sqlConnections)
    {
        Expiration = TimeSpan.FromDays(-1);
        TwoLevelCache = twoLevelCache ?? throw new ArgumentNullException(nameof(twoLevelCache));
        UserAccessor = userAccessor ?? throw new ArgumentNullException(nameof(userAccessor));
    }

    protected override void PrepareQuery(SqlQuery query)
    {
        base.PrepareQuery(query);
        AddClientFilter(query);
    }

    protected void AddClientFilter(SqlQuery query)
    {
        var r = new TRow();
        query.Where(r.ClientIdField == UserAccessor.User.GetClientId().GetValueOrDefault());
    }

    public override string GetScript()
    {
        return TwoLevelCache.GetLocalStoreOnly("MultiTenantLookup:" +
                this.ScriptName + ":" +
                UserAccessor.User.GetClientId(),
                TimeSpan.FromHours(1),
            new TRow().GetFields().GenerationKey, () =>
            {
                return base.GetScript();
            });
    }
}