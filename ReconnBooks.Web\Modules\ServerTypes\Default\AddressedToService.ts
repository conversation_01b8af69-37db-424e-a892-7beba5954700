﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { AddressedToRow } from "./AddressedToRow";

export namespace AddressedToService {
    export const baseUrl = 'Default/AddressedTo';

    export declare function Create(request: SaveRequest<AddressedToRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<AddressedToRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<AddressedToRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<AddressedToRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<AddressedToRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<AddressedToRow>>;

    export const Methods = {
        Create: "Default/AddressedTo/Create",
        Update: "Default/AddressedTo/Update",
        Delete: "Default/AddressedTo/Delete",
        Retrieve: "Default/AddressedTo/Retrieve",
        List: "Default/AddressedTo/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>AddressedToService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}