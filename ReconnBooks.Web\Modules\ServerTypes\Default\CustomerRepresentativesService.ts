﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CustomerRepresentativesRow } from "./CustomerRepresentativesRow";

export namespace CustomerRepresentativesService {
    export const baseUrl = 'Default/CustomerRepresentatives';

    export declare function Create(request: SaveRequest<CustomerRepresentativesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CustomerRepresentativesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CustomerRepresentativesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CustomerRepresentativesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CustomerRepresentativesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CustomerRepresentativesRow>>;

    export const Methods = {
        Create: "Default/CustomerRepresentatives/Create",
        Update: "Default/CustomerRepresentatives/Update",
        Delete: "Default/CustomerRepresentatives/Delete",
        Retrieve: "Default/CustomerRepresentatives/Retrieve",
        List: "Default/CustomerRepresentatives/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CustomerRepresentativesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}