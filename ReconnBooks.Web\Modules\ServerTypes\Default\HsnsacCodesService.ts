﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { HsnsacCodesRow } from "./HsnsacCodesRow";

export namespace HsnsacCodesService {
    export const baseUrl = 'Default/HsnsacCodes';

    export declare function Create(request: SaveRequest<HsnsacCodesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<HsnsacCodesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<HsnsacCodesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<HsnsacCodesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<HsnsacCodesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<HsnsacCodesRow>>;

    export const Methods = {
        Create: "Default/HsnsacCodes/Create",
        Update: "Default/HsnsacCodes/Update",
        Delete: "Default/HsnsacCodes/Delete",
        Retrieve: "Default/HsnsacCodes/Retrieve",
        List: "Default/HsnsacCodes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>HsnsacCodesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}