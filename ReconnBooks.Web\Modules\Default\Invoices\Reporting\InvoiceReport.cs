using Microsoft.Net.Http.Headers;
using ReconnBooks.Administration;
using ReconnBooks.Default;
using ReconnBooks.Modules.Common.Reporting;
using Serenity.Reporting;
using System.Net;
using Microsoft.Extensions.DependencyInjection;
using ReconnBooks.Default.Endpoints;
using ReconnBooks.Modules.Common.Helpers;

namespace ReconnBooks.Modules.Default.Invoices.Reporting;


[Report("InvoiceReport")]
[ReportDesign("~/Modules/Default/Invoices/Reporting/InvoiceReport.cshtml")]
//[RequiredPermission(PermissionKeys.Security)]

public class InvoiceReport : ReportBase, IReport, ICustomizeHtmlToPdf
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;
    private readonly IServiceProvider serviceProvider;

    public InvoiceReport(IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections, IServiceProvider serviceProvider)
    {
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
        this.serviceProvider = serviceProvider;
    }

    public string ReportType { get; set; }

    protected ISqlConnections SqlConnections => sqlConnections ?? throw new ArgumentNullException(nameof(sqlConnections));

    public object GetData()
    {
        var data = new InvoiceReportData();

        using (var connection = SqlConnections.NewFor<InvoicesRow>())
        {
            var invoicesRetrieveHandler = serviceProvider.GetRequiredService<IInvoicesRetrieveHandler>();
            data.Invoice = invoicesRetrieveHandler.Retrieve(connection, new RetrieveRequest { EntityId = ID }).Entity;

            data.ReportType = ReportType;

            var customerFields = CustomersRow.Fields;
            data.Customer = connection.TryFirst<CustomersRow>(a =>
            {
                a.SelectTableFields();
                a.SelectNonTableFields();
                a.Select();
                a.Where(customerFields.CustomerId == data.Invoice.CustomerId.GetValueOrDefault());
            }) ?? new CustomersRow();

            var clientFields = ClientsRow.Fields;
            data.Client = connection.TryFirst<ClientsRow>(a =>
            {
                a.SelectTableFields();
                a.SelectNonTableFields();
                a.Select();
                a.Where(clientFields.ClientId == data.Invoice.ClientId.GetValueOrDefault());
            }) ?? new ClientsRow();

            var clientBankAccountFields = ClientBankAccountsRow.Fields;
            data.ClientBankAccount = connection.TryFirst<ClientBankAccountsRow>(a =>
            {
                a.SelectTableFields();
                a.SelectNonTableFields();
                a.Select();
                a.Where(clientBankAccountFields.ClientId == data.Invoice.ClientId.GetValueOrDefault());
            }) ?? new ClientBankAccountsRow();
           
            var deliveryNoteFields = DeliveryNotesRow.Fields;
            data.DeliveryNote = connection.TryFirst<DeliveryNotesRow>(a =>
            {
                a.Select(deliveryNoteFields.DeliveryNoteDate);
                a.Select(deliveryNoteFields.SalesOrderRefNo);
                a.Select(deliveryNoteFields.SalesOrderRefDate);
                a.Where(deliveryNoteFields.DeliveryNoteNo == data.Invoice.DeliveryNoteNo);
            }) ?? null;
        }
        return data;
    }

    public void Customize(IHtmlToPdfOptions options)
    {
        // you may customize HTML to PDF converter (WKHTML) parameters here, e.g. 
        // options.MarginsAll = "2cm";
    }
}

public class InvoiceReportData
{
    public string ReportType { get; set; }
    public InvoicesRow Invoice { get; set; }
    //public List<InvoiceDetailsRow> Details { get; set; }
    public CustomersRow Customer { get; set; }
    public ClientsRow Client { get; set; }
    public ClientBankAccountsRow ClientBankAccount { get; set; }
    public DeliveryNotesRow DeliveryNote { get; set; }
    public string InvoiceValueInWords => NumberToWordsConverter.Convert(Invoice.GrandTotal.GetValueOrDefault());
}








