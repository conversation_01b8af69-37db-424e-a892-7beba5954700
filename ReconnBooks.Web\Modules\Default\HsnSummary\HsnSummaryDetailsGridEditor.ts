import { Decorators, WidgetProps, Aggregators } from '@serenity-is/corelib';
import { HsnSummaryDetailsColumns, HsnSummaryDetailsRow } from '../../ServerTypes/Default';
import { GridEditorBase } from '@serenity-is/extensions';

@Decorators.registerEditor('ReconnBooks.Default.HsnSummaryDetailsGridEditor')
export class HsnSummaryDetailsGridEditor extends GridEditorBase<HsnSummaryDetailsRow> {
    protected getColumnsKey() { return HsnSummaryDetailsColumns.columnsKey; }

    protected getColumns() {
        let columns = super.getColumns();
        //.filter(x => x.field != ReceiptDetailsRow.Fields.AmountPaid && x.field != ReceiptDetailsRow.Fields.TdsAmount && x.field != ReceiptDetailsRow.Fields.TdsPercent && x.field != ReceiptDetailsRow.Fields.LineTotal));
        //return columns.filter(x => x.field != HsnSummaryDetailsRow.Fields.HsnSummaryId);

        return columns;
    }


    // --- TO add Colum summary on grid.make sure to enable footer on grid
    protected getSlickOptions() {
        let opt = super.getSlickOptions();
        opt.showFooterRow = true;
        opt.footerRowHeight = 25;
        return opt;
    }

    protected createSlickGrid() {
        var grid = super.createSlickGrid();

        this.view.setSummaryOptions({
            aggregators: [
                new Aggregators.Sum('NetTaxableAmount'),
                new Aggregators.Sum('NetIGSTAmount'),
                new Aggregators.Sum('NetCGSTAmount'),
                new Aggregators.Sum('NetSGSTAmount'),
                new Aggregators.Sum('NetAmount'),
            ]
        });

        return grid;
    }

    protected getButtons() {
        return [];
    }
    
}