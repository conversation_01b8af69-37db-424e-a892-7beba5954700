﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { VendorPaymentDetailsRow } from "./VendorPaymentDetailsRow";

export namespace VendorPaymentDetailsService {
    export const baseUrl = 'Default/VendorPaymentDetails';

    export declare function Create(request: SaveRequest<VendorPaymentDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorPaymentDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorPaymentDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorPaymentDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorPaymentDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorPaymentDetailsRow>>;

    export const Methods = {
        Create: "Default/VendorPaymentDetails/Create",
        Update: "Default/VendorPaymentDetails/Update",
        Delete: "Default/VendorPaymentDetails/Delete",
        Retrieve: "Default/VendorPaymentDetails/Retrieve",
        List: "Default/VendorPaymentDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>VendorPaymentDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}