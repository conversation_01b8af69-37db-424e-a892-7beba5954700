using ReconnBooks.Default;
using Serenity.Navigation;

namespace ReconnBooks.Modules.Common.Navigation;

public interface ISidebarModel
{
    ClientDetails ActiveClient { get; }
    INavigationModel NavigationModel { get; }
    string UserCompanyName { get; }
    string UserDisplayName { get; }
    string UserCompanyNameInitials { get; }
    string UserCompanyColorCode { get; }
    string UserDisplayNameInitials { get; }
    string UserDisplayNameColorCode { get; }
    string ClientNameInitials { get; }
    string ClientNameColorCode { get; }
    string UserLogo { get; }
    string UserCompanyLogo { get; }
}

public class SidebarModel : ISidebarModel
{
    public INavigationModel NavigationModel { get; internal set; }
    public ClientDetails ActiveClient { get; internal set; }
    public string UserCompanyName { get; internal set; }
    public string UserDisplayName { get; internal set; }
    public string UserCompanyNameInitials { get; internal set; }
    public string UserCompanyColorCode { get; internal set; }
    public string UserDisplayNameInitials { get; internal set; }
    public string UserDisplayNameColorCode { get; internal set; }
    public string ClientNameInitials { get; internal set; }
    public string ClientNameColorCode { get; internal set; }
    public string UserLogo { get; internal set; }
    public string UserCompanyLogo { get; internal set; }
}

public class ClientDetails
{
    public int ClientId { get; set; }
    public string ClientName { get; set; }
    public string ClientCode { get; set; }
    public string ClientLogo { get; set; }
}
