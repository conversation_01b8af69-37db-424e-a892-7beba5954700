﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface DepartmentsForm {
    DepartmentName: StringEditor;
    Description: StringEditor;
}

export class DepartmentsForm extends PrefixedContext {
    static readonly formKey = 'Default.Departments';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DepartmentsForm.init)  {
            DepartmentsForm.init = true;

            var w0 = StringEditor;

            initFormType(DepartmentsForm, [
                'DepartmentName', w0,
                'Description', w0
            ]);
        }
    }
}