# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
- dev_main

pool:
  vmImage: 'windows-latest'

variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:

# Install Node.js (latest LTS version)
- task: UseNode@1
  inputs:
    version: '21.6.1'  # Use the latest LTS version of Node.js
    checkLatest: true     # Ensure the latest version is fetched

- task: NuGetToolInstaller@1

- task: NuGetCommand@2
  inputs:
    restoreSolution: '$(solution)'
    feedsToUse: 'config'

- task: VSBuild@1
  inputs:
    solution: '$(solution)'
    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:DesktopBuildPackageLocation="$(build.artifactStagingDirectory)\WebApp.zip" /p:DeployIisAppPath="Default Web Site"'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'

- task: VSTest@2
  inputs:
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'


    
# - task: DotNetCoreCLI@2
#   inputs:
#     command: publish
#    # publishWebProjects: True
#     arguments: '--configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)'
#     #zipAfterPublish: True

- task: PublishPipelineArtifact@1
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)' 
    artifactName: 'myWebsite'
  
# Optional: Verify Node.js and npm versions (debugging step)
- script: |
    node --version
    npm --version
  displayName: 'Verify Node.js and npm versions'