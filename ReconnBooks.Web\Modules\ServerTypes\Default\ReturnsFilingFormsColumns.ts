﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ReturnsFilingFormsRow } from "./ReturnsFilingFormsRow";

export interface ReturnsFilingFormsColumns {
    RowNumber: Column<ReturnsFilingFormsRow>;
    ReturnsFilingForm: Column<ReturnsFilingFormsRow>;
    NatureOfSupply: Column<ReturnsFilingFormsRow>;
    SupplyType: Column<ReturnsFilingFormsRow>;
    Description: Column<ReturnsFilingFormsRow>;
    Filer: Column<ReturnsFilingFormsRow>;
    Frequency: Column<ReturnsFilingFormsRow>;
    DueDate: Column<ReturnsFilingFormsRow>;
    Remarks: Column<ReturnsFilingFormsRow>;
    ReturnsFilingFormId: Column<ReturnsFilingFormsRow>;
}

export class ReturnsFilingFormsColumns extends ColumnsBase<ReturnsFilingFormsRow> {
    static readonly columnsKey = 'Default.ReturnsFilingForms';
    static readonly Fields = fieldsProxy<ReturnsFilingFormsColumns>();
}