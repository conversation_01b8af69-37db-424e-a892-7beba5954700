using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.SalesOrderDetailsRow>;
using MyRow = ReconnBooks.Default.SalesOrderDetailsRow;

namespace ReconnBooks.Default;

public interface ISalesOrderDetailsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class SalesOrderDetailsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, ISalesOrderDetailsListHandler
{
    public SalesOrderDetailsListHandler(IRequestContext context)
            : base(context)
    {
    }
    protected override void OnReturn()
    {
        base.OnReturn();
        foreach (var entity in Response.Entities)
        {
            entity.OrderUnitAmount = entity.OrderQuantity.GetValueOrDefault() * entity.OrderUnitPrice.GetValueOrDefault();
            entity.NetTaxableAmount = entity.OrderUnitAmount - entity.NetDiscountAmount.GetValueOrDefault();
            
            entity.TaxableAmountPerUnit = entity.NetTaxableAmount / entity.OrderQuantity;
            
            entity.NetIGSTAmount = entity.NetTaxableAmount * entity.IGSTRate.GetValueOrDefault() / 100;
            entity.NetSGSTAmount = entity.NetTaxableAmount * entity.SGSTRate.GetValueOrDefault() / 100;
            entity.NetCGSTAmount = entity.NetTaxableAmount * entity.CGSTRate.GetValueOrDefault() / 100;
            
            entity.IGSTAmountPerUnit = entity.NetIGSTAmount / entity.OrderQuantity;
            entity.SGSTAmountPerUnit = entity.NetSGSTAmount / entity.OrderQuantity;
            entity.CGSTAmountPerUnit = entity.NetCGSTAmount / entity.OrderQuantity;
        }
    }
}