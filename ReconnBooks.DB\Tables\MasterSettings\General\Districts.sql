﻿CREATE TABLE [dbo].[Districts] (
    [DistrictId]          INT               NOT NULL   IDENTITY (1, 1),
    [District]            NVARCHAR (100)    NOT NULL,
    [DistrictCode]        NVARCHAR (10)         NULL,
    [Headquarters]        NVARCHAR (100)        NULL,
    [StateId]             INT               NOT NULL,

    CONSTRAINT [PK_Districts] PRIMARY KEY CLUSTERED ([DistrictId] ASC),
    CONSTRAINT [FK_Districts_States] FOREIGN KEY ([StateId]) REFERENCES [dbo].[States] ([StateId]),
);

GO
CREATE NONCLUSTERED INDEX [StateId]
    ON [dbo].[Districts]([StateId] ASC);

