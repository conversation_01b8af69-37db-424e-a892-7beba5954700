﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface SalesRegisterRow {
    SalesRegisterId?: number;
    Date?: string;
    Narration?: string;
    InvoiceId?: number;
    ReceiptId?: number;
    Debit?: number;
    Credit?: number;
    TDSAmount?: number;
    TCSAmount?: number;
    CompanyName?: string;
    DocumentNo?: string;
    Balance?: number;
}

export abstract class SalesRegisterRow {
    static readonly idProperty = 'SalesRegisterId';
    static readonly nameProperty = 'Narration';
    static readonly localTextPrefix = 'Default.SalesRegister';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SalesRegisterRow>();
}