﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { OpeningBalancesRow } from "./OpeningBalancesRow";

export namespace OpeningBalancesService {
    export const baseUrl = 'Default/OpeningBalances';

    export declare function Create(request: SaveRequest<OpeningBalancesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<OpeningBalancesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<OpeningBalancesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<OpeningBalancesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<OpeningBalancesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<OpeningBalancesRow>>;

    export const Methods = {
        Create: "Default/OpeningBalances/Create",
        Update: "Default/OpeningBalances/Update",
        Delete: "Default/OpeningBalances/Delete",
        Retrieve: "Default/OpeningBalances/Retrieve",
        List: "Default/OpeningBalances/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>OpeningBalancesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}