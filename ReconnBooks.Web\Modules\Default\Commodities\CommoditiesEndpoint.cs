using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using ReconnBooks.AppServices;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using MyRow = ReconnBooks.Default.CommoditiesRow;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/Commodities/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class CommoditiesEndpoint : ServiceEndpoint
{
    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] ICommoditiesSaveHandler handler)
    {
        return handler.Create(uow, request);
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] ICommoditiesSaveHandler handler)
    {
        return handler.Update(uow, request);
    }
 
    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] ICommoditiesDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] ICommoditiesRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] ICommoditiesListHandler handler)
    {
        return handler.List(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] ICommoditiesListHandler handler,
        [FromServices] IExcelExporter exporter)
    {
        var data = List(connection, request, handler).Entities;
        var bytes = exporter.Export(data, typeof(Columns.CommoditiesColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "CommoditiesList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }

    [HttpPost]
    public ExcelImportResponse ExcelImport(IUnitOfWork uow, ExcelImportRequest request,
    [FromServices] IUploadStorage uploadStorage,
    [FromServices] IServiceResolver<ICommoditiesSaveHandler> saveHandlerResolver,
    [FromServices] IUserAccessor userAccessor, [FromServices] IUserRetrieveService userRetriever)
    {
        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return new ExcelImportResponse { ErrorList = ["Error: No User found!"]}; 
        }

        if(user.ClientId == null || user.ClientId == 0)
        {
            return new ExcelImportResponse { ErrorList = ["Error: No Client found!"]}; 
        }

        ArgumentNullException.ThrowIfNull(request);
        ArgumentException.ThrowIfNullOrEmpty(request.FileName);
        ArgumentNullException.ThrowIfNull(uploadStorage);

        UploadPathHelper.CheckFileNameSecurity(request.FileName);

        if (!request.FileName.StartsWith("temporary/", StringComparison.OrdinalIgnoreCase))
            throw ArgumentExceptions.OutOfRange(request.FileName);

        ExcelPackage ep = new();
        using (var fs = uploadStorage.OpenFile(request.FileName))
            ep.Load(fs);

        var c = MyRow.Fields;
        var ct = CommodityTypesRow.Fields;
        var u = UnitsRow.Fields;
        var gst = GstRatesRow.Fields;

        var response = new ExcelImportResponse
        {
            ErrorList = []
        };

        var worksheet = ep.Workbook.Worksheets[0];

        // Get the GSTRateID with 'NIL GST' as remark
        var nilGstRate = uow.Connection.TryFirst<GstRatesRow>(q => q
            .Select(gst.GSTRateId)
            .Where(gst.Remarks == "NIL GST"));

        if (nilGstRate == null)
        {
            response.ErrorList.Add("Error: No GSTRate with 'NIL GST' found!");
            return response;
        }

        // Get the CommodityTypeID with 'Goods' as CommodityType
        var goodsCommodityType = uow.Connection.TryFirst<CommodityTypesRow>(q => q
            .Select(ct.CommodityTypeId)
            .Where(ct.CommodityType == "Goods"));

        if (goodsCommodityType == null)
        {
            response.ErrorList.Add("Error: No CommodityType with 'Goods' as CommodityType found!");
            return response;
        }

        //get all units from the database
        var units = uow.Connection.List<UnitsRow>(q => q
            .Select(u.UnitId, u.UnitName)
            //.Where(u.ClientId == user.ClientId.val)
            );

        for (var row = 2; row <= worksheet.Dimension.End.Row; row++)
        {
            try
            {
                var commodityName = Convert.ToString(worksheet.Cells[row, 2].Value ?? "", CultureInfo.CurrentCulture);
                if (string.IsNullOrWhiteSpace(commodityName))
                    continue;

                var commodity = uow.Connection.TryFirst<MyRow>(q => q
                    .Select(c.CommodityId)
                    .Where(c.CommodityName == commodityName));

                if (commodity == null)
                    commodity = new MyRow
                    {
                        CommodityName = commodityName
                    };
                else
                {
                    // avoid assignment errors
                    ((IRow)commodity).TrackWithChecks = false;
                }

                // Set CommodityTypeID to the one with 'Goods' as CommodityType
                commodity.CommodityTypeId = goodsCommodityType.CommodityTypeId.Value;

                var unitName = Convert.ToString(worksheet.Cells[row, 3].Value ?? "", CultureInfo.CurrentCulture);
                if (!string.IsNullOrWhiteSpace(unitName))
                {
                    var unit = units.FirstOrDefault(u => u.UnitName == unitName);

                    if (unit == null)
                    {
                        response.ErrorList.Add("Error On Row " + row + ": Unit with name '" +
                            unitName + "' is not found!");
                        continue;
                    }

                    commodity.UnitId = unit.UnitId.Value;
                }

                commodity.SalesPrice = Convert.ToDecimal(worksheet.Cells[row, 4].Value ?? 0, CultureInfo.CurrentCulture);

                // Set GSTRateID to the one with 'NIL GST' as remark
                commodity.GSTRateId = nilGstRate.GSTRateId.Value;

                if (commodity.CommodityId == null)
                {
                    //commodity.ApplyDefaultValues(unassignedOnly: true);

                    saveHandlerResolver.Resolve().Create(uow, new SaveRequest<MyRow>
                    {
                        Entity = commodity
                    });

                    response.Inserted++;
                }
                else
                {
                    saveHandlerResolver.Resolve().Update(uow, new SaveRequest<MyRow>
                    {
                        Entity = commodity,
                        EntityId = commodity.CommodityId.Value
                    });

                    response.Updated++;
                }
            }
            catch (Exception ex)
            {
                response.ErrorList.Add("Exception on Row " + row + ": " + ex.Message);
            }
        }

        return response;
    }
}