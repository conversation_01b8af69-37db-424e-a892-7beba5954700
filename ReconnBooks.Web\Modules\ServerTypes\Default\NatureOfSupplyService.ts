﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { NatureOfSupplyRow } from "./NatureOfSupplyRow";

export namespace NatureOfSupplyService {
    export const baseUrl = 'Default/NatureOfSupply';

    export declare function Create(request: SaveRequest<NatureOfSupplyRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<NatureOfSupplyRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<NatureOfSupplyRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<NatureOfSupplyRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<NatureOfSupplyRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<NatureOfSupplyRow>>;

    export const Methods = {
        Create: "Default/NatureOfSupply/Create",
        Update: "Default/NatureOfSupply/Update",
        Delete: "Default/NatureOfSupply/Delete",
        Retrieve: "Default/NatureOfSupply/Retrieve",
        List: "Default/NatureOfSupply/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>NatureOfSupplyService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}