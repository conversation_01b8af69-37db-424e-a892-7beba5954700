﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { FinancialYearsRow } from "./FinancialYearsRow";

export namespace FinancialYearsService {
    export const baseUrl = 'Default/FinancialYears';

    export declare function Create(request: SaveRequest<FinancialYearsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<FinancialYearsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<FinancialYearsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<FinancialYearsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<FinancialYearsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<FinancialYearsRow>>;

    export const Methods = {
        Create: "Default/FinancialYears/Create",
        Update: "Default/FinancialYears/Update",
        Delete: "Default/FinancialYears/Delete",
        Retrieve: "Default/FinancialYears/Retrieve",
        List: "Default/FinancialYears/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>FinancialYearsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}