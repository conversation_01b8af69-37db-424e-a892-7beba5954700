import { faIcon } from "@serenity-is/corelib";
import { className } from "jsx-dom/min";
import { Todo } from "./todo-types";

function klass(todo: Todo) {
    return className([todo.done && "s-todo-done"]);
}

function timeAgo(date: Date): string {
    const now = new Date();
    const diff = Math.floor((now.getTime() - new Date(date).getTime()) / 60000); // minutes
    if (diff < 60) return `${diff} min${diff !== 1 ? 's' : ''} ago`;
    const hrs = Math.floor(diff / 60);
    if (hrs < 24) return `${hrs} hour${hrs !== 1 ? 's' : ''} ago`;
    const days = Math.floor(hrs / 24);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
}

export const TodoItem = ({ todo }: { todo: Todo }) =>
    <li class={klass(todo)} {...{ todo }}>
        <span class="handle"><i class={faIcon("grip-vertical")}></i></span>
        <input type="checkbox" class="form-check-input" checked={todo.done} onChange={e => {
            todo.done = !todo.done;
            (e.target as Element).closest('li')?.setAttribute("class", klass(todo));
            localStorage.setItem("todos", JSON.stringify(window.todoData));
        }} />
        <span class="text">{todo.text}</span>
        <small class="label"><i class={faIcon("clock")}></i> {timeAgo(todo.createdAt)}</small>
    </li>;
