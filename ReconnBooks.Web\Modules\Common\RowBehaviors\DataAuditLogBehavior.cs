using Microsoft.Extensions.Logging;
using System.Reflection;
using System.Globalization;
using ReconnBooks.Default;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;

namespace ReconnBooks.Default.DataAuditLog;

public class DataAuditLogBehavior : BaseSaveDeleteBehavior, IImplicitBehavior
{
    private readonly ISqlConnections sqlConnections;
    private readonly ILogger<DataAuditLogBehavior> logger;

    public DataAuditLogBehavior(ISqlConnections sqlConnections, ILogger<DataAuditLogBehavior> logger = null)
    {
        this.sqlConnections = sqlConnections ?? throw new ArgumentNullException(nameof(sqlConnections));
        this.logger = logger;
    }
    private DataAuditLogAttribute auditLogAttr;

    public bool ActivateFor(IRow row)
    {
        if (row is not IIdRow)
            return false;

        auditLogAttr = row.GetType().GetCustomAttribute<DataAuditLogAttribute>();
        if (auditLogAttr is null)
            return false;

        return true;
    }

    public override void OnAudit(IDeleteRequestHandler handler)
    {
        if (handler.Row == null)
            return;

        Log(handler.UnitOfWork, handler.Row, null, 
            handler.Context.User.GetIdentifier(), handler.Context.User.GetClientId());
    }

    public override void OnAudit(ISaveRequestHandler handler)
    {
        if (handler.Row == null)
            return;

        if (handler.IsCreate)
        {
            Log(handler.UnitOfWork, null, handler.Row, 
                handler.Context.User.GetIdentifier(), handler.Context.User.GetClientId());

            return;
        }

        bool anyChanged = false;
        foreach (var field in handler.Row.GetTableFields())
        {
            if (field.IndexCompare(handler.Old, handler.Row) != 0)
            {
                anyChanged = true;
                break;
            }
        }

        if (anyChanged)
            Log(handler.UnitOfWork, handler.Old, handler.Row, 
                handler.Context.User.GetIdentifier(), handler.Context.User.GetClientId());
    }

    private void Log(IUnitOfWork uow, IRow old, IRow row, object userId, object clientId)
    {
        if (old is null && row is null)
            throw new ArgumentNullException(nameof(old));

        var now = DateTime.UtcNow;

        var rowFields = (old ?? row).GetFields();
        var logRow = new ReconnBooks.Default.DataAuditLogRow();
        var ckRow = (old ?? row).GetType().GetCustomAttribute<ConnectionKeyAttribute>();
        var ckLog = logRow.GetType().GetCustomAttribute<ConnectionKeyAttribute>();
        var sameConnection = ckRow != null && ckLog != null && ckRow.Value == ckLog.Value;
        var queue = new Queue<DataAuditLogRow>();

        ((IRow)logRow).TrackAssignments = true;
        logRow.UserId = userId == null ? (int?)null : Convert.ToInt32(userId, 
            CultureInfo.InvariantCulture);
        logRow.ClientId = clientId == null ? (int?)null : Convert.ToInt32(clientId,
            CultureInfo.InvariantCulture);
        logRow.LogType = old == null ? DataAuditLogType.Insert : 
            (row == null ? DataAuditLogType.Delete : DataAuditLogType.Update);
        logRow.Tablename = (old ?? row).Table;
        var idField = (row ?? old).IdField;
        var idValue = idField.AsObject(row ?? old);
        logRow.RecordId = idValue == null ? null : Convert.ToString(idValue, 
            CultureInfo.InvariantCulture);
        logRow.LogDate = now;

        if (logRow.LogType == DataAuditLogType.Delete)
        {
            if (sameConnection)
                uow.Connection.Insert(logRow);
            else
                queue.Enqueue(logRow);
        }
        else if (logRow.LogType == DataAuditLogType.Insert)
        {
            foreach (var field in EnumerateCapturedFields(rowFields))
            {
                if (row.IsAssigned(field) &&
                    !field.IsNull(row))
                {
                    var clone = logRow.Clone();
                    clone.FieldName = field.Name;
                    clone.NewValue = SerializeValue(field.AsObject(row));

                    if (sameConnection)
                        uow.Connection.Insert(clone);
                    else
                        queue.Enqueue(clone);
                }
            }
        }
        else if (logRow.LogType == DataAuditLogType.Update)
        {
            foreach (var field in EnumerateCapturedFields(rowFields))
            {
                if (row.IsAssigned(field) && field.IndexCompare(old, row) != 0)
                {
                    var clone = logRow.Clone();
                    clone.FieldName = field.Name;
                    clone.OldValue = SerializeValue(field.AsObject(old));
                    clone.NewValue = SerializeValue(field.AsObject(row));
                    if (sameConnection)
                        uow.Connection.Insert(clone);
                    else
                        queue.Enqueue(clone);
                }
            }
        }
        else
            throw new InvalidOperationException("Audit Log Type?: " + logRow.LogType);

        if (queue.Count > 0)
        {
            uow.OnCommit += () =>
            {
                try
                {
                    using var connection = sqlConnections.NewFor<DataAuditLogRow>();
                    while (queue.Count > 0)
                        connection.Insert(queue.Dequeue());
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "Error inserting data audit log record!");
                }
            };
        }
    }

    private static IEnumerable<Field> EnumerateCapturedFields(RowFieldsBase fields)
    {
        foreach (var logField in fields)
        {
            if (!logField.IsTableField())
                continue;

            yield return logField;
        }
    }

    private static string SerializeValue(object value)
    {
        if (value == null)
            return null;

        if (value is string)
            return value as string;

        if (value is DateTime date)
        {
            if (date.Date == date)
                return date.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            else if (date.Kind == DateTimeKind.Utc)
                return date.ToString("yyyy-MM-ddTHH:mm:ss.fffZ", CultureInfo.InvariantCulture);
            else
                return date.ToString("yyyy-MM-ddTHH:mm:ss.fff", CultureInfo.InvariantCulture);
        }

        return Convert.ToString(value, CultureInfo.InvariantCulture);
    }
}