﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CustomerDetailsRow } from "./CustomerDetailsRow";

export interface CustomerDetailsColumns {
    CustomerDetailId: Column<CustomerDetailsRow>;
    LastContactedByEmployeeFirstName: Column<CustomerDetailsRow>;
    LastContactDate: Column<CustomerDetailsRow>;
    Email: Column<CustomerDetailsRow>;
    SendBulletin: Column<CustomerDetailsRow>;
}

export class CustomerDetailsColumns extends ColumnsBase<CustomerDetailsRow> {
    static readonly columnsKey = 'Default.CustomerDetails';
    static readonly Fields = fieldsProxy<CustomerDetailsColumns>();
}