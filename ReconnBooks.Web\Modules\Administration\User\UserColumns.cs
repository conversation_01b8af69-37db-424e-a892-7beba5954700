using ReconnBooks.Default;

namespace ReconnBooks.Administration.Columns;

[ColumnsScript("Administration.User")]
[BasedOnRow(typeof(UserRow), CheckNames = true)]
public class UserColumns
{
    //--------- To Add serial Nos. -----------
    [Di<PERSON><PERSON><PERSON><PERSON>("SL.No."), NotMapped, Width(50), AlignCenter]
    public long RowNumber { get; set; }
    //-----------------------
    [Hidden]
    [DisplayName("User Type"), Width(100)]
    public string UserTypeId { get; set; }

    [EditLink, DisplayName("User Type"), Width(100)]
    public string UserTypeName { get; set; }

    [EditLink, Width(200)]
    public string Username { get; set; }

    [DisplayName("Client Name"), Width(200), QuickSearch]
    public int? ClientName { get; set; }

    [DisplayName("Consultant Name"), Width(200), QuickSearch]
    public int? ConsultantName { get; set; }

    [Width(32)]
    public string ImpersonationToken { get; set; }

    [Hidden]
    [EditLink, Width(150)]
    public string DisplayName { get; set; }

    

    [Hidden]
    [Width(80)]
    public string Source { get; set; }

    [Width(200)]
    [ClientUsersFormatter]
    [DisplayName("User wise Clients")]
    public List<int> ClientList { get; set; }

    [Width(200)]
    public string Roles { get; set; }

    [Width(50), SortOrder(9, descending: true), AlignCenter]
    public string UserId { get; set; }
}
