import { EntityDialog, getRemoteData } from "@serenity-is/corelib";

export class EntityDialogBase<TEntity> extends EntityDialog<TEntity, any> {
    
    protected getNextNumberPrefix(docType: string, financialYear: string): string {
       
        var clientCode = getRemoteData('UserData').ClientCode;

        var prefix = ((clientCode) ? (clientCode) : "") + "/";

        prefix += docType + "/";

        try {
            prefix += financialYear;
        }
        catch (Error) {
            alert("There seems to be an error in Invoice Dialog");
        }

        return prefix;
    }
}