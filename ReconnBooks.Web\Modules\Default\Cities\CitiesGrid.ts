import { CitiesColumns, CitiesRow, CitiesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, gridPageInit } from '@serenity-is/corelib';
import { CitiesDialog } from './CitiesDialog';
import { ExcelExportHelper } from "@serenity-is/extensions";
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.CitiesGrid')
export class CitiesGrid extends EntityGridDialog<CitiesRow, any> {
    protected getColumnsKey() { return CitiesColumns.columnsKey; }
    protected getDialogType() { return CitiesDialog; }
    protected getRowDefinition() { return CitiesRow; }
    protected getService() { return CitiesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }

    protected getButtons() {
        let buttons = super.getButtons();
        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: CitiesService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        return buttons;
    }

}