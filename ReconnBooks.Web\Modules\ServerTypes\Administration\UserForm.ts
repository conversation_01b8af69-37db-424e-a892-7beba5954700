﻿import { LookupEditor, ServiceLookupEditor, StringEditor, PasswordEditor, EmailAddressEditor, ImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { ClientsDialog } from "../../Default/Clients/ClientsDialog";
import { ConsultantsDialog } from "../../Default/Consultants/ConsultantsDialog";
import { EmployeesDialog } from "../../Default/Employees/EmployeesDialog";
import { UserTypesDialog } from "../../Default/UserTypes/UserTypesDialog";

export interface UserForm {
    UserTypeId: LookupEditor;
    ClientId: ServiceLookupEditor;
    ConsultantId: ServiceLookupEditor;
    EmployeeId: LookupEditor;
    Username: StringEditor;
    DisplayName: StringEditor;
    Password: PasswordEditor;
    PasswordConfirm: PasswordEditor;
    ClientList: LookupEditor;
    Roles: LookupEditor;
    MobilePhoneNumber: StringEditor;
    Email: EmailAddressEditor;
    UserImage: ImageUploadEditor;
    Source: StringEditor;
}

export class UserForm extends PrefixedContext {
    static readonly formKey = 'Administration.User';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!UserForm.init)  {
            UserForm.init = true;

            var w0 = LookupEditor;
            var w1 = ServiceLookupEditor;
            var w2 = StringEditor;
            var w3 = PasswordEditor;
            var w4 = EmailAddressEditor;
            var w5 = ImageUploadEditor;

            initFormType(UserForm, [
                'UserTypeId', w0,
                'ClientId', w1,
                'ConsultantId', w1,
                'EmployeeId', w0,
                'Username', w2,
                'DisplayName', w2,
                'Password', w3,
                'PasswordConfirm', w3,
                'ClientList', w0,
                'Roles', w0,
                'MobilePhoneNumber', w2,
                'Email', w4,
                'UserImage', w5,
                'Source', w2
            ]);
        }
    }
}

queueMicrotask(() => [UserTypesDialog, ClientsDialog, ConsultantsDialog, EmployeesDialog]); // referenced dialogs