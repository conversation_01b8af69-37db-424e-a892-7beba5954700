﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.HsnSummaryRow>;
using MyRow = ReconnBooks.Default.HsnSummaryRow;

namespace ReconnBooks.Default;

public interface IHsnSummaryListHandler : IListHandler<MyRow, MyRequest, MyResponse> { }

public class HsnSummaryListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IHsnSummaryListHandler
{
    public HsnSummaryListHandler(IRequestContext context)
            : base(context)
    {
    }
}