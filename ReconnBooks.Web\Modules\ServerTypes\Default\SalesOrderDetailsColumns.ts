﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { SalesOrderDetailsRow } from "./SalesOrderDetailsRow";

export interface SalesOrderDetailsColumns {
    RowNumber: Column<SalesOrderDetailsRow>;
    CommodityName: Column<SalesOrderDetailsRow>;
    CommodityCode: Column<SalesOrderDetailsRow>;
    CommodityType: Column<SalesOrderDetailsRow>;
    OrderQuantity: Column<SalesOrderDetailsRow>;
    OrderUnitUnitName: Column<SalesOrderDetailsRow>;
    OrderUnitPrice: Column<SalesOrderDetailsRow>;
    OrderUnitAmount: Column<SalesOrderDetailsRow>;
    OfferQuantity: Column<SalesOrderDetailsRow>;
    OfferUnitUnitName: Column<SalesOrderDetailsRow>;
    OfferPrice: Column<SalesOrderDetailsRow>;
    DiscountPercent: Column<SalesOrderDetailsRow>;
    DiscountAmountPerUnit: Column<SalesOrderDetailsRow>;
    NetDiscountAmount: Column<SalesOrderDetailsRow>;
    TaxableAmountPerUnit: Column<SalesOrderDetailsRow>;
    NetTaxableAmount: Column<SalesOrderDetailsRow>;
    GSTRateRemarks: Column<SalesOrderDetailsRow>;
    IGSTRate: Column<SalesOrderDetailsRow>;
    IGSTAmountPerUnit: Column<SalesOrderDetailsRow>;
    NetIGSTAmount: Column<SalesOrderDetailsRow>;
    CGSTRate: Column<SalesOrderDetailsRow>;
    CGSTAmountPerUnit: Column<SalesOrderDetailsRow>;
    NetCGSTAmount: Column<SalesOrderDetailsRow>;
    SGSTRate: Column<SalesOrderDetailsRow>;
    SGSTAmountPerUnit: Column<SalesOrderDetailsRow>;
    NetSGSTAmount: Column<SalesOrderDetailsRow>;
    NetAmount: Column<SalesOrderDetailsRow>;
    PricePerUnit: Column<SalesOrderDetailsRow>;
    CommodityDescription: Column<SalesOrderDetailsRow>;
    SalesOrderNo: Column<SalesOrderDetailsRow>;
    SalesOrderDetailId: Column<SalesOrderDetailsRow>;
}

export class SalesOrderDetailsColumns extends ColumnsBase<SalesOrderDetailsRow> {
    static readonly columnsKey = 'Default.SalesOrderDetails';
    static readonly Fields = fieldsProxy<SalesOrderDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types