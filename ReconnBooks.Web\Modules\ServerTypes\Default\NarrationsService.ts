﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { NarrationsRow } from "./NarrationsRow";

export namespace NarrationsService {
    export const baseUrl = 'Default/Narrations';

    export declare function Create(request: SaveRequest<NarrationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<NarrationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<NarrationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<NarrationsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<NarrationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<NarrationsRow>>;

    export const Methods = {
        Create: "Default/Narrations/Create",
        Update: "Default/Narrations/Update",
        Delete: "Default/Narrations/Delete",
        Retrieve: "Default/Narrations/Retrieve",
        List: "Default/Narrations/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>NarrationsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}