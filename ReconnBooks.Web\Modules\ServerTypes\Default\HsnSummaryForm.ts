﻿import { PrefixedContext, initFormType } from "@serenity-is/corelib";
import { HsnSummaryDetailsGridEditor } from "../../Default/HsnSummary/HsnSummaryDetailsGridEditor";

export interface HsnSummaryForm {
    Invoices: HsnSummaryDetailsGridEditor;
}

export class HsnSummaryForm extends PrefixedContext {
    static readonly formKey = 'Default.HSNSummary';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!HsnSummaryForm.init)  {
            HsnSummaryForm.init = true;

            var w0 = HsnSummaryDetailsGridEditor;

            initFormType(HsnSummaryForm, [
                'Invoices', w0
            ]);
        }
    }
}