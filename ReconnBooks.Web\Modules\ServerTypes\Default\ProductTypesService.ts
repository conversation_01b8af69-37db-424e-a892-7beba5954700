﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ProductTypesRow } from "./ProductTypesRow";

export namespace ProductTypesService {
    export const baseUrl = 'Default/ProductTypes';

    export declare function Create(request: SaveRequest<ProductTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProductTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProductTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProductTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProductTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProductTypesRow>>;

    export const Methods = {
        Create: "Default/ProductTypes/Create",
        Update: "Default/ProductTypes/Update",
        Delete: "Default/ProductTypes/Delete",
        Retrieve: "Default/ProductTypes/Retrieve",
        List: "Default/ProductTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ProductTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}