﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface UnitsRow {
    RowNumber?: number;
    UnitId?: number;
    UnitName?: string;
    UnitDescription?: string;
    UqcId?: number;
    UQCQuantityName?: string;
    SetDefault?: boolean;
}

export abstract class UnitsRow {
    static readonly idProperty = 'UnitId';
    static readonly nameProperty = 'UnitName';
    static readonly localTextPrefix = 'Default.Units';
    static readonly lookupKey = 'Default.Units';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<UnitsRow>('Default.Units') }
    static async getLookupAsync() { return getLookupAsync<UnitsRow>('Default.Units') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<UnitsRow>();
}