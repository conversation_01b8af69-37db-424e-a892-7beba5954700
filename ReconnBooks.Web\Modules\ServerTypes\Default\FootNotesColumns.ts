﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { FootNotesRow } from "./FootNotesRow";

export interface FootNotesColumns {
    RowNumber: Column<FootNotesRow>;
    FootNoteId: Column<FootNotesRow>;
    FootNote: Column<FootNotesRow>;
    Remarks: Column<FootNotesRow>;
    ClientName: Column<FootNotesRow>;
}

export class FootNotesColumns extends ColumnsBase<FootNotesRow> {
    static readonly columnsKey = 'Default.FootNotes';
    static readonly Fields = fieldsProxy<FootNotesColumns>();
}