﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.BusinessCategoriesRow>;
using MyRow = ReconnBooks.Default.BusinessCategoriesRow;

namespace ReconnBooks.Default;

public interface IBusinessCategoriesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessCategoriesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessCategoriesListHandler
{
    public BusinessCategoriesListHandler(IRequestContext context)
            : base(context)
    {
    }
}