using ReconnBooks.Modules.Default.Clients;

namespace ReconnBooks.Administration;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Administration"), <PERSON><PERSON><PERSON>("Roles")]
[Display<PERSON><PERSON>("Roles"), InstanceName("Role"), GenerateFields]
[ReadPermission(PermissionKeys.Security)]
[ModifyPermission(PermissionKeys.Security)]
[LookupScript]
public sealed partial class RoleRow : Row<RoleRow.RowFields>, IIdRow, INameRow, IMultiClientRow
{
    [DisplayName("Role Id"), Identity, ForeignKey("Roles", "RoleId"), <PERSON>Join("jRole"), IdProperty]
    public int? RoleId { get => fields.RoleId[this]; set => fields.RoleId[this] = value; }

    [DisplayName("Role Name"), Size(100), NotNull, QuickSearch, NameProperty]
    public string RoleName { get => fields.RoleName[this]; set => fields.RoleName[this] = value; }

    [DisplayName("Role Key"), <PERSON><PERSON>(100), QuickSearch]
    public string RoleKey { get => fields.RoleKey[this]; set => fields.RoleKey[this] = value; }

    [DisplayName("Role Key or Name"), Expression("COALESCE(t0.[RoleKey], t0.[RoleName])")]
    public string RoleKeyOrName { get => fields.RoleKeyOrName[this]; set => fields.RoleKeyOrName[this] = value; }

    [Insertable(false), Updatable(false)]
    public int? ClientId
    {
        get => Fields.ClientId[this];
        set => Fields.ClientId[this] = value;
    }

    public Int32Field ClientIdField => fields.ClientId;
}