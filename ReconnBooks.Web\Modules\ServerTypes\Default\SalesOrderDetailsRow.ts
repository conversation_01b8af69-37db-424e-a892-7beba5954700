﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface SalesOrderDetailsRow {
    RowNumber?: number;
    SalesOrderDetailId?: number;
    SalesOrderId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    OfferQuantity?: number;
    OfferUnitId?: number;
    OfferPrice?: number;
    OrderQuantity?: number;
    OrderUnitId?: number;
    OrderUnitPrice?: number;
    OrderUnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: number;
    PricePerUnit?: number;
    NetAmount?: number;
    SalesOrderNo?: string;
    OfferUnitUnitName?: string;
    OrderUnitUnitName?: string;
    GSTRateRemarks?: string;
}

export abstract class SalesOrderDetailsRow {
    static readonly idProperty = 'SalesOrderDetailId';
    static readonly nameProperty = 'SalesOrderDetailId';
    static readonly localTextPrefix = 'Default.SalesOrderDetails';
    static readonly lookupKey = 'Default.SalesOrderDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<SalesOrderDetailsRow>('Default.SalesOrderDetails') }
    static async getLookupAsync() { return getLookupAsync<SalesOrderDetailsRow>('Default.SalesOrderDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SalesOrderDetailsRow>();
}