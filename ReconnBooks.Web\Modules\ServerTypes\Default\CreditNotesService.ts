﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { CreditNoteDetailsRow } from "./CreditNoteDetailsRow";
import { CreditNotesRow } from "./CreditNotesRow";

export namespace CreditNotesService {
    export const baseUrl = 'Default/CreditNotes';

    export declare function Create(request: SaveRequest<CreditNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CreditNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CreditNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CreditNotesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CreditNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CreditNotesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromInvoiceDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<CreditNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CreditNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/CreditNotes/Create",
        Update: "Default/CreditNotes/Update",
        Delete: "Default/CreditNotes/Delete",
        Retrieve: "Default/CreditNotes/Retrieve",
        List: "Default/CreditNotes/List",
        GetNextNumber: "Default/CreditNotes/GetNextNumber",
        GetFromInvoiceDetails: "Default/CreditNotes/GetFromInvoiceDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromInvoiceDetails'
    ].forEach(x => {
        (<any>CreditNotesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}