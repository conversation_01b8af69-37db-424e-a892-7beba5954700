import { BusinessCategoriesForm, BusinessCategoriesRow, BusinessCategoriesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.BusinessCategoriesDialog')
export class BusinessCategoriesDialog extends PendingChangesConfirmDialog<BusinessCategoriesRow> {
    protected getFormKey() { return BusinessCategoriesForm.formKey; }
    protected getRowDefinition() { return BusinessCategoriesRow; }
    protected getService() { return BusinessCategoriesService.baseUrl; }

    protected form = new BusinessCategoriesForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}