﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CustomerContactsRow } from "./CustomerContactsRow";

export namespace CustomerContactsService {
    export const baseUrl = 'Default/CustomerContacts';

    export declare function Create(request: SaveRequest<CustomerContactsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CustomerContactsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CustomerContactsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CustomerContactsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CustomerContactsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CustomerContactsRow>>;

    export const Methods = {
        Create: "Default/CustomerContacts/Create",
        Update: "Default/CustomerContacts/Update",
        Delete: "Default/CustomerContacts/Delete",
        Retrieve: "Default/CustomerContacts/Retrieve",
        List: "Default/CustomerContacts/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CustomerContactsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}