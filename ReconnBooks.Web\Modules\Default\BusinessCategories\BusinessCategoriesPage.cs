﻿using Microsoft.AspNetCore.Mvc;
using Serenity.Web;

namespace ReconnBooks.Default.Pages;

[PageAuthorize(typeof(BusinessCategoriesRow))]
public class BusinessCategoriesPage : Controller
{
    [Route("Default/BusinessCategories")]
    public ActionResult Index()
    {
        return this.GridPage("@/Default/BusinessCategories/BusinessCategoriesPage",
            BusinessCategoriesRow.Fields.PageTitle());
    }
}