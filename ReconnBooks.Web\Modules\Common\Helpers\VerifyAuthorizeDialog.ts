import { Authorization, Decorators, SaveResponse, WidgetProps, confirmDialog, getRemoteData } from "@serenity-is/corelib";
import { PendingChangesConfirmDialog } from "./PendingChangesConfirmDialog";

@Decorators.registerClass()
export class VerifyAuthorizeDialog<TEntity> extends PendingChangesConfirmDialog<TEntity> {

    protected form: any;
    constructor(props: WidgetProps<any>) {
        super(props);
        (this.element).findFirst('.readonly').attr("tabindex", -1);
    }

    protected save(callback: (response: SaveResponse) => void) {
        if (!this.isNew()) {
            if (this.getSaveState() != this.loadedState)
                this.setModifiedBy();
        }
        else {
            this.setPreparedBy();
        }

        if (Authorization.hasPermission("Administration:VerifiedBy")) {
            confirmDialog("Do you want to verify this record?",
                () => {
                    if (Authorization.hasPermission("Administration:AuthorisedBy")) {
                        confirmDialog("Do you also want to authorize this record?",
                            () => {
                                this.setAuthorizedBy();
                                super.save(callback);
                            },
                            {
                                onNo: () => {
                                    this.setVerifiedBy();
                                    super.save(callback);
                                }
                            }
                        );
                    }
                    else {
                        this.setVerifiedBy();
                        super.save(callback);
                    }
                },
                {
                    onNo: () => {
                        super.save(callback);
                    }
                });
        }
        else {
            super.save(callback);
        }
    }

    protected setPreparedBy() {
        this.form.PreparedByUserId.value = getRemoteData('UserData').UserId;
        this.form.PreparedDate.value = new Date().toLocaleString();
    }

    protected setModifiedBy() {
        this.form.ModifiedByUserId.value = getRemoteData('UserData').UserId;
        this.form.ModifiedDate.value = new Date().toLocaleString();
    }

    protected setVerifiedBy() {
        this.form.VerifiedByUserId.value = getRemoteData('UserData').UserId;
        this.form.VerifiedDate.value = new Date().toLocaleString();
    }

    protected setAuthorizedBy() {
        if (this.form.VerifiedByUserId.value == null || this.getSaveState() != this.loadedState)
            this.setVerifiedBy();
        this.form.AuthorizedByUserId.value = getRemoteData('UserData').UserId;
        this.form.AuthorizedDate.value = new Date().toLocaleString();
        this.form.AuthorizedStatus.value = 1;
    }

    protected saveAndClose() {
        super.save(response => {

            this.dialogClose();
            
        });
    }
}
