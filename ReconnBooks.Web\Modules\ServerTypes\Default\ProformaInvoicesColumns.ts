﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { ProformaInvoicesRow } from "./ProformaInvoicesRow";

export interface ProformaInvoicesColumns {
    RowNumber: Column<ProformaInvoicesRow>;
    ProformaInvoiceNo: Column<ProformaInvoicesRow>;
    ProformaInvoiceDate: Column<ProformaInvoicesRow>;
    CustomerCompanyName: Column<ProformaInvoicesRow>;
    SupplyType: Column<ProformaInvoicesRow>;
    NetTaxableAmount: Column<ProformaInvoicesRow>;
    NetCGSTAmount: Column<ProformaInvoicesRow>;
    NetSGSTAmount: Column<ProformaInvoicesRow>;
    NetIGSTAmount: Column<ProformaInvoicesRow>;
    SalesOrderNo: Column<ProformaInvoicesRow>;
    OrderRefNo: Column<ProformaInvoicesRow>;
    OrderRefDate: Column<ProformaInvoicesRow>;
    ProformaInvoiceAmt: Column<ProformaInvoicesRow>;
    RoundingOff: Column<ProformaInvoicesRow>;
    GrandTotal: Column<ProformaInvoicesRow>;
    DeliveryNoteNo: Column<ProformaInvoicesRow>;
    PaymentTerms: Column<ProformaInvoicesRow>;
    PaymentDueDate: Column<ProformaInvoicesRow>;
    ShippedVia: Column<ProformaInvoicesRow>;
    DocketNo: Column<ProformaInvoicesRow>;
    VehicleNo: Column<ProformaInvoicesRow>;
    FinancialYearName: Column<ProformaInvoicesRow>;
    ProformaInvoiceMonth: Column<ProformaInvoicesRow>;
    ProformaInvoiceId: Column<ProformaInvoicesRow>;
    Inspection: Column<ProformaInvoicesRow>;
    UploadFiles: Column<ProformaInvoicesRow>;
    Remarks: Column<ProformaInvoicesRow>;
    ClientId: Column<ProformaInvoicesRow>;
    PreparedByUserUsername: Column<ProformaInvoicesRow>;
    PreparedDate: Column<ProformaInvoicesRow>;
    VerifiedByUserUsername: Column<ProformaInvoicesRow>;
    VerifiedDate: Column<ProformaInvoicesRow>;
    AuthorizedByUserUsername: Column<ProformaInvoicesRow>;
    AuthorizedDate: Column<ProformaInvoicesRow>;
    ModifiedByUserUsername: Column<ProformaInvoicesRow>;
    ModifiedDate: Column<ProformaInvoicesRow>;
    CancelledByUserUsername: Column<ProformaInvoicesRow>;
    CancelledDate: Column<ProformaInvoicesRow>;
    AuthorizedStatus: Column<ProformaInvoicesRow>;
}

export class ProformaInvoicesColumns extends ColumnsBase<ProformaInvoicesRow> {
    static readonly columnsKey = 'Default.ProformaInvoices';
    static readonly Fields = fieldsProxy<ProformaInvoicesColumns>();
}

[IndianNumberFormatter]; // referenced types