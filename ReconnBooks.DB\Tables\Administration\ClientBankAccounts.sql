﻿CREATE TABLE [dbo].[ClientBankAccounts] 
(
    [ClientBankAccountId]   INT             NOT NULL    IDENTITY (1, 1),
    [ClientId]              INT             NOT NULL,
    [BankId]                INT             NOT NULL,
    [BranchName]            NVARCHAR (150)      NULL,
    [AccountName]           NVARCHAR (50)       NULL,
    [AccountNumber]         NVARCHAR (50)       NULL,
    [IFSCCode]              NVARCHAR (50)       NULL,
    [BranchCode]            NVARCHAR (50)       NULL,
    [SwiftCode]             NVARCHAR (50)       NULL,
    [QRCode]                NVARCHAR (500)      NULL,
    [Status]                BIT             NOT NULL    DEFAULT ((0)),

    CONSTRAINT [PK_ClientBankAccounts] PRIMARY KEY CLUSTERED    ([ClientBankAccountId] ASC),
    CONSTRAINT [FK_ClientBankAccounts_Clients]  FOREIGN KEY     ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_ClientBankAccounts_Banks]    FOREIGN KEY     ([BankId])   REFERENCES [dbo].[Banks] ([BankId])
);

GO
CREATE NONCLUSTERED INDEX [BankId]
    ON [dbo].[ClientBankAccounts]([BankId] ASC);

GO
CREATE NONCLUSTERED INDEX [ClientId]
    ON [dbo].[ClientBankAccounts]([ClientId] ASC);

