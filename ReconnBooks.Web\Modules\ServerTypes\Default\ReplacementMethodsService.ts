﻿import { SaveRequest, SaveR<PERSON>ponse, ServiceOptions, DeleteRequest, DeleteR<PERSON>ponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ReplacementMethodsRow } from "./ReplacementMethodsRow";

export namespace ReplacementMethodsService {
    export const baseUrl = 'Default/ReplacementMethods';

    export declare function Create(request: SaveRequest<ReplacementMethodsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ReplacementMethodsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ReplacementMethodsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReplacementMethodsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ReplacementMethodsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ReplacementMethodsRow>>;

    export const Methods = {
        Create: "Default/ReplacementMethods/Create",
        Update: "Default/ReplacementMethods/Update",
        Delete: "Default/ReplacementMethods/Delete",
        Retrieve: "Default/ReplacementMethods/Retrieve",
        List: "Default/ReplacementMethods/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ReplacementMethodsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}