﻿import { fieldsProxy } from "@serenity-is/corelib";
import { VendorPaymentDetailsRow } from "./VendorPaymentDetailsRow";

export interface VendorPaymentsRow {
    RowNumber?: number;
    VendorPaymentId?: number;
    PaymentVoucherNo?: string;
    PaymentVoucherDate?: string;
    PaymentVoucherMonth?: string;
    VendorId?: number;
    TotalPayable?: number;
    OnAccount?: number;
    TDSRateId?: number;
    TDSAmount?: number;
    TCSRateId?: number;
    TCSAmount?: number;
    NetPayable?: number;
    AmountPaid?: number;
    VendorPaymentDetailsList?: VendorPaymentDetailsRow[];
    Narration?: string;
    FinancialYearId?: number;
    ModeOfPaymentId?: number;
    ChequeDdNo?: string;
    ChequeDdDate?: string;
    BankBranchName?: string;
    PaymentRefNo?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    VendorName?: string;
    TDSRateTransaction?: string;
    TCSRateNatureOfTransaction?: string;
    FinancialYearName?: string;
    ModeOfPayment?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class VendorPaymentsRow {
    static readonly idProperty = 'VendorPaymentId';
    static readonly nameProperty = 'PaymentVoucherNo';
    static readonly localTextPrefix = 'Default.VendorPayments';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorPaymentsRow>();
}