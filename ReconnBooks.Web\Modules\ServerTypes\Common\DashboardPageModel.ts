﻿import { DataPoint } from "./DataPoint";

export interface DashboardPageModel {
    OpenOrders?: number;
    ClosedOrderPercent?: number;
    ProductCount?: number;
    SalesCount?: string;
    VendorBillsCount?: string;
    VendorBillsTotal?: string;
    CustomerCount?: string;
    SuppliersCount?: string;
    PendingInvoicesCount?: string;
    PendingVendorBillsCount?: string;
    InvoicesTotal?: string;
    ReceiptsTotal?: string;
    InvoicesDataPoints?: DataPoint[];
    ReceiptsDataPoints?: DataPoint[];
}