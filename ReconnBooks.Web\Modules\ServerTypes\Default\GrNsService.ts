﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { GrnDetailsRow } from "./GrnDetailsRow";
import { GrNsRow } from "./GrNsRow";

export namespace GrNsService {
    export const baseUrl = 'Default/GrNs';

    export declare function Create(request: SaveRequest<GrNsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GrNsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GrNsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GrNsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GrNsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GrNsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromPurchaseOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<GrnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GrnDetailsRow>>;
    export declare function EmailGRN(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/GrNs/Create",
        Update: "Default/GrNs/Update",
        Delete: "Default/GrNs/Delete",
        Retrieve: "Default/GrNs/Retrieve",
        List: "Default/GrNs/List",
        GetNextNumber: "Default/GrNs/GetNextNumber",
        GetFromPurchaseOrderDetails: "Default/GrNs/GetFromPurchaseOrderDetails",
        EmailGRN: "Default/GrNs/EmailGRN"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromPurchaseOrderDetails', 
        'EmailGRN'
    ].forEach(x => {
        (<any>GrNsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}