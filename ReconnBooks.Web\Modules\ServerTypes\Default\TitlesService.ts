﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { TitlesRow } from "./TitlesRow";

export namespace TitlesService {
    export const baseUrl = 'Default/Titles';

    export declare function Create(request: SaveRequest<TitlesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<TitlesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<TitlesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<TitlesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<TitlesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<TitlesRow>>;

    export const Methods = {
        Create: "Default/Titles/Create",
        Update: "Default/Titles/Update",
        Delete: "Default/Titles/Delete",
        Retrieve: "Default/Titles/Retrieve",
        List: "Default/Titles/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>TitlesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}