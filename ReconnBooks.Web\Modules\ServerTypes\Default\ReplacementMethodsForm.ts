﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ReplacementMethodsForm {
    ReplacementMethod: StringEditor;
    Description: StringEditor;
}

export class ReplacementMethodsForm extends PrefixedContext {
    static readonly formKey = 'Default.ReplacementMethods';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ReplacementMethodsForm.init)  {
            ReplacementMethodsForm.init = true;

            var w0 = StringEditor;

            initFormType(ReplacementMethodsForm, [
                'ReplacementMethod', w0,
                'Description', w0
            ]);
        }
    }
}