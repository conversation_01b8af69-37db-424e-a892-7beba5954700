﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.HsnSummaryRow>;
using MyRow = ReconnBooks.Default.HsnSummaryRow;

namespace ReconnBooks.Default;

public interface IHsnSummaryRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> { }

public class HsnSummaryRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IHsnSummaryRetrieveHandler
{
    public HsnSummaryRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}