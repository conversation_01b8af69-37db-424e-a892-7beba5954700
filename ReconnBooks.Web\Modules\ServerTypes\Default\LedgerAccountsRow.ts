﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface LedgerAccountsRow {
    RowNumber?: number;
    LedgerAccountId?: number;
    LedgerAccountName?: number;
    SecondaryGroupId?: number;
    LedgerCreationDate?: string;
    SecondaryGroupName?: string;
}

export abstract class LedgerAccountsRow {
    static readonly idProperty = 'LedgerAccountId';
    static readonly localTextPrefix = 'Default.LedgerAccounts';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<LedgerAccountsRow>();
}