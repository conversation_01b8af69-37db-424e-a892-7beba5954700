﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ClientUsersRow } from "./ClientUsersRow";

export interface ClientUsersColumns {
    ClientUserId: Column<ClientUsersRow>;
    Username: Column<ClientUsersRow>;
    ConsultantName: Column<ClientUsersRow>;
    ClientName: Column<ClientUsersRow>;
    Status: Column<ClientUsersRow>;
}

export class ClientUsersColumns extends ColumnsBase<ClientUsersRow> {
    static readonly columnsKey = 'Default.ClientUsers';
    static readonly Fields = fieldsProxy<ClientUsersColumns>();
}