﻿import { StringEditor, ServiceLookupEditor, MultipleImageUploadEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ConsultantBankAccountsForm {
    AccountName: StringEditor;
    BankId: ServiceLookupEditor;
    BranchName: StringEditor;
    AccountNumber: StringEditor;
    IFSCCode: StringEditor;
    BranchCode: StringEditor;
    SwiftCode: StringEditor;
    QRCode: MultipleImageUploadEditor;
    Status: BooleanEditor;
}

export class ConsultantBankAccountsForm extends PrefixedContext {
    static readonly formKey = 'Default.ConsultantBankAccounts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ConsultantBankAccountsForm.init)  {
            ConsultantBankAccountsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = MultipleImageUploadEditor;
            var w3 = BooleanEditor;

            initFormType(ConsultantBankAccountsForm, [
                'AccountName', w0,
                'BankId', w1,
                'BranchName', w0,
                'AccountNumber', w0,
                'IFSCCode', w0,
                'BranchCode', w0,
                'SwiftCode', w0,
                'QRCode', w2,
                'Status', w3
            ]);
        }
    }
}