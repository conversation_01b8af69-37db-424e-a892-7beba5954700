using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.Commodities")]
[BasedOnRow(typeof(CommoditiesRow), CheckNames = true)]
public class CommoditiesColumns
{
    [DisplayName("Sl.No."), Width(60), AlignCenter]
    public long RowNumber { get; set; }

    [Width(100)]
    [DisplayName("Commodity Type")]
    public string CommodityType { get; set; }

    [EditLink, Width(350)]
    [DisplayName("Product/Service Name")]
    public string CommodityName { get; set; }

    [Width(150)]
    [DisplayName("Product/Service Code")]
    public string CommodityCode { get; set; }

    [DisplayName("Unit"), Width(100)]
    public string UnitName { get; set; }

    [EditLink, Width(250)]
    [DisplayName("Product/Service Description")]
    public string CommodityDescription { get; set; }

    [DisplayName("HSN/SAC Code"), Width(100)]
    public string HSNSACCode { get; set; }

    [DisplayName("GST Rate"), Width(100)]
    public string GstRateRemarks { get; set; }

    [Hidden, DisplayName("Purchase Price"), Width(100)]
    public decimal PurchasePrice { get; set; }

    [Hidden, DisplayName("Sales Price"), Width(100)]
    public decimal SalesPrice { get; set; }

    [Hidden]
    public string ProductCategoryCategoryName { get; set; }
    [Hidden]
    public string ProductGroup { get; set; }
    [Hidden]
    public string ProductType { get; set; }
    [Hidden]
    public string ProductMake { get; set; }
    [Hidden]
    public string ProductWeight { get; set; }
    [Hidden]
    public string ProductImage { get; set; }
    [Hidden]
    public bool CommodityStatus { get; set; }

    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public long CommodityId { get; set; }
    [Hidden]
    public string Remarks { get; set; }
    public int ClientId { get; set; }
}