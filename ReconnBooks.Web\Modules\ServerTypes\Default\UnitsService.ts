﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { UnitsRow } from "./UnitsRow";

export namespace UnitsService {
    export const baseUrl = 'Default/Units';

    export declare function Create(request: SaveRequest<UnitsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<UnitsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<UnitsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<UnitsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<UnitsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<UnitsRow>>;

    export const Methods = {
        Create: "Default/Units/Create",
        Update: "Default/Units/Update",
        Delete: "Default/Units/Delete",
        Retrieve: "Default/Units/Retrieve",
        List: "Default/Units/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>UnitsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}