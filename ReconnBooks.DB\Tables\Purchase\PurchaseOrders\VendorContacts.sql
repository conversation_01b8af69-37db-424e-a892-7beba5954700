﻿CREATE TABLE [dbo].[VendorContacts] 
(
    [VendorContactId]   INT             NOT NULL    IDENTITY (1,1),
    [VendorId]          INT             NOT NULL,
    [TitleId]           INT                 NULL,
    [ContactName]       NVARCHAR (300)      NULL,
    [DesignationId]     INT                 NULL,
    [DepartmentId]      INT                 NULL,
    [OfficePhoneNo]     NVARCHAR (50)       NULL,
    [ExtensionNo]       NVARCHAR (50)       NULL,
    [MobileNo]          NVARCHAR (18)       NULL,
    [AlternateNo]       NVARCHAR (18)       NULL,
    [Email]             NVARCHAR (100)      NULL,
    [Status]            BIT             NOT NULL    DEFAULT ((1)),

    CONSTRAINT [PK_VendorContacts] PRIMARY KEY    CLUSTERED	([VendorContactId] ASC),
    CONSTRAINT [FK_VendorContacts_Customers]      FOREIGN KEY ([VendorId])      REFERENCES [dbo].[Vendors]      ([VendorId]),
    CONSTRAINT [FK_VendorContacts_Titles]         FOREIGN KEY ([TitleId])       REFERENCES [dbo].[Titles]       ([TitleId]),
    CONSTRAINT [FK_VendorContacts_Designations]   FOREIGN KEY ([DesignationId]) REFERENCES [dbo].[Designations] ([DesignationId]),
    CONSTRAINT [FK_VendorContacts_Departments]    FOREIGN KEY ([DepartmentId])  REFERENCES [dbo].[Departments]  ([DepartmentId])
);
GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[VendorContacts]([VendorId] ASC);