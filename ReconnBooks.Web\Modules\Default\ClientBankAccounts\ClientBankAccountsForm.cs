using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.ClientBankAccounts")]
[BasedOnRow(typeof(ClientBankAccountsRow), CheckNames = true)]
public class ClientBankAccountsForm
{
    //public int ClientId { get; set; }

    [FullWidth(UntilNext =true)]
    [DisplayName("Account Name")]
    public string AccountName { get; set; }

    [DisplayName("Bank Name")]
    public int BankId { get; set; }

    [DisplayName("Branch Name")]
    public string BranchName { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("Account No.")]
    public string AccountNumber { get; set; }

    [DisplayName("IFSC Code")]
    public string IFSCCode { get; set; }
    
    [DisplayName("Branch Code")]
    public string BranchCode { get; set; }

    [DisplayName("SWIFT Code")]
    public string SwiftCode { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Upload QR Code")]
    
    [ImageUploadEditor]
    public string QRCode { get; set; }

    [DisplayName("Status")]
    public bool Status { get; set; }
}