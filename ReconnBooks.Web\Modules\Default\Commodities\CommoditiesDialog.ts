import { CommoditiesExcelImportForm, CommoditiesForm, CommoditiesRow, CommoditiesService, CommodityTypesRow, HsnsacCodesRow, UnitsRow } from '@/ServerTypes/Default';
import { Decorators, reloadLookup, toId, notifyError, notifyInfo, cancelDialogButton, PropertyDialog, ToolButton } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.CommoditiesDialog')
@Decorators.responsive()
@Decorators.panel()

export class CommoditiesDialog extends PendingChangesConfirmDialog<CommoditiesRow> {
    protected getFormKey() { return CommoditiesForm.formKey; }
    protected getRowDefinition() { return CommoditiesRow; }
    protected getService() { return CommoditiesService.baseUrl; }

    protected form = new CommoditiesForm(this.idPrefix);

    constructor() {
        super();
        this.form.UnitId.changeSelect2(async a => {
            var unitId = toId(this.form.UnitId.value);
            if (unitId != null) {
                var unitRow = (await UnitsRow.getLookupAsync()).itemById[unitId];
                this.form.UQCQuantityName.value = unitRow.UQCQuantityName;

            }
        })
        this.form.HSNSACCodeId.changeSelect2(async a => {
            var hsnsaccodeId = toId(this.form.HSNSACCodeId.value);
            if (hsnsaccodeId != null) {
                var hsnsaccodeRow = (await HsnsacCodesRow.getLookupAsync()).itemById[hsnsaccodeId];
                this.form.HSNSACDescription.value = hsnsaccodeRow.HSNSACDescription;
                this.form.HSNSACGroup.value = hsnsaccodeRow.HSNSACGroup;
            }
        })
    }

    private setCommodityLabelNames(commodityType: string) {
        if (commodityType === "Goods") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Product Code';
            this.form.CommodityName.element[0].parentElement.querySelector('label').textContent = 'Product Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Product Description';
            this.form.ProductCategoryId.element[0].parentElement.querySelector('label').textContent = 'Product Category';
            this.form.ProductGroupId.element[0].parentElement.querySelector('label').textContent = 'Product Group';
            this.form.ProductTypeId.element[0].parentElement.querySelector('label').textContent = 'Product Type';
            this.form.ProductMakeId.element[0].parentElement.querySelector('label').textContent = 'Product Make';
            this.form.ProductWeight.element[0].parentElement.querySelector('label').textContent = 'Product Weight';
            this.form.CommodityStatus.element[0].parentElement.querySelector('label').textContent = 'Product / Service Status';
            this.form.Remarks.element[0].parentElement.querySelector('label').textContent = 'Remarks';
            this.form.ProductImage.element[0].parentElement.querySelector('label').textContent = 'Product Image';
            this.form.ProductCategoryId.element[0].parentElement.hidden = false;
            this.form.ProductGroupId.element[0].parentElement.hidden = false;
            this.form.ProductTypeId.element[0].parentElement.hidden = false;
            this.form.ProductMakeId.element[0].parentElement.hidden = false;
            this.form.ProductWeight.element[0].parentElement.hidden = false;
            this.form.CommodityStatus.element[0].parentElement.hidden = false;
            this.form.Remarks.element[0].parentElement.hidden = false;
            this.form.ProductImage.element[0].parentElement.hidden = false;

        } else if (commodityType === "Service") {
            this.form.CommodityCode.element[0].parentElement.querySelector('label').textContent = 'Service Code';
            this.form.CommodityName.element[0].parentElement.querySelector('label').textContent = 'Service Name';
            this.form.CommodityDescription.element[0].parentElement.querySelector('label').textContent = 'Service Description';
            this.form.ProductCategoryId.element[0].parentElement.hidden = true;
            this.form.ProductGroupId.element[0].parentElement.hidden = true;
            this.form.ProductTypeId.element[0].parentElement.hidden = true;
            this.form.ProductMakeId.element[0].parentElement.hidden = true;
            this.form.ProductWeight.element[0].parentElement.hidden = true;
            this.form.CommodityStatus.element[0].parentElement.hidden = true;
            this.form.Remarks.element[0].parentElement.hidden = true;
            this.form.ProductImage.element[0].parentElement.hidden = true;
        }
    }

    protected async afterLoadEntity() {
        await super.afterLoadEntity();

        if (!this.isNew()) {
            var commodityType = ((await CommodityTypesRow.getLookupAsync()).itemById[this.form.CommodityTypeId.value]).CommodityType;
            this.setCommodityLabelNames(commodityType);
        }

        if (this.isNew()) {
            this.form.UnitId.value = (await UnitsRow.getLookupAsync()).items.find(item => item.SetDefault)?.UnitId.toString();

            const defaultUnit = (await UnitsRow.getLookupAsync()).items.find(item => item.SetDefault);
            if (defaultUnit) {
                this.form.UQCQuantityName.value = defaultUnit.UQCQuantityName ?? "";
            }
        }
        this.setDialogsLoadedState();
    }

    protected updateInterface() {

        // by default cloneButton is hidden in base UpdateInterface method
        super.updateInterface();
        // here we show it if it is edit mode (not new)
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    protected onSaveSuccess(response) {
        super.onSaveSuccess(response);

        reloadLookup(CommoditiesRow.lookupKey);
    }

}

@Decorators.registerClass('ReconnBooks.Default.ProductExcelImportDialog')
export class ProductExcelImportDialog extends PropertyDialog<any, any> {

    private form: CommoditiesExcelImportForm;

    protected getFormKey() { return CommoditiesExcelImportForm.formKey; }

    constructor() {
        super();

        this.form = new CommoditiesExcelImportForm(this.idPrefix);
    }

    protected getInitialDialogTitle(): string {
        return "Excel Import";
    }

    protected getDialogButtons() {
        return [
            {
                text: 'Import',
                click: () => {
                    if (!this.validateBeforeSave())
                        return;

                    if (!this.form.FileName.value?.Filename) {
                        notifyError("Please select a file!");
                        return;
                    }

                    CommoditiesService.ExcelImport({
                        FileName: this.form.FileName.value.Filename
                    }, response => {
                        notifyInfo(
                            'Inserted: ' + (response.Inserted || 0) +
                            ', Updated: ' + (response.Updated || 0));

                        if (response.ErrorList != null && response.ErrorList.length > 0) {
                            notifyError(response.ErrorList.join(',\r\n '));
                        }

                        this.dialogClose("done");
                    });
                },
            },
            cancelDialogButton()
        ];
    }
}





