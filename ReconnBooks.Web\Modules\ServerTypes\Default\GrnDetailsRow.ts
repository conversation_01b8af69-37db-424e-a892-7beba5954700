﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface GrnDetailsRow {
    RowNumber?: number;
    GrnDetailId?: number;
    GRNId?: number;
    PurchaseOrderDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    PoQuantity?: number;
    PoUnitId?: number;
    ReceivedQuantity?: number;
    ReceivedUnitId?: number;
    Sku?: string;
    SerialNos?: string;
    AcceptedQuantity?: number;
    AcceptedUnitId?: number;
    SupplyDueDate?: string;
    LocationId?: number;
    WarehouseId?: number;
    StoreId?: number;
    RackId?: number;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    GRNNo?: string;
    PurchaseOrderDetailCommodityDescription?: string;
    PoUnitUnitName?: string;
    ReceivedUnitUnitName?: string;
    AcceptedUnitUnitName?: string;
    LocationName?: string;
    WarehouseName?: string;
    StoreName?: string;
    RackNo?: string;
}

export abstract class GrnDetailsRow {
    static readonly idProperty = 'GrnDetailId';
    static readonly nameProperty = 'GrnDetailId';
    static readonly localTextPrefix = 'Default.GrnDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GrnDetailsRow>();
}