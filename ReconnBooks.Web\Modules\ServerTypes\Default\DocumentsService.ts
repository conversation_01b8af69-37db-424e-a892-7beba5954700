﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { DocumentsRow } from "./DocumentsRow";

export namespace DocumentsService {
    export const baseUrl = 'Default/Documents';

    export declare function Create(request: SaveRequest<DocumentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DocumentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DocumentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DocumentsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DocumentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DocumentsRow>>;

    export const Methods = {
        Create: "Default/Documents/Create",
        Update: "Default/Documents/Update",
        Delete: "Default/Documents/Delete",
        Retrieve: "Default/Documents/Retrieve",
        List: "Default/Documents/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>DocumentsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}