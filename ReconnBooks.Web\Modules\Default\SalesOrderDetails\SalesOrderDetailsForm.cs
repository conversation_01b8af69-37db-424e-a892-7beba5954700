using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.SalesOrderDetails")]
[BasedOnRow(typeof(SalesOrderDetailsRow), CheckNames = true)]
public class SalesOrderDetailsForm
{
    //public int SalesOrderId { get; set; }

    [Category("Commodity Details")]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Commodity Type")]
    public int CommodityTypeId { get; set; }

    [DisplayName("Product Code")]
    public string CommodityCode { get; set; }

    [FullWidth]
    [DisplayName("Product Name")]
    public long CommodityId { get; set; }

    [TextAreaEditor(Rows = 3)]
    [DisplayName("Product Description")]
    public string CommodityDescription { get; set; }

    [Category("HSN/SAC Details"), Collapsible(Collapsed = true)]
    [HalfWidth(UntilNext = true)]
    [ReadOnly(true)]
    [DisplayName("HSN/SAC Code")]
    public string HSNSACCode { get; set; }

    [ReadOnly(true)]
    [Disp<PERSON><PERSON><PERSON>("HSN/SAC Group")]
    public string HSNSACGroup { get; set; }


    [FullWidth(UntilNext = true)]
    [ReadOnly(true)]
    [TextAreaEditor(Rows = 3)]
    [DisplayName("Description")]
    public string HSNSACDescription { get; set; }


    [Category("Order Details")]
    [OneThirdWidth(UntilNext = true)]
    [ReadOnly(true), DisplayName("Offer Quantity"), LabelWidth(120)]
    public decimal OfferQuantity { get; set; }


    [ReadOnly(true), DisplayName("Offer Unit"), LabelWidth(80)]
    public int OfferUnitId { get; set; }

    [ReadOnly(true), DisplayName("Offer Unit Price"), LabelWidth(120)]
    public decimal OfferPrice { get; set; }
          
    [DisplayName("Order Quantity"), LabelWidth(120)]
    public decimal OrderQuantity { get; set; }

    [DisplayName("Order Unit "), LabelWidth(80)]
    public int OrderUnitId { get; set; }

    [DisplayName("Order Unit Price"), LabelWidth(120)]
    public decimal OrderUnitPrice { get; set; }
    //------

    [OneThirdWidth(UntilNext = true)]
    [DisplayName("Discount %"), LabelWidth(120)]
    public decimal DiscountPercent { get; set; }

    [Hidden]
    [DisplayName("Discount/Unit"), LabelWidth(120)]
    public decimal DiscountAmountPerUnit { get; set; }

    [DisplayName("Discount Amt."), LabelWidth(125)]
    public decimal NetDiscountAmount { get; set; }

    [ReadOnly(true), DisplayName("Unit Amount"), LabelWidth(120)]
    public decimal OrderUnitAmount { get; set; }

    [Category(" ")]
    [DisplayName("GST Rate (%)"), LabelWidth(120)]
    public int GSTRateId { get; set; }

    [ReadOnly(true)]
    [DisplayName("Taxable Amt./Unit"), LabelWidth(125)]
    public decimal TaxableAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net Taxable Amt."), LabelWidth(120)]
    public decimal NetTaxableAmount { get; set; }


    [ReadOnly(true)]
    [DisplayName("IGST Rate (%)"), LabelWidth(120)]
    public decimal IGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("IGST Amt./Unit"), LabelWidth(125)]
    public decimal IGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net IGST Amt."), LabelWidth(120)]
    public decimal NetIGSTAmount { get; set; }
    
    [ReadOnly(true)]
    [DisplayName("CGST Rate (%)"), LabelWidth(120)]
    public decimal CGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("CGST Amt./Unit"), LabelWidth(125)]
    public decimal CGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net CGST Amt."), LabelWidth(120)]
    public decimal NetCGSTAmount { get; set; }

    [ReadOnly(true)]
    [DisplayName("SGST Rate (%)"), LabelWidth(120)]
    public decimal SGSTRate { get; set; }

    [ReadOnly(true)]
    [DisplayName("SGST Amt./Unit"), LabelWidth(125)]
    public decimal SGSTAmountPerUnit { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net SGST Amt."), LabelWidth(120)]
    public decimal NetSGSTAmount { get; set; }

    [Category("   ")]
    [ReadOnly(true), LabelWidth(0)]
    public decimal DummyField { get; set; }

    [ReadOnly(true)]
    [DisplayName("Net Price/Unit"), LabelWidth(125)]
    public decimal PricePerUnit { get; set; }
    
    [ReadOnly(true)]
    [DisplayName("Net Amount"), LabelWidth(120)]
    public decimal NetAmount { get; set; }

}