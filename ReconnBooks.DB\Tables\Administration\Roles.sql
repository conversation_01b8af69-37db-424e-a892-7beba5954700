﻿CREATE TABLE [dbo].[Roles] (
    [RoleId]		INT				NOT NULL	IDENTITY (1, 1) ,
    [RoleName]		NVARCHAR (100)	NOT NULL,
    [RoleKey]  NVARCHAR (100) NULL,
    [ClientId]		INT				NOT NULL	CONSTRAINT	[DF_Roles_ClientId]		DEFAULT ((0)),
  --[ConsultantId]	INT				NOT NULL	CONSTRAINT	[DF_Roles_ConsultantId]	DEFAULT ((0)),
    
	CONSTRAINT [PK_Roles] PRIMARY KEY	CLUSTERED	([RoleId] ASC),
    CONSTRAINT [FK_Role_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId])

);