﻿import { IntegerEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface PrimaryGroupsForm {
    PrimaryGroupCode: IntegerEditor;
    PrimaryGroupName: StringEditor;
    Remarks: StringEditor;
}

export class PrimaryGroupsForm extends PrefixedContext {
    static readonly formKey = 'Default.PrimaryGroups';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PrimaryGroupsForm.init)  {
            PrimaryGroupsForm.init = true;

            var w0 = IntegerEditor;
            var w1 = StringEditor;

            initFormType(PrimaryGroupsForm, [
                'PrimaryGroupCode', w0,
                'PrimaryGroupName', w1,
                'Remarks', w1
            ]);
        }
    }
}