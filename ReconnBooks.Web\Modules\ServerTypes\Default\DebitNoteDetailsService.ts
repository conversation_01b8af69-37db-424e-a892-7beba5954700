﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { DebitNoteDetailsRow } from "./DebitNoteDetailsRow";

export namespace DebitNoteDetailsService {
    export const baseUrl = 'Default/DebitNoteDetails';

    export declare function Create(request: SaveRequest<DebitNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DebitNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DebitNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DebitNoteDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DebitNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DebitNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/DebitNoteDetails/Create",
        Update: "Default/DebitNoteDetails/Update",
        Delete: "Default/DebitNoteDetails/Delete",
        Retrieve: "Default/DebitNoteDetails/Retrieve",
        List: "Default/DebitNoteDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>DebitNoteDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}