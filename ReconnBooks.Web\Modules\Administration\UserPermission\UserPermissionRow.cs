namespace ReconnBooks.Administration;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Administration"), <PERSON><PERSON><PERSON>("UserPermissions")]
[DisplayName("UserPermissions"), Instance<PERSON>ame("UserPermissions"), GenerateFields]
[ReadPermission(PermissionKeys.Security)]
[ModifyPermission(PermissionKeys.Security)]
public sealed partial class UserPermissionRow : Row<UserPermissionRow.RowFields>, IUserPermissionRow
{
    [DisplayName("User Permission Id"), Identity, IdProperty]
    public long? UserPermissionId { get => fields.UserPermissionId[this]; set => fields.UserPermissionId[this] = value; }

    [Di<PERSON><PERSON><PERSON><PERSON>("User Id"), NotNull, ForeignKey("Users", "UserId"), LeftJoin("jUser")]
    public int? UserId { get => fields.UserId[this]; set => fields.UserId[this] = value; }

    [Display<PERSON><PERSON>("Permission Key"), <PERSON><PERSON>(100), <PERSON><PERSON><PERSON>, <PERSON><PERSON>earch, NameProperty]
    public string PermissionKey { get => fields.PermissionKey[this]; set => fields.PermissionKey[this] = value; }

    [DisplayName("Grant")]
    public bool? Granted { get => fields.Granted[this]; set => fields.Granted[this] = value; }

    [DisplayName("User Username"), Expression("jUser.[Username]")]
    public string Username { get => fields.Username[this]; set => fields.Username[this] = value; }

    [DisplayName("User Display Name"), Expression("jUser.[DisplayName]")]
    public string User { get => fields.User[this]; set => fields.User[this] = value; }

    public Field UserIdField => fields.UserId;
    public StringField PermissionKeyField => fields.PermissionKey;
    public BooleanField GrantedField => fields.Granted;
}