﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { DeliveryNoteDetailsRow } from "./DeliveryNoteDetailsRow";

export interface DeliveryNoteDetailsColumns {
    RowNumber: Column<DeliveryNoteDetailsRow>;
    DeliveryNoteNo: Column<DeliveryNoteDetailsRow>;
    CommodityName: Column<DeliveryNoteDetailsRow>;
    CommodityCode: Column<DeliveryNoteDetailsRow>;
    CommodityType: Column<DeliveryNoteDetailsRow>;
    Quantity: Column<DeliveryNoteDetailsRow>;
    UnitName: Column<DeliveryNoteDetailsRow>;
    UnitPrice: Column<DeliveryNoteDetailsRow>;
    UnitAmount: Column<DeliveryNoteDetailsRow>;
    GSTRateRemarks: Column<DeliveryNoteDetailsRow>;
    IGSTRate: Column<DeliveryNoteDetailsRow>;
    PerUnitIGSTAmount: Column<DeliveryNoteDetailsRow>;
    IGSTAmount: Column<DeliveryNoteDetailsRow>;
    CGSTRate: Column<DeliveryNoteDetailsRow>;
    PerUnitCGSTAmount: Column<DeliveryNoteDetailsRow>;
    CGSTAmount: Column<DeliveryNoteDetailsRow>;
    SGSTRate: Column<DeliveryNoteDetailsRow>;
    PerUnitSGSTAmount: Column<DeliveryNoteDetailsRow>;
    SGSTAmount: Column<DeliveryNoteDetailsRow>;
    NetAmount: Column<DeliveryNoteDetailsRow>;
    PerUnitPrice: Column<DeliveryNoteDetailsRow>;
    CommodityDescription: Column<DeliveryNoteDetailsRow>;
    Sku: Column<DeliveryNoteDetailsRow>;
    DeliveryNoteDetailId: Column<DeliveryNoteDetailsRow>;
}

export class DeliveryNoteDetailsColumns extends ColumnsBase<DeliveryNoteDetailsRow> {
    static readonly columnsKey = 'Default.DeliveryNoteDetails';
    static readonly Fields = fieldsProxy<DeliveryNoteDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types