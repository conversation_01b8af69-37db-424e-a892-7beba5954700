﻿CREATE TABLE [dbo].[LedgerAccounts] (
    [LedgerAccountId]    INT      IDENTITY (1, 1) NOT NULL,
    [LedgerAccountName]  INT      NOT NULL,
    [SecondaryGroupId]   INT      NOT NULL,
    [LedgerCreationDate] DATETIME NULL,
    CONSTRAINT [PK_LedgerAccounts] PRIMARY KEY CLUSTERED ([LedgerAccountId] ASC),
    CONSTRAINT [FK_LedgerAccounts_SecondaryGroups] FOREIGN KEY ([SecondaryGroupId]) REFERENCES [dbo].[SecondaryGroups] ([SecondaryGroupId])
);


GO
CREATE NONCLUSTERED INDEX [LedgerAccounts]
    ON [dbo].[LedgerAccounts]([LedgerAccountId] ASC);
