﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.CommoditiesRow>;
using MyRow = ReconnBooks.Default.CommoditiesRow;

namespace ReconnBooks.Default;

public interface ICommoditiesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class CommoditiesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, ICommoditiesRetrieveHandler
{
    public CommoditiesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}