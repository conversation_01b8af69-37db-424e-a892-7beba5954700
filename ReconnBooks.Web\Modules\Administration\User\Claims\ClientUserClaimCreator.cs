using Serenity;
using System.Security.Claims;
using System.Security.Principal;
using System.Transactions;

namespace ReconnBooks.Modules.Administration.User.Authentication.Claims;

public class ClientUserClaimCreator : DefaultUserClaimCreator
{
    private readonly IUserRetrieveService userRetrieveService;

    public ClientUserClaimCreator(IUserRetrieveService userRetrieveService) : base(userRetrieveService)
    {
        this.userRetrieveService = userRetrieveService ?? throw new ArgumentNullException(nameof(userRetrieveService));
    }


    public override ClaimsPrincipal CreatePrincipal(string username, string authType)
    {
        if (username is null)
            throw new ArgumentNullException(nameof(username));

        var user = (UserDefinition)userRetrieveService.ByUsername(username) ??
            throw new ArgumentOutOfRangeException(nameof(username));

        if (authType == null)
            throw new ArgumentNullException(nameof(authType));

        var identity = new GenericIdentity(username, authType);
        identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, user.Id));

        string clientIdValue = user.ClientId?.ToString() ?? string.Empty;
        identity.AddClaim(new Claim("ClientId", clientIdValue));

        string consultantIdValue = user.ConsultantId?.ToString() ?? string.Empty;
        identity.AddClaim(new Claim("ConsultantId", consultantIdValue));
        //identity.AddClaim(new Claim("ConsultantId", user.ConsultantId.Value.ToInvariant()));


        return new ClaimsPrincipal(identity);

    }

}
