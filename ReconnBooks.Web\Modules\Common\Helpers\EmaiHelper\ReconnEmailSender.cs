using ReconnBooks.Default;
using MailKit.Net.Smtp;
using Microsoft.AspNetCore.Hosting;
using MimeKit;
using System.Globalization;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using System.IO;
using Serenity.Web;
using ReconnBooks.Administration;
using Microsoft.AspNetCore.DataProtection;

namespace ReconnBooks.Modules.Common.Helpers.EmailHelper;

public class ReconnEmailSender(
    IWebHostEnvironment host,
    ISqlConnections sqlConnections,
    IUserAccessor userAccessor,
    IDataProtectionProvider dataProtectionProvider,
    IEmailQueue emailQueue = null) : IEmailSender
{
    private readonly IWebHostEnvironment host = host ?? throw new ArgumentNullException(nameof(host));
    private readonly IDataProtector _dataProtector = dataProtectionProvider.CreateProtector("EmailServerPassword");

    public void Send(MimeMessage message, bool skipQueue = false)
    {
        ArgumentNullException.ThrowIfNull(message);

        using var connection = sqlConnections.NewFor<ClientsRow>();
        var clientId = userAccessor.User?.GetClientId();

        if (clientId == null)
            throw new ValidationError("Client ID not found for current user!");

        var clientRow = connection.ById<ClientsRow>(clientId.Value);
        if (clientRow == null)
            throw new ValidationError("Client settings not found!");

        if (message.From.Count == 0 && !string.IsNullOrEmpty(clientRow.EMail))
            message.From.Add(MailboxAddress.Parse(clientRow.EMail));

        if (!skipQueue && emailQueue != null)
        {
            emailQueue.Enqueue(message);
        }
        else if (!string.IsNullOrEmpty(clientRow.EmailServerHost))
        {
            using var client = new SmtpClient();
            var port = clientRow.EmailServerPort ?? 587;

            try
            {
                client.Connect(clientRow.EmailServerHost, port, MailKit.Security.SecureSocketOptions.StartTls);

                if (!string.IsNullOrEmpty(clientRow.EmailServerUsername))
                {
                    if (string.IsNullOrEmpty(clientRow.EmailServerPasswordEncrypted))
                        throw new ValidationError("Email server password not configured!");

                    var decryptedPassword = _dataProtector.Unprotect(clientRow.EmailServerPasswordEncrypted);
                    client.Authenticate(clientRow.EmailServerUsername, decryptedPassword);
                }

                client.Send(message);
                client.Disconnect(true);
            }
            catch (Exception ex)
            {
                throw new ValidationError($"Failed to send email: {ex.Message}");
            }
        }
        else
        {
            // Fallback to file system for development/testing
            var pickupPath = Path.Combine(host.ContentRootPath, "App_Data", "Mail");
            if (!Directory.Exists(pickupPath))
                Directory.CreateDirectory(pickupPath);

            var fileName = Path.Combine(pickupPath,
                DateTime.Now.ToString("yyyyMMdd_HHmmss_fff", CultureInfo.InvariantCulture) +
                "_client" + clientId + ".eml");

            message.WriteTo(fileName);
        }
    }
}