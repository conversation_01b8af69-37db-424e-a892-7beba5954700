﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ProductCategoriesRow } from "./ProductCategoriesRow";

export interface ProductCategoriesColumns {
    RowNumber: Column<ProductCategoriesRow>;
    ProductCategoryId: Column<ProductCategoriesRow>;
    CategoryName: Column<ProductCategoriesRow>;
    Description: Column<ProductCategoriesRow>;
    ClientName: Column<ProductCategoriesRow>;
}

export class ProductCategoriesColumns extends ColumnsBase<ProductCategoriesRow> {
    static readonly columnsKey = 'Default.ProductCategories';
    static readonly Fields = fieldsProxy<ProductCategoriesColumns>();
}