﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { VendorPaymentsRow } from "./VendorPaymentsRow";

export namespace VendorPaymentsService {
    export const baseUrl = 'Default/VendorPayments';

    export declare function Create(request: SaveRequest<VendorPaymentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorPaymentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorPaymentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorPaymentsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorPaymentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorPaymentsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;

    export const Methods = {
        Create: "Default/VendorPayments/Create",
        Update: "Default/VendorPayments/Update",
        Delete: "Default/VendorPayments/Delete",
        Retrieve: "Default/VendorPayments/Retrieve",
        List: "Default/VendorPayments/List",
        GetNextNumber: "Default/VendorPayments/GetNextNumber"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber'
    ].forEach(x => {
        (<any>VendorPaymentsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}