﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.BusinessTypesRow>;
using MyRow = ReconnBooks.Default.BusinessTypesRow;

namespace ReconnBooks.Default;

public interface IBusinessTypesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessTypesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessTypesListHandler
{
    public BusinessTypesListHandler(IRequestContext context)
            : base(context)
    {
    }
}