﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface WarehousesRow {
    RowNumber?: number;
    WarehouseId?: number;
    WarehouseName?: string;
    Description?: string;
    LocationId?: number;
    Remarks?: string;
    Discontinued?: boolean;
    ClientId?: number;
    ClientName?: string;
    LocationName?: string;
}

export abstract class WarehousesRow {
    static readonly idProperty = 'WarehouseId';
    static readonly nameProperty = 'WarehouseName';
    static readonly localTextPrefix = 'Default.Warehouses';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<WarehousesRow>();
}