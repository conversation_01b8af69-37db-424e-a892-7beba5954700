﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface CountriesForm {
    CountryName: StringEditor;
}

export class CountriesForm extends PrefixedContext {
    static readonly formKey = 'Default.Countries';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CountriesForm.init)  {
            CountriesForm.init = true;

            var w0 = StringEditor;

            initFormType(CountriesForm, [
                'CountryName', w0
            ]);
        }
    }
}