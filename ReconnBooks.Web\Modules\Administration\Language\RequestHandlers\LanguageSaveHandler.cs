﻿using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Administration.LanguageRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Administration.LanguageRow;

namespace ReconnBooks.Administration;
public interface ILanguageSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> { }
public class LanguageSaveHandler(IRequestContext context) :
    SaveRequestHandler<MyRow, MyRequest, MyResponse>(context), ILanguageSaveHandler
{
}