﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { ReceiptsRow } from "./ReceiptsRow";

export interface ReceiptsColumns {
    RowNumber: Column<ReceiptsRow>;
    ReceiptNo: Column<ReceiptsRow>;
    ReceiptDate: Column<ReceiptsRow>;
    CustomerCompanyName: Column<ReceiptsRow>;
    AmountReceived: Column<ReceiptsRow>;
    TDSRateId: Column<ReceiptsRow>;
    TCSRateId: Column<ReceiptsRow>;
    TotalReceivable: Column<ReceiptsRow>;
    OnAccount: Column<ReceiptsRow>;
    Narration: Column<ReceiptsRow>;
    ModeOfPayment: Column<ReceiptsRow>;
    PaymentRefNo: Column<ReceiptsRow>;
    ChequeDdNo: Column<ReceiptsRow>;
    ChequeDdDate: Column<ReceiptsRow>;
    BankBranchName: Column<ReceiptsRow>;
    FinancialYearName: Column<ReceiptsRow>;
    ReceiptMonth: Column<ReceiptsRow>;
    Remarks: Column<ReceiptsRow>;
    ClientId: Column<ReceiptsRow>;
    PreparedByUserUsername: Column<ReceiptsRow>;
    PreparedDate: Column<ReceiptsRow>;
    VerifiedByUserUsername: Column<ReceiptsRow>;
    VerifiedDate: Column<ReceiptsRow>;
    AuthorizedByUserUsername: Column<ReceiptsRow>;
    AuthorizedDate: Column<ReceiptsRow>;
    ModifiedByUserUsername: Column<ReceiptsRow>;
    ModifiedDate: Column<ReceiptsRow>;
    CancelledByUserUsername: Column<ReceiptsRow>;
    CancelledDate: Column<ReceiptsRow>;
    AuthorizedStatus: Column<ReceiptsRow>;
    ReceiptId: Column<ReceiptsRow>;
}

export class ReceiptsColumns extends ColumnsBase<ReceiptsRow> {
    static readonly columnsKey = 'Default.Receipts';
    static readonly Fields = fieldsProxy<ReceiptsColumns>();
}

[IndianNumberFormatter]; // referenced types