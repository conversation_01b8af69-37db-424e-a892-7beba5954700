﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.CommodityTypesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.CommodityTypesRow;

namespace ReconnBooks.Default;

public interface ICommodityTypesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class CommodityTypesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, ICommodityTypesSaveHandler
{
    public CommodityTypesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}