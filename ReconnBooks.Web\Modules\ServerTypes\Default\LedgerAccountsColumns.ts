﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { LedgerAccountsRow } from "./LedgerAccountsRow";

export interface LedgerAccountsColumns {
    RowNumber: Column<LedgerAccountsRow>;
    LedgerAccountName: Column<LedgerAccountsRow>;
    SecondaryGroupName: Column<LedgerAccountsRow>;
    LedgerCreationDate: Column<LedgerAccountsRow>;
    LedgerAccountId: Column<LedgerAccountsRow>;
}

export class LedgerAccountsColumns extends ColumnsBase<LedgerAccountsRow> {
    static readonly columnsKey = 'Default.LedgerAccounts';
    static readonly Fields = fieldsProxy<LedgerAccountsColumns>();
}