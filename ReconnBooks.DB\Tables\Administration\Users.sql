﻿CREATE TABLE [dbo].[Users] (
    [UserId]              INT            IDENTITY (1, 1) NOT NULL,
    [Username]            NVARCHAR (100) NOT NULL,
    [DisplayName]         NVARCHAR (100) NOT NULL,
    [EmployeeId]          INT            NULL,
    [Email]               NVARCHAR (100) NULL,
    [Source]              NVARCHAR (4)   NOT NULL,
    [PasswordHash]        NVARCHAR (86)  NOT NULL,
    [PasswordSalt]        NVARCHAR (10)  NOT NULL,
    [LastDirectoryUpdate] DATETIME       NULL,
    [UserImage]           NVARCHAR (100) NULL,
    [InsertDate]          DATETIME       NOT NULL,
    [InsertUserId]        INT            NOT NULL,
    [UpdateDate]          DATETIME       NULL,
    [UpdateUserId]        INT            NULL,
    [IsActive]            SMALLINT       CONSTRAINT [DF_Users_IsActive] DEFAULT ((1)) NOT NULL,
    [MobilePhoneNumber]   NVARCHAR (20)  NULL,
    [TwoFactorData]       NVARCHAR (MAX) NULL,
    [TwoFactorAuth]       INT            NULL,
    [MobilePhoneVerified] BIT            DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([UserId] ASC),
    CONSTRAINT [FK_Users_Employees] FOREIGN KEY ([EmployeeId]) REFERENCES [dbo].[Employees] ([EmployeeId])
);


GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_Username]
    ON [dbo].[Users]([Username] ASC);





