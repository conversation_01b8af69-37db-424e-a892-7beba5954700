﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CustomerRepresentativesRow } from "./CustomerRepresentativesRow";

export interface CustomerRepresentativesColumns {
    RepresentativeId: Column<CustomerRepresentativesRow>;
    CustomerId: Column<CustomerRepresentativesRow>;
    EmployeeId: Column<CustomerRepresentativesRow>;
}

export class CustomerRepresentativesColumns extends ColumnsBase<CustomerRepresentativesRow> {
    static readonly columnsKey = 'Default.CustomerRepresentatives';
    static readonly Fields = fieldsProxy<CustomerRepresentativesColumns>();
}