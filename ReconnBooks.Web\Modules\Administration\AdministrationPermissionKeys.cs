namespace ReconnBooks.Administration;

[NestedPermissionKeys]
[DisplayName("Administration")]
public class PermissionKeys
{
    [Description("User, Role Management and Permissions")]
    public const string Security = "Administration:Security";

    [Description("Languages and Translations")]
    public const string Translation = "Administration:Translation";

    //[Description("Client Management")]
    //public const string Client = "Administration:Client";

    public class Clients
    {
        [Description("Client User")]
        public const string User = "Administration:Clients:User";

        [Description("Client Admin")]
        public const string Admin = "Administration:Clients:Admin";
    }

    public class Consultants
    {
        [Description("Consultant Admin")]
        public const string Admin = "Administration:Consultants:Admin";

        [Description("Consultant User")]
        public const string User = "Administration:Consultants:User";
    }

    public class Employees
    {
        public const string Insert = "Administration:Employees:Insert";
        public const string Modify = "Administration:Employees:Modify";
        public const string Delete = "Administration:Employees:Delete";
        public const string View = "Administration:Employees:View";
        public const string LookupView = "Administration:Employees:LookupRead";
    }

    public class Users
    {
        [Description("Lookup Read")]
        public const string LookupView = "Administration:Users:LookupRead";
    }

    [Description("Can Verify Record")]
    public const string Verify = "Administration:VerifiedBy";

    [Description("Can Authorize Record")]
    public const string Authorise = "Administration:AuthorisedBy";

}

