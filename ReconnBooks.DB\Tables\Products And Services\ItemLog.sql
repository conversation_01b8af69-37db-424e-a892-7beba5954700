﻿CREATE TABLE [dbo].[ItemLog] 
(
    [ItemLogId]			BIGINT			NOT NULL		IDENTITY (1,1),
    [OperationType]		SMALLINT		NOT NULL,
    [ChangingUserId]	INT					NULL,
    [Valid<PERSON>rom]			DATETIME		NOT NULL,
    [ValidUntil]		DATETIME		NOT NULL,
    [ItemId]			INT				NOT NULL,
    [ItemName]			NVARCHAR (100)		NULL,
    [ItemImage]			NVARCHAR (100)		NULL,
    [Discontinued]		BIT					NULL,
    [SupplierId]		INT					NULL,
    [CategoryId]		INT					NULL,
    [QuantityPerUnit]	NVARCHAR (20)		NULL,
    [UnitPrice]			MONEY				NULL,
    [UnitsInStock]		SMALLINT			NULL,
    [UnitsOnInvoice]    SMALLINT			NULL,
    [ReinvoiceLevel]    SMALLINT			NULL,
    
	CONSTRAINT [PK_ItemLog] PRIMARY KEY	CLUSTERED	([ItemLogId] ASC)
);

