﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface NatureOfSupplyRow {
    RowNumber?: number;
    NatureOfSupplyId?: number;
    NatureOfSupply?: string;
    Description?: string;
}

export abstract class NatureOfSupplyRow {
    static readonly idProperty = 'NatureOfSupplyId';
    static readonly nameProperty = 'NatureOfSupply';
    static readonly localTextPrefix = 'Default.NatureOfSupply';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<NatureOfSupplyRow>();
}