using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.ClientBankAccounts")]
[BasedOnRow(typeof(ClientBankAccountsRow), CheckNames = true)]
public class ClientBankAccountsColumns
{
    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int ClientBankAccountId { get; set; }
    [Hidden]
    public int ClientId { get; set; }
    
    [Hidden]
    public string AccountName { get; set; }

    [EditLink, DisplayName("Bank Name"), Width(150)]
    public string BankName { get; set; }

    [EditLink, DisplayName("Branch Name"), Width(150)]
    public string BranchName { get; set; }

    [EditLink, DisplayName("Account No."), Width(100)]
    public string AccountNumber { get; set; }

    [DisplayName("IFSC Code"), Width(100)]
    public string IFSCCode { get; set; }

    [DisplayName("Branch Code"), Width(100)]
    public string BranchCode { get; set; }

    [DisplayName("Swift Code"), Width(100)]
    public string SwiftCode { get; set; }
    
    public string QRCode { get; set; }
        
    public bool Status { get; set; }
}