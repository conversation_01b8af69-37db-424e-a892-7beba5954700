using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.Clients")]
[BasedOnRow(typeof(ClientsRow), CheckNames = true)]
public class ClientsForm
{
    [Category("Client Details")]
    [FullWidth(UntilNext = true)]
    [DisplayName("Client Name")]
    public string ClientName { get; set; }

    [HalfWidth]
    [DisplayName("Short Name")]
    public string ClientCode { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Address")]
    public string Address { get; set; }

    [Hidden]
    public string Address2 { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("City")]
    public int CityId { get; set; }

    [DisplayName("Pin Code")]
    public string PINCode { get; set; }
    
    [FullWidth]
    [DisplayName("Consultant Name")]
    public int ConsultantId { get; set; }

    [Category("Company Details")]
    [HalfWidth(UntilNext = true)]
    [DisplayName("GST No.")]
    public string GSTIN { get; set; }

    [DisplayName("Place Of Supply")]
    public int PlaceOfSupplyId { get; set; }

    [DisplayName("Nature Of Supply")]
    public int NatureOfSupplyId { get; set; }

    [DisplayName("Supply Type")]
    public int SupplyTypeId { get; set; }

    [DisplayName("Company PAN")]
    public string PAN { get; set; }

    [DisplayName("Import/Export No.")]
    public string IECNo { get; set; }

    [DisplayName("Company CIN")]
    public string CINNo { get; set; }

    [DisplayName("Company TAN")]
    public string TANNo { get; set; }

    [DisplayName("Udyam Reg. No.")]
    public string UdyamNo { get; set; }
 
    [Category("Contact Details"), Collapsible(Collapsed = true)]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Title")]
    public int TitleId { get; set; }

    [DisplayName("Contact Person")]
    public string ClientContactName { get; set; }

    [DisplayName("Designation")]
    public int DesignationId { get; set; }

    [DisplayName("Email")]
    public string EMail { get; set; }

    [DisplayName("Mobile No.")]
    public string MobileNo { get; set; }

    [DisplayName("Alternate No.")]
    public string AlternateNo { get; set; }

    [DisplayName("Phone No.")]
    public string PhoneNo { get; set; }

    [DisplayName("Fax No.")]
    public string FaxNo { get; set; }

    [DisplayName("Web Site")]
    public string HomePage { get; set; }

    //--------------------------------------------------------------
    [Category("Bank Details"), Collapsible(Collapsed = true)]
    [FullWidth(UntilNext = true)]
    [ClientBankAccountsGridEditor]
    public List<ClientBankAccountsRow> ClientBankAccountsList { get; set; }
    //--------------------------------------------------------------

    [Category("Logo Upload"), Collapsible(Collapsed =true) ]
    [FullWidth(UntilNext = true)]
    [ImageUploadEditor]
    [DisplayName("Logo")]
    public string Logo { get; set; }

    [DisplayName("Company Tag Line")]
    public string TagLine { get; set; }

    [Category("Signature Upload"), Collapsible (Collapsed = true)]
    [ImageUploadEditor]
    [DisplayName("Client DSC")]
    public string ClientDSC { get; set; }

    [Category("Email Server Settings"), Collapsible(Collapsed = true)]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Email Server Host"), LabelWidth(180)]
    public string EmailServerHost { get; set; }

    [DefaultValue(587)]
    [DisplayName("Email Server Port"), LabelWidth(180)]
    public int EmailServerPort { get; set; }

    [DisplayName("Email Server UserName"), LabelWidth(180)]
    public string EmailServerUsername { get; set; }

    [PasswordEditor]
    [DisplayName("Email Server Password"), LabelWidth(180)]
    public string EmailServerPassword { get; set; }

    [Category("Other Details"), Collapsible(Collapsed = true)]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Business Type")]
    public int BusinessTypeId { get; set; }

    [DisplayName("Business Group")]
    public int BusinessGroupId { get; set; }

    [DisplayName("Business Category")]
    public int BusinessCategoryId { get; set; }

    [DisplayName("Invoice Format")]
    public string InvoiceNoFormat { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Disclaimer")]
    public string Disclaimer { get; set; }
}