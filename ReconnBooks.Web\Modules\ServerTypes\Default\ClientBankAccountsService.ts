﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ClientBankAccountsRow } from "./ClientBankAccountsRow";

export namespace ClientBankAccountsService {
    export const baseUrl = 'Default/ClientBankAccounts';

    export declare function Create(request: SaveRequest<ClientBankAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ClientBankAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ClientBankAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ClientBankAccountsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ClientBankAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ClientBankAccountsRow>>;

    export const Methods = {
        Create: "Default/ClientBankAccounts/Create",
        Update: "Default/ClientBankAccounts/Update",
        Delete: "Default/ClientBankAccounts/Delete",
        Retrieve: "Default/ClientBankAccounts/Retrieve",
        List: "Default/ClientBankAccounts/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ClientBankAccountsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}