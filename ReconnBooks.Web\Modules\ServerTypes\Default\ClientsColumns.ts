﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ClientsRow } from "./ClientsRow";

export interface ClientsColumns {
    RowNumber: Column<ClientsRow>;
    ClientName: Column<ClientsRow>;
    ConsultantName: Column<ClientsRow>;
    ClientCode: Column<ClientsRow>;
    Address: Column<ClientsRow>;
    Address2: Column<ClientsRow>;
    CityName: Column<ClientsRow>;
    PINCode: Column<ClientsRow>;
    PhoneNo: Column<ClientsRow>;
    FaxNo: Column<ClientsRow>;
    HomePage: Column<ClientsRow>;
    Logo: Column<ClientsRow>;
    ClientContactName: Column<ClientsRow>;
    MobileNo: Column<ClientsRow>;
    AlternateNo: Column<ClientsRow>;
    EMail: Column<ClientsRow>;
    GSTIN: Column<ClientsRow>;
    PAN: Column<ClientsRow>;
    IECNo: Column<ClientsRow>;
    CINNo: Column<ClientsRow>;
    TANNo: Column<ClientsRow>;
    UdyamNo: Column<ClientsRow>;
    TagLine: Column<ClientsRow>;
    ClientDSC: Column<ClientsRow>;
    InvoiceNoFormat: Column<ClientsRow>;
    Disclaimer: Column<ClientsRow>;
    ClientId: Column<ClientsRow>;
}

export class ClientsColumns extends ColumnsBase<ClientsRow> {
    static readonly columnsKey = 'Default.Clients';
    static readonly Fields = fieldsProxy<ClientsColumns>();
}