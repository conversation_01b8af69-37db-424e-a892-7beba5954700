﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CommodityTypesRow } from "./CommodityTypesRow";

export namespace CommodityTypesService {
    export const baseUrl = 'Default/CommodityTypes';

    export declare function Create(request: SaveRequest<CommodityTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CommodityTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CommodityTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CommodityTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CommodityTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CommodityTypesRow>>;

    export const Methods = {
        Create: "Default/CommodityTypes/Create",
        Update: "Default/CommodityTypes/Update",
        Delete: "Default/CommodityTypes/Delete",
        Retrieve: "Default/CommodityTypes/Retrieve",
        List: "Default/CommodityTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CommodityTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}