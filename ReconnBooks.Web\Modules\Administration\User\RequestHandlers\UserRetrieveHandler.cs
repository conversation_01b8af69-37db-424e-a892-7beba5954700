using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Administration.UserRow>;
using MyRow = ReconnBooks.Administration.UserRow;


namespace ReconnBooks.Administration;
public interface IUserRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> { }
public class UserRetrieveHandler(IRequestContext context) :
    RetrieveRequestHandler<MyRow, MyRequest, MyResponse>(context), IUserRetrieveHandler
{
    protected override void PrepareQuery(SqlQuery query)
    {
        base.PrepareQuery(query);

        var username = User.Identity.Name;

        if (username != "admin" && Permissions.HasPermission(PermissionKeys.Clients.User))
        {
            query.Where(MyRow.Fields.ClientId == User.GetClientId().Value);
        }
    }
}