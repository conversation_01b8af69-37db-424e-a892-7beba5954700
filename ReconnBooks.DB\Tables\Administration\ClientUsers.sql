﻿CREATE TABLE [dbo].[ClientUsers] 
(
    [ClientUserId]  INT     NOT NULL    IDENTITY (1, 1),
    [UserId]        INT     NOT NULL,
    [ClientId]      INT     NOT NULL,
    [Status]        BIT     NOT NULL    DEFAULT ((0)),

    CONSTRAINT [PK_ClientUsers] PRIMARY KEY CLUSTERED   ([ClientUserId] ASC, [UserId] ASC),
    CONSTRAINT [FK_ClientUsers_Clients] FOREIGN KEY     ([ClientId])    REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_ClientUsers_Users]   FOREIGN KEY     ([UserId])      REFERENCES [dbo].[Users] ([UserId])
);

GO
CREATE NONCLUSTERED INDEX [UserId]
    ON [dbo].[ClientUsers]([UserId] ASC);
GO
CREATE NONCLUSTERED INDEX [ClientId]
    ON [dbo].[ClientUsers]([ClientId] ASC);

