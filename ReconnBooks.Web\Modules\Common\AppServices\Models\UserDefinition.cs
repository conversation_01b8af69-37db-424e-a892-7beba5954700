namespace ReconnBooks;

[Serializable]
public class UserDefinition : IUserDefinition, IHasPassword, ITwoFactorUserDefinition
{
    public string Id => UserId.ToInvariant();
    public string DisplayName { get; set; }
    public string Email { get; set; }
    public string MobilePhoneNumber { get; set; }
    public string UserImage { get; set; }
    public short IsActive { get; set; }
    public int UserId { get; set; }
    public string Username { get; set; }
    public string PasswordHash { get; set; }
    public string PasswordSalt { get; set; }
    public string Source { get; set; }
    public DateTime? UpdateDate { get; set; }
    public DateTime? LastDirectoryUpdate { get; set; }
    public TwoFactorUserData TwoFactorData { get; set; }
    public bool HasPassword => PasswordSalt != "unassigned";
    public int? ClientId { get; set; }
    public int? ConsultantId { get; set; }
    public string ClientName { get; set; }
    public string ClientCode { get; set; }
    public string ClientLogo { get; set; }
    public string ConsultantName { get; set; }
    public string ConsultantLogo { get; set; }
    public int? PlaceOfSupplyStateId { get; set; }
}