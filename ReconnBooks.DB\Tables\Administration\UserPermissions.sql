﻿CREATE TABLE [dbo].[UserPermissions] 
(
    [UserPermissionId]  BIGINT          NOT NULL	IDENTITY (1,1),
    [UserId]            INT             NOT NULL,
    [PermissionKey]     NVARCHAR (100)  NOT NULL,
    [Granted]           BIT             NOT NULL	CONSTRAINT	[DF_UserPermissions_Granted]	DEFAULT ((1)),
    
    CONSTRAINT [PK_UserPermissions] PRIMARY KEY CLUSTERED   ([UserPermissionId] ASC),
    CONSTRAINT [FK_UserPermissions_UserId]  FOREIGN KEY     ([UserId])	REFERENCES	[dbo].[Users]	([UserId])
);
GO
CREATE UNIQUE NONCLUSTERED INDEX [UQ_UserPerm_UserId_PermKey]
    ON [dbo].[UserPermissions]([UserId] ASC, [PermissionKey] ASC);

