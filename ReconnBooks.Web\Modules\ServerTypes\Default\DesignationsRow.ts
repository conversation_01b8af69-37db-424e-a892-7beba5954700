﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface DesignationsRow {
    RowNumber?: number;
    DesignationId?: number;
    Designation?: string;
    Description?: string;
}

export abstract class DesignationsRow {
    static readonly idProperty = 'DesignationId';
    static readonly nameProperty = 'Designation';
    static readonly localTextPrefix = 'Default.Designations';
    static readonly lookupKey = 'Default.Designations';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<DesignationsRow>('Default.Designations') }
    static async getLookupAsync() { return getLookupAsync<DesignationsRow>('Default.Designations') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DesignationsRow>();
}