﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { PoAmendmentDetailsRow } from "./PoAmendmentDetailsRow";

export interface PoAmendmentDetailsColumns {
    RowNumber: Column<PoAmendmentDetailsRow>;
    POAmendmentNo: Column<PoAmendmentDetailsRow>;
    CommodityName: Column<PoAmendmentDetailsRow>;
    CommodityCode: Column<PoAmendmentDetailsRow>;
    CommodityType: Column<PoAmendmentDetailsRow>;
    POQuantity: Column<PoAmendmentDetailsRow>;
    POUnitUnitName: Column<PoAmendmentDetailsRow>;
    POUnitPrice: Column<PoAmendmentDetailsRow>;
    AmendedQuantity: Column<PoAmendmentDetailsRow>;
    AmendedUnitUnitName: Column<PoAmendmentDetailsRow>;
    AmendedUnitPrice: Column<PoAmendmentDetailsRow>;
    PendingQuantity: Column<PoAmendmentDetailsRow>;
    TaxableAmountPerUnit: Column<PoAmendmentDetailsRow>;
    NetTaxableAmount: Column<PoAmendmentDetailsRow>;
    GSTRateRemarks: Column<PoAmendmentDetailsRow>;
    IGSTRate: Column<PoAmendmentDetailsRow>;
    IGSTAmountPerUnit: Column<PoAmendmentDetailsRow>;
    NetIGSTAmount: Column<PoAmendmentDetailsRow>;
    CGSTRate: Column<PoAmendmentDetailsRow>;
    CGSTAmountPerUnit: Column<PoAmendmentDetailsRow>;
    NetCGSTAmount: Column<PoAmendmentDetailsRow>;
    SGSTRate: Column<PoAmendmentDetailsRow>;
    SGSTAmountPerUnit: Column<PoAmendmentDetailsRow>;
    NetSGSTAmount: Column<PoAmendmentDetailsRow>;
    NetAmount: Column<PoAmendmentDetailsRow>;
    NetPricePerUnit: Column<PoAmendmentDetailsRow>;
    CommodityDescription: Column<PoAmendmentDetailsRow>;
    POAmendmentDetailId: Column<PoAmendmentDetailsRow>;
}

export class PoAmendmentDetailsColumns extends ColumnsBase<PoAmendmentDetailsRow> {
    static readonly columnsKey = 'Default.PoAmendmentDetails';
    static readonly Fields = fieldsProxy<PoAmendmentDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types