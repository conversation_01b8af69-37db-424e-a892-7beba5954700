﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ProductCategoriesRow } from "./ProductCategoriesRow";

export namespace ProductCategoriesService {
    export const baseUrl = 'Default/ProductCategories';

    export declare function Create(request: SaveRequest<ProductCategoriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProductCategoriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProductCategoriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProductCategoriesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProductCategoriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProductCategoriesRow>>;

    export const Methods = {
        Create: "Default/ProductCategories/Create",
        Update: "Default/ProductCategories/Update",
        Delete: "Default/ProductCategories/Delete",
        Retrieve: "Default/ProductCategories/Retrieve",
        List: "Default/ProductCategories/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ProductCategoriesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}