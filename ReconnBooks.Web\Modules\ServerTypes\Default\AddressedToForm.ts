﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface AddressedToForm {
    AddressedTo: StringEditor;
}

export class AddressedToForm extends PrefixedContext {
    static readonly formKey = 'Default.AddressedTo';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!AddressedToForm.init)  {
            AddressedToForm.init = true;

            var w0 = StringEditor;

            initFormType(AddressedToForm, [
                'AddressedTo', w0
            ]);
        }
    }
}