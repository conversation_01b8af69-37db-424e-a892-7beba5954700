﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { FinancialYearsRow } from "./FinancialYearsRow";

export interface FinancialYearsColumns {
    RowNumber: Column<FinancialYearsRow>;
    FinancialYearId: Column<FinancialYearsRow>;
    FinancialYearName: Column<FinancialYearsRow>;
    FromDate: Column<FinancialYearsRow>;
    ToDate: Column<FinancialYearsRow>;
    Remarks: Column<FinancialYearsRow>;
}

export class FinancialYearsColumns extends ColumnsBase<FinancialYearsRow> {
    static readonly columnsKey = 'Default.FinancialYears';
    static readonly Fields = fieldsProxy<FinancialYearsColumns>();
}