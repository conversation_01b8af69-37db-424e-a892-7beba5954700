import { BusinessGroupsColumns, BusinessGroupsRow, BusinessGroupsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { BusinessGroupsDialog } from './BusinessGroupsDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.BusinessGroupsGrid')
export class BusinessGroupsGrid extends EntityGridDialog<BusinessGroupsRow, any> {
    protected getColumnsKey() { return BusinessGroupsColumns.columnsKey; }
    protected getDialogType() { return BusinessGroupsDialog; }
    protected getRowDefinition() { return BusinessGroupsRow; }
    protected getService() { return BusinessGroupsService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}