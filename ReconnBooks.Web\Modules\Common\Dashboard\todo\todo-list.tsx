import { Fluent } from "@serenity-is/corelib";
import { TodoItem } from "./todo-item";
import { Todo } from "./todo-types";

declare var Sortable: any;
export const TodoList = ({ todos }: { todos: Todo[] }) =>
    <ul class="s-todo-list" ref={ul => {
        Fluent(ul).on('change', () => {
            localStorage.setItem("todos", JSON.stringify(window.todoData));
        });
        typeof Sortable !== "undefined" && Sortable.create(ul, {
            handle: ".handle"
        });
    }}>
        {todos.filter(x => !x.done).map(todo => <TodoItem todo={todo} />)}
    </ul>;
