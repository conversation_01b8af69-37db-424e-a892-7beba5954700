﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { TcsRatesRow } from "./TcsRatesRow";

export interface TcsRatesColumns {
    RowNumber: Column<TcsRatesRow>;
    Section: Column<TcsRatesRow>;
    NatureOfTransaction: Column<TcsRatesRow>;
    TCSRate: Column<TcsRatesRow>;
    Collector: Column<TcsRatesRow>;
    Collectee: Column<TcsRatesRow>;
    WefDate: Column<TcsRatesRow>;
    FinancialYearName: Column<TcsRatesRow>;
    IsDefault: Column<TcsRatesRow>;
    Description: Column<TcsRatesRow>;
    TCSRateId: Column<TcsRatesRow>;
}

export class TcsRatesColumns extends ColumnsBase<TcsRatesRow> {
    static readonly columnsKey = 'Default.TcsRates';
    static readonly Fields = fieldsProxy<TcsRatesColumns>();
}