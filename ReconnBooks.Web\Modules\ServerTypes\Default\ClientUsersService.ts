﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceRequest, serviceRequest } from "@serenity-is/corelib";
import { ClientUserServiceResponse } from "../Modules/Default.ClientUsers.ClientUserServiceResponse";
import { ClientUsersRow } from "./ClientUsersRow";

export namespace ClientUsersService {
    export const baseUrl = 'Default/ClientUsers';

    export declare function Create(request: SaveRequest<ClientUsersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ClientUsersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ClientUsersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ClientUsersRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ClientUsersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ClientUsersRow>>;
    export declare function ActiveClient(request: ServiceRequest, onSuccess?: (response: ClientUserServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ClientUserServiceResponse>;
    export declare function GetListOfUserClients(request: RetrieveRequest, onSuccess?: (response: ListResponse<ClientUsersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ClientUsersRow>>;

    export const Methods = {
        Create: "Default/ClientUsers/Create",
        Update: "Default/ClientUsers/Update",
        Delete: "Default/ClientUsers/Delete",
        Retrieve: "Default/ClientUsers/Retrieve",
        List: "Default/ClientUsers/List",
        ActiveClient: "Default/ClientUsers/ActiveClient",
        GetListOfUserClients: "Default/ClientUsers/GetListOfUserClients"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'ActiveClient', 
        'GetListOfUserClients'
    ].forEach(x => {
        (<any>ClientUsersService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}