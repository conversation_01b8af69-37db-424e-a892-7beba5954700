﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ModeOfPaymentsRow {
    RowNumber?: number;
    ModeOfPaymentId?: number;
    ModeOfPayment?: string;
    Description?: string;
}

export abstract class ModeOfPaymentsRow {
    static readonly idProperty = 'ModeOfPaymentId';
    static readonly nameProperty = 'ModeOfPayment';
    static readonly localTextPrefix = 'Default.ModeOfPayments';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ModeOfPaymentsRow>();
}