﻿import { StringEditor, TextAreaEditor, ServiceLookupEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { LocationsDialog } from "../../Default/Locations/LocationsDialog";
import { WarehousesDialog } from "../../Default/Warehouses/WarehousesDialog";

export interface StoresForm {
    StoreName: StringEditor;
    StoreDescription: TextAreaEditor;
    Remarks: StringEditor;
    LocationId: ServiceLookupEditor;
    WarehouseId: ServiceLookupEditor;
    SetDefault: BooleanEditor;
    Discontinued: BooleanEditor;
}

export class StoresForm extends PrefixedContext {
    static readonly formKey = 'Default.Stores';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!StoresForm.init)  {
            StoresForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;
            var w2 = ServiceLookupEditor;
            var w3 = BooleanEditor;

            initFormType(StoresForm, [
                'StoreName', w0,
                'StoreDescription', w1,
                'Remarks', w0,
                'LocationId', w2,
                'WarehouseId', w2,
                'SetDefault', w3,
                'Discontinued', w3
            ]);
        }
    }
}

queueMicrotask(() => [LocationsDialog, WarehousesDialog]); // referenced dialogs