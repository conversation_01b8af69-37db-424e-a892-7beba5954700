namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.HsnSummaryDetailsColumns")]
[BasedOnRow(typeof(HsnSummaryDetailsRow), CheckNames = true)]
public class HsnSummaryDetailsColumns
{
    [DisplayName(""), Width(30), AlignCenter]
    public long RowNumber { get; set; }     //Serial Numbering

    [DisplayName("Invoice No."), Width(130)]
    public string InvoiceNo { get; set; }

    [DisplayName("Date"), Width(80)]
    public DateTime InvoiceDate { get; set; }

    [DisplayName("HSN/SAC Code"), Width(90)]
    public string HSNSACCode { get; set; }

    [DisplayName("Qty."), Width(60)]
    public decimal Quantity { get; set; }

    [DisplayName("Net Taxable Amt."), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetTaxableAmount { get; set; }

    [DisplayName("IGST %"), Width(90), IndianNumberFormatter, AlignCenter]
    public decimal IGSTRate { get; set; }

    [DisplayName("Net IGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetIGSTAmount { get; set; }

    [DisplayName("CGST %"), Width(90), IndianNumberFormatter, AlignCenter]
    public decimal CGSTRate { get; set; }

    [DisplayName("Net CGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetCGSTAmount { get; set; }

    [DisplayName("SGST %"), Width(90), IndianNumberFormatter, AlignCenter]
    public decimal SGSTRate { get; set; }

    [DisplayName("Net SGST"), Width(90), IndianNumberFormatter, AlignRight]
    public decimal NetSGSTAmount { get; set; }

    [DisplayName("Net Amount"), Width(100), IndianNumberFormatter, AlignRight]
    public decimal NetAmount { get; set; }
    
    //[DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    //public int InvoiceDetailId { get; set; }

}