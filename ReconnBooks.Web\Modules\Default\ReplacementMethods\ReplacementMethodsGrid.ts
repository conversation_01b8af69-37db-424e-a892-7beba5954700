import { ReplacementMethodsColumns, ReplacementMethodsRow, ReplacementMethodsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { ReplacementMethodsDialog } from './ReplacementMethodsDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.ReplacementMethodsGrid')
export class ReplacementMethodsGrid extends EntityGridDialog<ReplacementMethodsRow, any> {
    protected getColumnsKey() { return ReplacementMethodsColumns.columnsKey; }
    protected getDialogType() { return ReplacementMethodsDialog; }
    protected getRowDefinition() { return ReplacementMethodsRow; }
    protected getService() { return ReplacementMethodsService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}