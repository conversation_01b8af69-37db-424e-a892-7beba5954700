﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { ProductGroupsRow } from "./ProductGroupsRow";

export interface ProductGroupsColumns {
    RowNumber: Column<ProductGroupsRow>;
    ProductGroupId: Column<ProductGroupsRow>;
    ProductGroup: Column<ProductGroupsRow>;
    ClientName: Column<ProductGroupsRow>;
}

export class ProductGroupsColumns extends ColumnsBase<ProductGroupsRow> {
    static readonly columnsKey = 'Default.ProductGroups';
    static readonly Fields = fieldsProxy<ProductGroupsColumns>();
}