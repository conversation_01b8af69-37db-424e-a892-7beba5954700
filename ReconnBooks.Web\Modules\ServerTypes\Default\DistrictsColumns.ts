﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { DistrictsRow } from "./DistrictsRow";

export interface DistrictsColumns {
    RowNumber: Column<DistrictsRow>;
    DistrictId: Column<DistrictsRow>;
    District: Column<DistrictsRow>;
    DistrictCode: Column<DistrictsRow>;
    Headquarters: Column<DistrictsRow>;
    StateName: Column<DistrictsRow>;
}

export class DistrictsColumns extends ColumnsBase<DistrictsRow> {
    static readonly columnsKey = 'Default.Districts';
    static readonly Fields = fieldsProxy<DistrictsColumns>();
}