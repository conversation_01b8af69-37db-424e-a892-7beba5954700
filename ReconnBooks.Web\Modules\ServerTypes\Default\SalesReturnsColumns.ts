﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { SalesReturnsRow } from "./SalesReturnsRow";

export interface SalesReturnsColumns {
    RowNumber: Column<SalesReturnsRow>;
    SalesReturnNo: Column<SalesReturnsRow>;
    SalesReturnDate: Column<SalesReturnsRow>;
    CustomerCompanyName: Column<SalesReturnsRow>;
    DeliveryNoteNo: Column<SalesReturnsRow>;
    InvoiceNo: Column<SalesReturnsRow>;
    FinancialYearName: Column<SalesReturnsRow>;
    SalesReturnMonth: Column<SalesReturnsRow>;
    UploadFiles: Column<SalesReturnsRow>;
    Remarks: Column<SalesReturnsRow>;
    ClientId: Column<SalesReturnsRow>;
    PreparedByUserUsername: Column<SalesReturnsRow>;
    PreparedDate: Column<SalesReturnsRow>;
    VerifiedByUserUsername: Column<SalesReturnsRow>;
    VerifiedDate: Column<SalesReturnsRow>;
    AuthorizedByUserUsername: Column<SalesReturnsRow>;
    AuthorizedDate: Column<SalesReturnsRow>;
    ModifiedByUserUsername: Column<SalesReturnsRow>;
    ModifiedDate: Column<SalesReturnsRow>;
    CancelledByUserUsername: Column<SalesReturnsRow>;
    CancelledDate: Column<SalesReturnsRow>;
    AuthorizedStatus: Column<SalesReturnsRow>;
    SalesReturnId: Column<SalesReturnsRow>;
}

export class SalesReturnsColumns extends ColumnsBase<SalesReturnsRow> {
    static readonly columnsKey = 'Default.SalesReturns';
    static readonly Fields = fieldsProxy<SalesReturnsColumns>();
}