import { SalesReturnsForm, SalesReturnsRow, SalesReturnsService, DocumentsRow, CustomersRow, StatesRow } from '@/ServerTypes/Default';
import { Decorators, formatDate, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';

@Decorators.registerClass('ReconnBooks.Default.SalesReturnsDialog')
@Decorators.responsive()
@Decorators.panel()
export class SalesReturnsDialog extends VerifyAuthorizeDialog<SalesReturnsRow> {
    protected getFormKey() { return SalesReturnsForm.formKey; }
    protected getRowDefinition() { return SalesReturnsRow; }
    protected getService() { return SalesReturnsService.baseUrl; }

    protected form = new SalesReturnsForm(this.idPrefix);
    private docType: string;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.SalesReturnDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.form.SalesReturnDetailsList.getGrid().focus();

            const grid = this.form.SalesReturnDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }

        });

        this.form.CustomerId.changeSelect2(a => {
            setTimeout(async () => {

                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    var customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];

                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;
                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.SalesReturnDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            }, 100);
        })

        this.form.InvoiceId.changeSelect2(e => {
            if (this.form.InvoiceId.value === '') {
                // Clear the details in the grid
                this.form.SalesReturnDetailsList.value = [];
            }
            else {
                SalesReturnsService.GetFromInvoiceDetails({
                    EntityId: toId(this.form.InvoiceId.value)
                },
                    response => {
                        this.form.SalesReturnDetailsList.value = response.Entities;
                    });
            }
        });

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                    this.getNextNumber();
                    this.setDialogsLoadedState();
                });
            }
            else {
                this.getNextNumber();
                this.setDialogsLoadedState();
            }
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.SalesReturnDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    protected updateInterface() {

        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    private getNextNumber(): any {

        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "Sales Returns")[0].DocumentShortName;
        }

        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.SalesReturnNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.SalesReturnDetailsList.value || this.form.SalesReturnDetailsList.value.length === 0) {
            alertDialog("Sales Return cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }
    private clearCustomerFields() {
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.SalesReturnDetailsList.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Sales Return";
    }
} 