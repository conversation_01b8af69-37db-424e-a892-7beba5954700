﻿import { StringEditor, BooleanEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { UqCsDialog } from "../../Default/UqCs/UqCsDialog";

export interface UnitsForm {
    UnitName: StringEditor;
    UnitDescription: StringEditor;
    SetDefault: BooleanEditor;
    UqcId: ServiceLookupEditor;
}

export class UnitsForm extends PrefixedContext {
    static readonly formKey = 'Default.Units';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!UnitsForm.init)  {
            UnitsForm.init = true;

            var w0 = StringEditor;
            var w1 = BooleanEditor;
            var w2 = ServiceLookupEditor;

            initFormType(UnitsForm, [
                'UnitName', w0,
                'UnitDescription', w0,
                'SetDefault', w1,
                'UqcId', w2
            ]);
        }
    }
}

queueMicrotask(() => [UqCsDialog]); // referenced dialogs