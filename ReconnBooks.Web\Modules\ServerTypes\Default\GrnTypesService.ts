﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GrnTypesRow } from "./GrnTypesRow";

export namespace GrnTypesService {
    export const baseUrl = 'Default/GrnTypes';

    export declare function Create(request: SaveRequest<GrnTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GrnTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GrnTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GrnTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GrnTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GrnTypesRow>>;

    export const Methods = {
        Create: "Default/GrnTypes/Create",
        Update: "Default/GrnTypes/Update",
        Delete: "Default/GrnTypes/Delete",
        Retrieve: "Default/GrnTypes/Retrieve",
        List: "Default/GrnTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>GrnTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}