import { ClientsColumns, ClientsRow, ClientsService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, gridPageInit } from '@serenity-is/corelib';
import { ClientsDialog } from './ClientsDialog';

@Decorators.registerClass('ReconnBooks.Default.ClientsGrid')
export class ClientsGrid extends EntityGrid<ClientsRow> {
    protected getColumnsKey() { return ClientsColumns.columnsKey; }
    protected getDialogType() { return ClientsDialog; }
    protected getRowDefinition() { return ClientsRow; }
    protected getService() { return ClientsService.baseUrl; }
}