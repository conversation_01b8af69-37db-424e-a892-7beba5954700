using ReconnBooks.Administration;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using ReconnBooks.Modules.Default.Consultants;
using System.ComponentModel;
using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.RowBehaviors;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("Clients")]
[Di<PERSON><PERSON><PERSON><PERSON>("Clients"), InstanceName("Clients"), GenerateFields]
[ReadPermission(PermissionKeys.Clients.User)]
[ModifyPermission(PermissionKeys.Clients.Admin)]
//[ServiceLookupPermission("Administration:General")]
[LookupScript(Permission = PermissionKeys.Security, LookupType = typeof(MultiConsultantRowLookup<>))]
public sealed partial class ClientsRow : Row<ClientsRow.RowFields>, IIdRow, INameRow, IMultiConsultantRow, IRowNumberedRow //Row Number adding
{
    const string jCity = nameof(jCity);
    const string jTitle = nameof(jTitle);
    const string jDesignation = nameof(jDesignation);
    const string jPlaceOfSupply = nameof(jPlaceOfSupply);
    const string jNatureOfSupply = nameof(jNatureOfSupply);
    const string jSupplyType = nameof(jSupplyType);
    const string jBank = nameof(jBank);
    const string jBusinessType = nameof(jBusinessType);
    const string jBusinessGroup = nameof(jBusinessGroup);
    const string jBusinessCategory = nameof(jBusinessCategory);
    const string jConsultant = nameof(jConsultant);

    //--------- To Add serial Nos. -----------
    [DisplayName("Sl.No."), NotMapped]
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value;}
    public Int64Field RowNumberField { get => fields.RowNumber; }
    //---------------------

    [DisplayName("Client Id"), Identity, IdProperty]
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Client Name"), Size(500), NotNull, QuickSearch, NameProperty]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }

    [DisplayName("Short Name"), Size(6), NotNull, LookupInclude]
    public string ClientCode { get => fields.ClientCode[this]; set => fields.ClientCode[this] = value; }

    [DisplayName("Consultant Name"), ForeignKey(typeof(ConsultantsRow)), LeftJoin(jConsultant), TextualField(nameof(ConsultantName)), LookupInclude]
    [ServiceLookupEditor(typeof(ConsultantsRow), InplaceAdd = true, Service = "Default/Consultants/List")]
    public int? ConsultantId { get => fields.ConsultantId[this]; set => fields.ConsultantId[this] = value; }
    
    [DisplayName("Address"), NotNull]
    public string Address { get => fields.Address[this]; set => fields.Address[this] = value; }

    [DisplayName("Address2"), Size(250)]
    public string Address2 { get => fields.Address2[this]; set => fields.Address2[this] = value; }

    [DisplayName("City"), NotNull, ForeignKey(typeof(CitiesRow)), LeftJoin(jCity), TextualField(nameof(CityName))]
    [ServiceLookupEditor(typeof(CitiesRow), InplaceAdd = true, Service = "Default/Cities/List")]
    public int? CityId { get => fields.CityId[this]; set => fields.CityId[this] = value; }

    [DisplayName("Pin Code"), Column("PINCode"), Size(10)]
    public string PINCode { get => fields.PINCode[this]; set => fields.PINCode[this] = value; }

    [DisplayName("Phone No."), Size(50)]
    public string PhoneNo { get => fields.PhoneNo[this]; set => fields.PhoneNo[this] = value; }

    [DisplayName("Fax No."), Size(50)]
    public string FaxNo { get => fields.FaxNo[this]; set => fields.FaxNo[this] = value; }

    [DisplayName("Web Site"), Size(1073741823)]
    public string HomePage { get => fields.HomePage[this]; set => fields.HomePage[this] = value; }

    [DisplayName("Logo")]
    public string Logo { get => fields.Logo[this]; set => fields.Logo[this] = value; }

    [DisplayName("Title"), ForeignKey(typeof(TitlesRow)), LeftJoin(jTitle), TextualField(nameof(TitleOfRespect))]
    [ServiceLookupEditor(typeof(TitlesRow), InplaceAdd = true, Service = "Default/Titles/List")]
    public int? TitleId { get => fields.TitleId[this]; set => fields.TitleId[this] = value; }

    [DisplayName("Contact Person"), Size(100)]
    public string ClientContactName { get => fields.ClientContactName[this]; set => fields.ClientContactName[this] = value; }

    [DisplayName("Designation"), ForeignKey(typeof(DesignationsRow)), LeftJoin(jDesignation), TextualField(nameof(Designation))]
    [ServiceLookupEditor(typeof(DesignationsRow), InplaceAdd = true, Service = "Default/Designations/List")]
    public int? DesignationId { get => fields.DesignationId[this]; set => fields.DesignationId[this] = value; }

    [DisplayName("Mobile No."), Size(18)]
    public string MobileNo { get => fields.MobileNo[this]; set => fields.MobileNo[this] = value; }

    [DisplayName("Alternate No."), Size(18)]
    public string AlternateNo { get => fields.AlternateNo[this]; set => fields.AlternateNo[this] = value; }

    [DisplayName("Email"), Size(100)]
    public string EMail { get => fields.EMail[this]; set => fields.EMail[this] = value; }

    [DisplayName("GST No."), Column("GSTIN"), Size(30)]
    public string GSTIN { get => fields.GSTIN[this]; set => fields.GSTIN[this] = value; }

    [DisplayName("Place Of Supply"), ForeignKey(typeof(StatesRow)), LeftJoin(jPlaceOfSupply), NotNull, LookupInclude]
    [ServiceLookupEditor(typeof(StatesRow), Service = "Default/States/List",
        IdField = nameof(StatesRow.StateId), TextField = nameof(StatesRow.StateNameWithStateCodeAndNo))]
    public int? PlaceOfSupplyId { get => fields.PlaceOfSupplyId[this]; set => fields.PlaceOfSupplyId[this] = value; }

    [DisplayName("Nature Of Supply"), ForeignKey(typeof(NatureOfSupplyRow)), LeftJoin(jNatureOfSupply)]
    [TextualField(nameof(NatureOfSupply)), ServiceLookupEditor(typeof(NatureOfSupplyRow), Service = "Default/NatureOfSupply/List")]
    public int? NatureOfSupplyId { get => fields.NatureOfSupplyId[this]; set => fields.NatureOfSupplyId[this] = value; }

    [DisplayName("Supply Type"), ForeignKey(typeof(SupplyTypesRow)), LeftJoin(jSupplyType), TextualField(nameof(SupplyType))]
    [ServiceLookupEditor(typeof(SupplyTypesRow), Service = "Default/SupplyTypes/List")]
    public int? SupplyTypeId { get => fields.SupplyTypeId[this]; set => fields.SupplyTypeId[this] = value; }

    [DisplayName("Company PAN"), Column("PAN"), Size(30)]
    public string PAN { get => fields.PAN[this]; set => fields.PAN[this] = value; }

    [DisplayName("Import/Export No."), Column("IECNo"), Size(30)]
    public string IECNo { get => fields.IECNo[this]; set => fields.IECNo[this] = value; }

    [DisplayName("Company CIN"), Column("CINNo"), Size(50)]
    public string CINNo { get => fields.CINNo[this]; set => fields.CINNo[this] = value; }

    [DisplayName("Company TAN"), Column("TANNo"), Size(50)]
    public string TANNo { get => fields.TANNo[this]; set => fields.TANNo[this] = value; }

    //--------- Master Child Grid -----------------
    [MasterDetailRelation(foreignKey: nameof(ClientBankAccountsRow.ClientId)), NotMapped]
    public List<ClientBankAccountsRow> ClientBankAccountsList
    {
        get { return Fields.ClientBankAccountsList[this]; }
        set { Fields.ClientBankAccountsList[this] = value; }
    }
    //-------------------------------------------------------------------------

    [DisplayName("Company Tag Line")]
    public string TagLine { get => fields.TagLine[this]; set => fields.TagLine[this] = value; }

    [DisplayName("Client DSC"), Column("ClientDSC")]
    public string ClientDSC { get => fields.ClientDSC[this]; set => fields.ClientDSC[this] = value; }

    [DisplayName("Business Type"), ForeignKey(typeof(BusinessTypesRow)), LeftJoin(jBusinessType), TextualField(nameof(BusinessType))]
    [ServiceLookupEditor(typeof(BusinessTypesRow), InplaceAdd = true, Service = "Default/BusinessTypes/List")]
    public int? BusinessTypeId { get => fields.BusinessTypeId[this]; set => fields.BusinessTypeId[this] = value; }

    [DisplayName("Business Group"), ForeignKey(typeof(BusinessGroupsRow)), LeftJoin(jBusinessGroup), TextualField(nameof(BusinessGroup))]
    [ServiceLookupEditor(typeof(BusinessGroupsRow), InplaceAdd = true, Service = "Default/BusinessGroups/List")]
    public int? BusinessGroupId { get => fields.BusinessGroupId[this]; set => fields.BusinessGroupId[this] = value; }

    [DisplayName("Business Category"), ForeignKey(typeof(BusinessCategoriesRow)), LeftJoin(jBusinessCategory)]
    [TextualField(nameof(BusinessCategory))]
    [ServiceLookupEditor(typeof(BusinessCategoriesRow), InplaceAdd = true, Service = "Default/BusinessCategories/List")]
    public int? BusinessCategoryId { get => fields.BusinessCategoryId[this]; set => fields.BusinessCategoryId[this] = value; }

    [DisplayName("Invoice Format"), Size(20)]
    public string InvoiceNoFormat { get => fields.InvoiceNoFormat[this]; set => fields.InvoiceNoFormat[this] = value; }

    [DisplayName("Disclaimer")]
    public string Disclaimer { get => fields.Disclaimer[this]; set => fields.Disclaimer[this] = value; }

    [DisplayName("Udyam Reg.No."), Size(100)]
    public string UdyamNo { get => fields.UdyamNo[this]; set => fields.UdyamNo[this] = value; }

    [DisplayName("City Name"), Origin(jCity, nameof(CitiesRow.CityName))]
    public string CityName { get => fields.CityName[this]; set => fields.CityName[this] = value; }

    [DisplayName("Title"), Origin(jTitle, nameof(TitlesRow.TitleOfRespect))]
    public string TitleOfRespect { get => fields.TitleOfRespect[this]; set => fields.TitleOfRespect[this] = value; }

    [DisplayName("Designation"), Origin(jDesignation, nameof(DesignationsRow.Designation))]
    public string Designation { get => fields.Designation[this]; set => fields.Designation[this] = value; }

    [DisplayName("Place Of Supply")]
    [Origin(jPlaceOfSupply, nameof(StatesRow.StateNameWithStateCodeAndNo)), LookupInclude]
    public string PlaceOfSupplyStateName { get => fields.PlaceOfSupplyStateName[this]; set => fields.PlaceOfSupplyStateName[this] = value; }

    [DisplayName("State Code"), Origin(jPlaceOfSupply, nameof(StatesRow.StateCode))]
    public string PlaceOfSupplyStateCode { get => fields.PlaceOfSupplyStateCode[this]; set => fields.PlaceOfSupplyStateCode[this] = value; }

    [DisplayName("State Code No."), Origin(jPlaceOfSupply, nameof(StatesRow.StateCodeNo))]
    public string PlaceOfSupplyStateCodeNo { get => fields.PlaceOfSupplyStateCodeNo[this]; set => fields.PlaceOfSupplyStateCodeNo[this] = value; }

    [DisplayName("Nature Of Supply"), Origin(jNatureOfSupply, nameof(NatureOfSupplyRow.NatureOfSupply))]
    public string NatureOfSupply { get => fields.NatureOfSupply[this]; set => fields.NatureOfSupply[this] = value; }

    [DisplayName("Supply Type"), Origin(jSupplyType, nameof(SupplyTypesRow.SupplyType))]
    public string SupplyType { get => fields.SupplyType[this]; set => fields.SupplyType[this] = value; }

    //[DisplayName("Bank Name"), Origin(jBank, nameof(BanksRow.BankName))]
    //public string BankName { get => fields.BankName[this]; set => fields.BankName[this] = value; }

    [DisplayName("Business Type"), Origin(jBusinessType, nameof(BusinessTypesRow.BusinessType))]
    public string BusinessType { get => fields.BusinessType[this]; set => fields.BusinessType[this] = value; }

    [DisplayName("Business Group"), Origin(jBusinessGroup, nameof(BusinessGroupsRow.BusinessGroup))]
    public string BusinessGroup { get => fields.BusinessGroup[this]; set => fields.BusinessGroup[this] = value; }

    [DisplayName("Business Category"), Origin(jBusinessCategory, nameof(BusinessCategoriesRow.BusinessCategory))]
    public string BusinessCategory { get => fields.BusinessCategory[this]; set => fields.BusinessCategory[this] = value; }

    [DisplayName("Consultant"), Origin(jConsultant, nameof(ConsultantsRow.ConsultantName)), LookupInclude]
    public string ConsultantName { get => fields.ConsultantName[this]; set => fields.ConsultantName[this] = value; }

    [DisplayName("Email Server Host"), Size(100)]
    public string EmailServerHost { get => fields.EmailServerHost[this]; set => fields.EmailServerHost[this] = value; }

    [DisplayName("Email Server Username"), Size(100)]
    public string EmailServerUsername { get => fields.EmailServerUsername[this]; set => fields.EmailServerUsername[this] = value; }

    [DisplayName("Email Server Password"), Size(100), PasswordEditor, NotMapped]
    public string EmailServerPassword { get => fields.EmailServerPassword[this]; set => fields.EmailServerPassword[this] = value; }

    [Column("EmailServerPasswordEncrypted"), Hidden]
    public string EmailServerPasswordEncrypted { get => fields.EmailServerPasswordEncrypted[this]; set => fields.EmailServerPasswordEncrypted[this] = value; }

    [DisplayName("Email Server Port"), DefaultValue(587)]
    public int? EmailServerPort { get => fields.EmailServerPort[this]; set => fields.EmailServerPort[this] = value; }

    
    public Int32Field ConsultantIdField => fields.ConsultantId;
}