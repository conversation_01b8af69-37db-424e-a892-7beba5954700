﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface NatureOfSupplyForm {
    NatureOfSupply: StringEditor;
    Description: StringEditor;
}

export class NatureOfSupplyForm extends PrefixedContext {
    static readonly formKey = 'Default.NatureOfSupply';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!NatureOfSupplyForm.init)  {
            NatureOfSupplyForm.init = true;

            var w0 = StringEditor;

            initFormType(NatureOfSupplyForm, [
                'NatureOfSupply', w0,
                'Description', w0
            ]);
        }
    }
}