﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { LocationsRow } from "./LocationsRow";

export namespace LocationsService {
    export const baseUrl = 'Default/Locations';

    export declare function Create(request: SaveRequest<LocationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<LocationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<LocationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<LocationsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<LocationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<LocationsRow>>;

    export const Methods = {
        Create: "Default/Locations/Create",
        Update: "Default/Locations/Update",
        Delete: "Default/Locations/Delete",
        Retrieve: "Default/Locations/Retrieve",
        List: "Default/Locations/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>LocationsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}