﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { BusinessGroupsRow } from "./BusinessGroupsRow";

export interface BusinessGroupsColumns {
    RowNumber: Column<BusinessGroupsRow>;
    BusinessGroupId: Column<BusinessGroupsRow>;
    BusinessGroup: Column<BusinessGroupsRow>;
    Description: Column<BusinessGroupsRow>;
}

export class BusinessGroupsColumns extends ColumnsBase<BusinessGroupsRow> {
    static readonly columnsKey = 'Default.BusinessGroups';
    static readonly Fields = fieldsProxy<BusinessGroupsColumns>();
}