﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface UqCsForm {
    QuantityName: StringEditor;
    QuantityType: StringEditor;
    UniqueQuantityCode: StringEditor;
    UqcName: StringEditor;
    UQCDescription: StringEditor;
}

export class UqCsForm extends PrefixedContext {
    static readonly formKey = 'Default.UqCs';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!UqCsForm.init)  {
            UqCsForm.init = true;

            var w0 = StringEditor;

            initFormType(UqCsForm, [
                'QuantityName', w0,
                'QuantityType', w0,
                'UniqueQuantityCode', w0,
                'UqcName', w0,
                'UQCDescription', w0
            ]);
        }
    }
}