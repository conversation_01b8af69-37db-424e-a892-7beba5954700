using ReconnBooks.Default;
using MyRow = ReconnBooks.Administration.UserRow;

namespace ReconnBooks.AppServices;
public class UserRetrieveService(ITwoLevelCache cache, ISqlConnections sqlConnections)
    : BaseUserRetrieveService<MyRow>(cache, sqlConnections)
{

    protected override IUserDefinition LoadByCriteria(System.Data.IDbConnection connection, BaseCriteria criteria)
    {
        var user = connection.TrySingle<MyRow>(a =>
        {
            a.SelectTableFields();
            a.SelectNonTableFields();
            a.Select();
            a.Where(criteria);
        });

        if (user != null)
        {
            var userDefinition = ToUserDefinition(user);

            if (user.ConsultantId != null)
            {
                var clientUsersRowFields = ClientUsersRow.Fields;

                var userRow = connection.TryFirst<ClientUsersRow>(
                                                clientUsersRowFields.UserId == user.UserId.GetValueOrDefault()
                                                && clientUsersRowFields.Status == 1);
                if (userRow != null)
                {
                    var clientRow = connection.TryFirst<ClientsRow>(
                                            new Criteria(ClientsRow.Fields.ClientId) == userRow.ClientId.GetValueOrDefault());

                    userDefinition.ClientId = userRow.ClientId;
                    userDefinition.ClientName = clientRow.ClientName;
                    userDefinition.ClientCode = clientRow.ClientCode;
                    userDefinition.PlaceOfSupplyStateId = clientRow.PlaceOfSupplyId;
                }
            }

            return userDefinition;
        }

        return null;
    }

    protected override UserDefinition ToUserDefinition(MyRow user)
    {
        var userDefinition = new UserDefinition
        {
            UserId = user.UserId.Value,
            Username = user.Username,
            Email = user.Email,
            MobilePhoneNumber = user.MobilePhoneNumber,
            UserImage = user.UserImage,
            DisplayName = user.DisplayName,
            IsActive = user.IsActive.Value,
            Source = user.Source,
            PasswordHash = user.PasswordHash,
            PasswordSalt = user.PasswordSalt,
            UpdateDate = user.UpdateDate,
            LastDirectoryUpdate = user.LastDirectoryUpdate,
            TwoFactorData = user.TwoFactorData,
            ClientId = user.ClientId,
            ConsultantId = user.ConsultantId,
            ClientCode = user.ClientCode,
            ClientName = user.ClientName,
            ClientLogo = user.ClientLogo,
            ConsultantName = user.ConsultantName,
            ConsultantLogo = user.ConsultantLogo,
            PlaceOfSupplyStateId = user.PlaceOfSupplyId
        };

        return userDefinition;
    }
}