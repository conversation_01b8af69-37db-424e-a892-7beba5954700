﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ProductMakeForm {
    ProductMake: StringEditor;
}

export class ProductMakeForm extends PrefixedContext {
    static readonly formKey = 'Default.ProductMake';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProductMakeForm.init)  {
            ProductMakeForm.init = true;

            var w0 = StringEditor;

            initFormType(ProductMakeForm, [
                'ProductMake', w0
            ]);
        }
    }
}