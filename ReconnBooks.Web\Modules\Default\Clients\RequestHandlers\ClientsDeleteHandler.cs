﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.ClientsRow;

namespace ReconnBooks.Default;

public interface IClientsDeleteHandler : <PERSON>eleteHandler<MyRow, MyRequest, MyResponse> {}

public class ClientsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IClientsDeleteHandler
{
    public ClientsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}