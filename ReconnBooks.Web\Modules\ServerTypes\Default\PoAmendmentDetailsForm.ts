﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";

export interface PoAmendmentDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    POQuantity: DecimalEditor;
    PendingQuantity: DecimalEditor;
    POUnitPrice: DecimalEditor;
    POUnitId: ServiceLookupEditor;
    AmendedUnitId: ServiceLookupEditor;
    POUnitAmount: DecimalEditor;
    AmendedQuantity: DecimalEditor;
    AmendedUnitPrice: DecimalEditor;
    AmendedUnitAmount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: StringEditor;
    NetPricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
}

export class PoAmendmentDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.PoAmendmentDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PoAmendmentDetailsForm.init)  {
            PoAmendmentDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;

            initFormType(PoAmendmentDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'POQuantity', w4,
                'PendingQuantity', w4,
                'POUnitPrice', w4,
                'POUnitId', w0,
                'AmendedUnitId', w0,
                'POUnitAmount', w4,
                'AmendedQuantity', w4,
                'AmendedUnitPrice', w4,
                'AmendedUnitAmount', w4,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w4,
                'NetTaxableAmount', w4,
                'IGSTRate', w4,
                'IGSTAmountPerUnit', w4,
                'NetIGSTAmount', w4,
                'CGSTRate', w4,
                'CGSTAmountPerUnit', w4,
                'NetCGSTAmount', w4,
                'SGSTRate', w4,
                'SGSTAmountPerUnit', w4,
                'NetSGSTAmount', w4,
                'DummyField', w3,
                'NetPricePerUnit', w4,
                'NetAmount', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog]); // referenced dialogs