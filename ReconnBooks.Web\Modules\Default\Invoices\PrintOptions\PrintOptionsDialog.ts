import { Decorators, EntityDialog, alertDialog } from "@serenity-is/corelib";
import { ReportHelper } from "@serenity-is/extensions";
import { PrintOptionsForm } from "../../../ServerTypes/Default/PrintOptionsForm";

@Decorators.registerClass()
export class PrintOptionsDialog extends EntityDialog<any, any> {
    protected getFormKey() { return PrintOptionsForm.formKey; }
    protected form = new PrintOptionsForm(this.idPrefix);
    
    constructor(entityId: number) {
        super();
        this.entityId = entityId;

        // Set dialog properties
        this.dialogTitle = "Print Options";
        
        // Remove save button since we don't need it
        this.toolbar.findButton('save-and-close-button')?.remove();
        this.toolbar.findButton('apply-changes-button')?.remove();
        this.toolbar.findButton('delete-button')?.remove();

        this.form.Original.value = true;
    }

    protected getToolbarButtons() {
        var buttons = super.getToolbarButtons();
        // Remove all buttons
        buttons.splice(0, buttons.length);
        
        // Add Print button
        buttons.push({
            title: 'Print Selected',
            cssClass: 'btn-primary',
            onClick: () => {
                const selectedOptions = this.getSelectedOptions();
                
                if (selectedOptions.length === 0) {
                    alertDialog("Please select at least one option to print.");
                    return;
                }
                
                // Print each selected option
                for (const option of selectedOptions) {
                    ReportHelper.execute({
                        reportKey: 'InvoiceReport',
                        params: {
                            ID: this.entityId,
                            ReportType: option.reportType
                        }
                    });
                }
                
                this.dialogClose();
            }
        });
        
        return buttons;
    }
    
    private getSelectedOptions() {
        const selectedOptions = [];
        
        if (this.form.Original.value) {
            selectedOptions.push({
                reportType: 'Original Copy'
            });
        }
        
        if (this.form.Duplicate.value) {
            selectedOptions.push({
                reportType: 'Duplicate Copy'
            });
        }
        
        if (this.form.Triplicate.value) {
            selectedOptions.push({
                reportType: 'Triplicate Copy'
            });
        }
        
        if (this.form.TransporterCopy.value) {
            selectedOptions.push({
                reportType: 'Transporter Copy'
            });
        }
        
        if (this.form.SupplierCopy.value) {
            selectedOptions.push({
                reportType: 'Supplier Copy'
            });
        }
        
        if (this.form.ExtraCopy.value) {
            selectedOptions.push({
                reportType: 'Extra Copy'
            });
        }
        
        return selectedOptions;
    }
}