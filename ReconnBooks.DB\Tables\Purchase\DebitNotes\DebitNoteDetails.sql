﻿CREATE TABLE [dbo].[DebitNoteDetails]
(
    [DebitNoteDetailId]      INT	        NOT NULL	IDENTITY (1, 1),
    [DebitNoteId]			 INT			NOT NULL,

    [PurchaseOrderDetailId]  INT                NULL,
    [PurchaseReturnDetailId] INT                NULL,

    [CommodityTypeId]       INT			    NOT NULL,
    [CommodityId]           BIGINT			NOT NULL,
	[CommodityDescription]  NVARCHAR (MAX)      NULL,

    --Fetch Invoice Item and Sales Return Items 
    [POQuantity]            DECIMAL (18, 2) NOT NULL,
    [POUnitId]              INT             NOT NULL,

    [ReturnedQuantity]		DECIMAL (18,2)	NOT NULL,   --PurchaseReturnDetails Quantity
    [ReturnedUnitId]	    INT				NOT NULL,   --PurchaseReturnDetails Unit
    
    [SerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader. Multiple Sl.Nos are possible

    [UnitPrice]             DECIMAL (18, 2) NOT NULL,   
    [DiscountPercent]       DECIMAL (18, 2)     NULL,   
    [DiscountAmountPerUnit] DECIMAL (18, 2)     NULL,
    [NetDiscountAmount]     DECIMAL (18, 2)     NULL,
        
    [GSTRateId]             INT                 NULL,
    [IGSTRate]              DECIMAL (18, 2)     NULL,      
    [CGSTRate]              DECIMAL (18, 2)     NULL,      
    [SGSTRate]              DECIMAL (18, 2)     NULL,      

    [NetPricePerUnit]       DECIMAL (18, 2)     NULL,   -- Per Unit Price = Net Amount/Quantity
    [NetAmount]             DECIMAL (18, 2)     NULL,   -- Net Amount = Net Price x Quantity

    --Fetch from SalesReturnDetails
    [RejectionReasonId]		INT	                NULL,
    [AssesmentRemarks]		NVARCHAR (MAX)	    NULL,
    [ReplacementMethodId]	INT                 NULL,  --Replace with New Product, Replace with Re-work, Regret Supply Qty, Amend Invoice/DC
    [Remarks]		        NVARCHAR (MAX)	    NULL,

	CONSTRAINT [PK_DebitNoteDetails]		PRIMARY KEY	CLUSTERED   ([DebitNoteDetailId] ASC),
    CONSTRAINT [FK_DebitNoteDetails_DebitNotes]	            FOREIGN KEY ([DebitNoteId])            REFERENCES [dbo].[DebitNotes] ([DebitNoteId]),
    CONSTRAINT [FK_DebitNoteDetails_PurchaseOrderDetails]   FOREIGN KEY ([PurchaseOrderDetailId])     REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_DebitNoteDetails_PurchaseReturnDetails]  FOREIGN KEY ([PurchaseReturnDetailId])    REFERENCES [dbo].[PurchaseReturnDetails] ([PurchaseReturnDetailId]),

    CONSTRAINT [FK_DebitNoteDetails_Commodities]	    FOREIGN KEY ([CommodityId])	        REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_DebitNoteDetails_PurchaseOrderUnits]	FOREIGN KEY ([POUnitId])            REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_DebitNoteDetails_ReturnedUnits]	    FOREIGN KEY ([ReturnedUnitId])	    REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_DebitNoteDetails_GSTRates]	        FOREIGN KEY ([GSTRateId])	        REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_DebitNoteDetails_RejectionReasons]	FOREIGN KEY ([RejectionReasonId])	REFERENCES [dbo].[RejectionReasons] ([RejectionReasonId]),
    CONSTRAINT [FK_DebitNoteDetails_ReplacementMethods] FOREIGN KEY ([ReplacementMethodId])	REFERENCES [dbo].[ReplacementMethods] ([ReplacementMethodId]),
);
GO
CREATE NONCLUSTERED INDEX [DebitNotes]
    ON [dbo].[DebitNoteDetails]([DebitNoteId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseReturnDetails]
    ON [dbo].[DebitNoteDetails]([PurchaseReturnDetailId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseOrderDetails]
    ON [dbo].[DebitNoteDetails]([PurchaseOrderDetailId] ASC);
    
GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[DebitNoteDetails]([CommodityId] ASC);