﻿import { IntegerEditor, StringEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface SecondaryGroupsForm {
    SecondaryGroupCode: IntegerEditor;
    SecondaryGroupName: StringEditor;
    PrimaryGroupId: ServiceLookupEditor;
    Remarks: StringEditor;
}

export class SecondaryGroupsForm extends PrefixedContext {
    static readonly formKey = 'Default.SecondaryGroups';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SecondaryGroupsForm.init)  {
            SecondaryGroupsForm.init = true;

            var w0 = IntegerEditor;
            var w1 = StringEditor;
            var w2 = ServiceLookupEditor;

            initFormType(SecondaryGroupsForm, [
                'SecondaryGroupCode', w0,
                'SecondaryGroupName', w1,
                'PrimaryGroupId', w2,
                'Remarks', w1
            ]);
        }
    }
}