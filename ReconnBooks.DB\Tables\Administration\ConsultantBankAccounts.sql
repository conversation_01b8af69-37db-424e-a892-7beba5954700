﻿CREATE TABLE [dbo].[ConsultantBankAccounts] 
(
    [ConsultantBankAccountId]   INT             NOT NULL    IDENTITY (1, 1),
    [ConsultantId]              INT             NOT NULL,
    [BankId]                    INT             NOT NULL,
    [BranchName]                NVARCHAR (150)      NULL,
    [AccountName]               NVARCHAR (50)       NULL,
    [AccountNumber]             NVARCHAR (50)       NULL,
    [IFSCCode]                  NVARCHAR (50)       NULL,
    [BranchCode]                NVARCHAR (50)       NULL,
    [SwiftCode]                 NVARCHAR (50)       NULL,
    [QRCode]                    NVARCHAR (500)      NULL,
    [Status]                    BIT             NOT NULL    DEFAULT ((0)),
    
    CONSTRAINT [PK_ConsultantBankAccounts] PRIMARY KEY CLUSTERED ([ConsultantBankAccountId] ASC),
    CONSTRAINT [FK_ConsultantBankAccounts_Banks]        FOREIGN KEY ([BankId]) REFERENCES [dbo].[Banks] ([BankId]),
    CONSTRAINT [FK_ConsultantBankAccounts_Consultants]  FOREIG<PERSON> KEY ([ConsultantId]) REFERENCES [dbo].[Consultants] ([ConsultantId])
);

GO
CREATE NONCLUSTERED INDEX [BankId]
    ON [dbo].[ConsultantBankAccounts]([BankId] ASC);
GO
CREATE NONCLUSTERED INDEX [ClientId]
    ON [dbo].[ConsultantBankAccounts]([ConsultantId] ASC);

