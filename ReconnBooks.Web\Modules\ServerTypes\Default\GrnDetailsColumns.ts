﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { GrnDetailsRow } from "./GrnDetailsRow";

export interface GrnDetailsColumns {
    RowNumber: Column<GrnDetailsRow>;
    CommodityName: Column<GrnDetailsRow>;
    CommodityCode: Column<GrnDetailsRow>;
    CommodityType: Column<GrnDetailsRow>;
    ReceivedQuantity: Column<GrnDetailsRow>;
    ReceivedUnitUnitName: Column<GrnDetailsRow>;
    PoQuantity: Column<GrnDetailsRow>;
    PoUnitUnitName: Column<GrnDetailsRow>;
    AcceptedQuantity: Column<GrnDetailsRow>;
    AcceptedUnitUnitName: Column<GrnDetailsRow>;
    Sku: Column<GrnDetailsRow>;
    SerialNos: Column<GrnDetailsRow>;
    SupplyDueDate: Column<GrnDetailsRow>;
    LocationName: Column<GrnDetailsRow>;
    WarehouseName: Column<GrnDetailsRow>;
    StoreName: Column<GrnDetailsRow>;
    RackNo: Column<GrnDetailsRow>;
    Remarks: Column<GrnDetailsRow>;
    CommodityDescription: Column<GrnDetailsRow>;
    PurchaseOrderDetailCommodityDescription: Column<GrnDetailsRow>;
    GRNNo: Column<GrnDetailsRow>;
    GrnDetailId: Column<GrnDetailsRow>;
}

export class GrnDetailsColumns extends ColumnsBase<GrnDetailsRow> {
    static readonly columnsKey = 'Default.GrnDetails';
    static readonly Fields = fieldsProxy<GrnDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types