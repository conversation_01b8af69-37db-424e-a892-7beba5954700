﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface TitlesForm {
    TitleOfRespect: StringEditor;
    Gender: StringEditor;
}

export class TitlesForm extends PrefixedContext {
    static readonly formKey = 'Default.Titles';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!TitlesForm.init)  {
            TitlesForm.init = true;

            var w0 = StringEditor;

            initFormType(TitlesForm, [
                'TitleOfRespect', w0,
                'Gender', w0
            ]);
        }
    }
}