﻿CREATE TABLE [dbo].[Vendors]
(
    [VendorId]              INT             NOT NULL    IDENTITY (1, 1),
    [VendorName]            NVARCHAR (100)  NOT NULL,
    [ShortName]             NVARCHAR (50)   NOT NULL,
    [AddressedToId]         INT                 NULL,
    
    [GSTIN]                 NVARCHAR (30)       NULL,
    [PlaceOfSupplyId]       INT                 NULL,
    [NatureOfSupplyId]      INT                 NULL,
    [SupplyTypeId]          INT                 NULL,
    
    [PAN]                   NVARCHAR (30)       NULL,
    [IECNo]                 NVARCHAR (50)       NULL,
    [CINNo]                 NVARCHAR (50)       NULL,
    [TAN]                   NVARCHAR (50)       NULL,
    [UdyamNo]               NVARCHAR (50)       NULL,

    [BillingAddress]        NVARCHAR (MAX)  NOT NULL,
    [BillingCityId]         INT             NOT NULL,
    [BillingPINCode]        NVARCHAR (10)       NULL,
    [CorrespondenceAddress] NVARCHAR (MAX)      NULL,
    [CorrespondenceCityId]  INT                 NULL,
    [CorrespondencePINCode] NVARCHAR (10)       NULL,

    [PhoneNo]               NVARCHAR (50)       NULL,
    [FaxNo]                 NVARCHAR (50)       NULL,
    [HomePage]              NVARCHAR (200)      NULL,
    [EMailId]               NVARCHAR (30)       NULL,

    [BankId]                INT                 NULL,
    [BranchName]            NVARCHAR (50)       NULL,
    [AccountName]           NVARCHAR (50)       NULL,
    [AccountNumber]         NVARCHAR (50)       NULL,
    [IFSCCode]              NVARCHAR (50)       NULL,
    [BranchCode]            NVARCHAR (50)       NULL,
    [UploadFiles]           NTEXT               NULL,
    [ClientId]              INT             NOT NULL    CONSTRAINT [DF_Vendors_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_Vendors] PRIMARY KEY           CLUSTERED   ([VendorId] ASC),
    CONSTRAINT [FK_Vendors_AddressedTo]           FOREIGN KEY ([AddressedToId])       REFERENCES [dbo].[AddressedTo] ([AddressedToId]),
    CONSTRAINT [FK_Vendors_PlaceOfSupply]         FOREIGN KEY ([PlaceOfSupplyId])     REFERENCES [dbo].[States] ([StateId]),
    CONSTRAINT [FK_Vendors_NatureOfSupply]        FOREIGN KEY ([NatureOfSupplyId])    REFERENCES [dbo].[NatureOfSupply] ([NatureOfSupplyId]),
    CONSTRAINT [FK_Vendors_SupplyTypes]           FOREIGN KEY ([SupplyTypeId])        REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_Vendors_BillingCities]         FOREIGN KEY ([BillingCityId])       REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Vendors_CorrespondenceCities]  FOREIGN KEY ([CorrespondenceCityId])REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Vendors_Banks]                 FOREIGN KEY ([BankId])              REFERENCES [dbo].[Banks] ([BankId]),
    CONSTRAINT [FK_Vendors_Clients]               FOREIGN KEY ([ClientId])            REFERENCES [dbo].[Clients] ([ClientId]),
);
GO
CREATE NONCLUSTERED INDEX [BillingCityId]
    ON [dbo].[Vendors]([BillingCityId] ASC);
GO
CREATE NONCLUSTERED INDEX [CompanyName]
    ON [dbo].[Vendors]([VendorName] ASC);
GO
CREATE NONCLUSTERED INDEX [BillingPINCode]
    ON [dbo].[Vendors]([BillingPINCode] ASC);
GO
CREATE NONCLUSTERED INDEX [GSTIN]
    ON [dbo].[Vendors]([GSTIN] ASC);

