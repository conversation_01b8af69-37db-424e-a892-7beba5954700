﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { LedgerAccountsRow } from "./LedgerAccountsRow";

export namespace LedgerAccountsService {
    export const baseUrl = 'Default/LedgerAccounts';

    export declare function Create(request: SaveRequest<LedgerAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<LedgerAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<LedgerAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<LedgerAccountsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<LedgerAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<LedgerAccountsRow>>;

    export const Methods = {
        Create: "Default/LedgerAccounts/Create",
        Update: "Default/LedgerAccounts/Update",
        Delete: "Default/LedgerAccounts/Delete",
        Retrieve: "Default/LedgerAccounts/Retrieve",
        List: "Default/LedgerAccounts/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>LedgerAccountsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}