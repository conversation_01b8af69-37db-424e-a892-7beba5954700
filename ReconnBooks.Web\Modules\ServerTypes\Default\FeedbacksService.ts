﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { FeedbacksRow } from "./FeedbacksRow";

export namespace FeedbacksService {
    export const baseUrl = 'Default/Feedbacks';

    export declare function Create(request: SaveRequest<FeedbacksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<FeedbacksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<FeedbacksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<FeedbacksRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<FeedbacksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<FeedbacksRow>>;

    export const Methods = {
        Create: "Default/Feedbacks/Create",
        Update: "Default/Feedbacks/Update",
        Delete: "Default/Feedbacks/Delete",
        Retrieve: "Default/Feedbacks/Retrieve",
        List: "Default/Feedbacks/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>FeedbacksService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}