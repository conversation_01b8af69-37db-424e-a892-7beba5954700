﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ProductCategoriesRow {
    RowNumber?: number;
    ProductCategoryId?: number;
    CategoryName?: string;
    Description?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class ProductCategoriesRow {
    static readonly idProperty = 'ProductCategoryId';
    static readonly nameProperty = 'CategoryName';
    static readonly localTextPrefix = 'Default.ProductCategories';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProductCategoriesRow>();
}