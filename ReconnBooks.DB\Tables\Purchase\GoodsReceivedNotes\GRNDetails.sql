﻿CREATE TABLE [dbo].[GRNDetails]
(
    [GRNDetailId]           INT             NOT NULL    IDENTITY (1, 1),
    [GRNId]                 INT             NOT NULL,
    [PurchaseOrderDetailId] INT                 NULL,   --cascade Purchase Order Items from Selected POs in GRNs form,  
    [CommodityTypeId]       INT             NOT NULL,
    
    [CommodityId]           BIGINT			    NULL,
	[CommodityDescription]  NVARCHAR (MAX)      NULL,

    [POQuantity]		    DECIMAL (18,2)	    NULL	CONSTRAINT [DF_PODetails_Quantity] DEFAULT ((1)),   --POAmended Qty from Purchase Order Details table
    [POUnitId]	            INT				    NULL,   --these two fields data not required to save in this table. still i am saving 
    [ReceivedQuantity]		DECIMAL (18,2)	NOT NULL	CONSTRAINT [DF_GRNDetails_Quantity] DEFAULT ((1)),  
    [ReceivedUnitId]	    INT				NOT NULL,
    [SKU]		            NVARCHAR (300)		NULL,
    [SerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader. Multiple Sl.Nos are possible

    [AcceptedQuantity]	    DECIMAL (18,2)	    NULL,   --This information need to be updated from Rejection Note. else value of Accepted Qty.
    [AcceptedUnitId]	    INT				    NULL,   

    [SupplyDueDate]         DATETIME            NULL,
    [LocationId]            INT                 NULL,
    [WarehouseId]           INT                 NULL,
    [StoreId]               INT                 NULL,
    [RackId]                INT                 NULL,
    [Remarks]               NVARCHAR (MAX)      NULL, 
    [ClientId]				INT				NOT NULL    CONSTRAINT [DF_GRNDetails_ClientId]	DEFAULT ((0)),

    CONSTRAINT [PK_GRNDetails] PRIMARY KEY CLUSTERED    ([GRNDetailId] ASC),
    CONSTRAINT [FK_GRNDetails_GRNs]             FOREIGN KEY ([GRNId])	                REFERENCES [dbo].[GRNs] ([GRNId]),
    CONSTRAINT [FK_GRNDetails_PODetails]        FOREIGN KEY ([PurchaseOrderDetailId])   REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_GRNDetails_CommodityTypes]   FOREIGN KEY ([CommodityTypeId])	        REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_GRNDetails_Commodities]      FOREIGN KEY ([CommodityId])		        REFERENCES [dbo].[Commodities] ([CommodityId]),

    CONSTRAINT [FK_GRNDetails_POUnits]	        FOREIGN KEY ([POUnitId])        REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_GRNDetails_ReceivedUnits]    FOREIGN KEY ([ReceivedUnitId])  REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_GRNDetails_AcceptedUnits]    FOREIGN KEY ([AcceptedUnitId])  REFERENCES [dbo].[Units] ([UnitId]),

    CONSTRAINT [FK_GRNDetails_Locations]    FOREIGN KEY ([LocationId])	REFERENCES [dbo].[Locations] ([LocationId]),
    CONSTRAINT [FK_GRNDetails_Warehouses]   FOREIGN KEY ([WarehouseId])	REFERENCES [dbo].[Warehouses] ([WarehouseId]),
    CONSTRAINT [FK_GRNDetails_Stores]       FOREIGN KEY ([StoreId])	    REFERENCES [dbo].[Stores]    ([StoreId]),
    CONSTRAINT [FK_GRNDetails_Racks]        FOREIGN KEY ([RackId])	    REFERENCES [dbo].[Racks]     ([RackId]),
);
GO
CREATE NONCLUSTERED INDEX [GRNId]
    ON [dbo].[GRNDetails]([GRNId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseOrderDetailId]
    ON [dbo].[GRNDetails]([PurchaseOrderDetailId] ASC);
GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[GRNDetails]([CommodityId] ASC);

GO
CREATE NONCLUSTERED INDEX [LocationId]
    ON [dbo].[GRNDetails]([LocationId] ASC);
GO
CREATE NONCLUSTERED INDEX [StoreId]
    ON [dbo].[GRNDetails]([StoreId] ASC);
GO
CREATE NONCLUSTERED INDEX [RackId]
    ON [dbo].[GRNDetails]([RackId] ASC);


