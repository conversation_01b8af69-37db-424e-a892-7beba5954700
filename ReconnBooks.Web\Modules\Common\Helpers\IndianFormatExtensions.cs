using System.Globalization;

namespace ReconnBooks.Modules.Common.Helpers;

public static class IndianFormatExtensions
{
    public static string IndianFormat(this int number, CultureInfo cultureInfo = null, string format = "{0:N2}")
    {
        return String.Format(cultureInfo ?? new CultureInfo("hi-IN"), format ?? "{0:#,0}", number);
    }

    public static string IndianFormat(this decimal number, CultureInfo cultureInfo = null, string format = "{0:N2}")
    {
        return String.Format(cultureInfo ?? new CultureInfo("hi-IN"), format ?? "{0:#,0}", number);
        //return number?.ToString("N2", System.Globalization.CultureInfo.CreateSpecificCulture("hi-IN")) ?? "";
    }

    public static string IndianFormat(this decimal? number, CultureInfo cultureInfo = null, string format = "{0:N2}")
    {
        return number.GetValueOrDefault().IndianFormat();
        //return number?.ToString("N2", System.Globalization.CultureInfo.CreateSpecificCulture("hi-IN")) ?? "";
    }
    public static string IndianFormatWholeNumber(this decimal number, CultureInfo cultureInfo = null, string format = "{0:#,0}")
    {
        return Math.Round(number).IndianFormat(cultureInfo, format);
    }

    public static string IndianFormatWholeNumber(this decimal? number, CultureInfo cultureInfo = null, string format = "{0:#,0}")
    {
        return Math.Round(number.GetValueOrDefault()).IndianFormat(cultureInfo, format); 
    }

    public static string IndianFormatDate(this DateTime? date)
    {
        return date?.ToString("dd-MM-yyyy") ?? string.Empty;
    }

}
