using ReconnBooks.Administration;
using Serenity;
using Serenity.Data;
using Serenity.Services;
using Microsoft.AspNetCore.DataProtection;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.ClientsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.ClientsRow;

namespace ReconnBooks.Default;

public interface IClientsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IClientsSaveHandler
{
    private readonly IDataProtector _dataProtector;

    public ClientsSaveHandler(IRequestContext context, IDataProtectionProvider dataProtectionProvider) : base(context)
    {
        _dataProtector = dataProtectionProvider.CreateProtector("EmailServerPassword");
    }

    protected override void SetInternalFields()
    {
        base.SetInternalFields();

        if (Row.IsAssigned(ClientsRow.Fields.EmailServerPassword) && 
            !string.IsNullOrEmpty(Row.EmailServerPassword))
        {
            Row.EmailServerPasswordEncrypted = _dataProtector.Protect(Row.EmailServerPassword);
        }
    }
}