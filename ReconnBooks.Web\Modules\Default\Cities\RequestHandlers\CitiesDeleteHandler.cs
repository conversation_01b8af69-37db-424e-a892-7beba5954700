﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.CitiesRow;

namespace ReconnBooks.Default;

public interface ICitiesDeleteHandler : <PERSON>eleteHandler<MyRow, MyRequest, MyResponse> {}

public class CitiesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, ICitiesDeleteHandler
{
    public CitiesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}