@model ReconnBooks.Common.DashboardPageModel
@inject ITextLocalizer Localizer
@{
    ViewData["Title"] = "Dashboard";
    ViewData["PageId"] = "Dashboard";
#if (Northwind)
    string nwLinkFormat = "~/Northwind/{0}";
#else  
    string nwLinkFormat = "~/Default/{0}";
#endif
}

@section Head {
@Html.StyleBundle("Pages/Dashboard")
}

@section ContentHeader {
<h1>@Localizer.Get("Navigation.Dashboard")</h1>
}

<div id="DashboardContent">
</div>

@Html.ModulePageInit(ESM.DashboardPage, new
{
    model = Model,
    nwLinkFormat
})
<script>
    var invoicesData = @Html.Raw(Json.Serialize(Model.InvoicesDataPoints.Select(d => new
        {
            Month = d.Month,
            InvoiceTotal = d.InvoiceTotal
        })));
    var receiptsData = @Html.Raw(Json.Serialize(Model.ReceiptsDataPoints.Select(d => new
        {
            Month = d.Month,
            ReceiptTotal = d.ReceiptTotal
        })));
</script>


