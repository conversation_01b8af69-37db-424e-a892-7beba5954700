﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ProductGroupsForm {
    ProductGroup: StringEditor;
}

export class ProductGroupsForm extends PrefixedContext {
    static readonly formKey = 'Default.ProductGroups';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProductGroupsForm.init)  {
            ProductGroupsForm.init = true;

            var w0 = StringEditor;

            initFormType(ProductGroupsForm, [
                'ProductGroup', w0
            ]);
        }
    }
}