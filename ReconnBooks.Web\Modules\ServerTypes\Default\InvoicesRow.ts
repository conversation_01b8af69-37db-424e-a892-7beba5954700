﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { TransactionStatus } from "../Modules/Common.Helpers.TransactionStatus";
import { InvoiceDetailsRow } from "./InvoiceDetailsRow";

export interface InvoicesRow {
    RowNumber?: number;
    InvoiceId?: number;
    InvoiceNo?: string;
    InvoiceDate?: string;
    InvoiceMonth?: string;
    CustomerId?: number;
    CustomerCompanyName?: string;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    CustomerEMailId?: string;
    PlaceOfSupplyStateName?: string;
    ShipToCustomerId?: number;
    ShipToCustomerName?: string;
    ShippingAddress?: string;
    ShippingCityName?: string;
    ShippingPinCode?: string;
    ShippingGSTIN?: string;
    ShippingPlaceOfSupplyStateName?: string;
    SupplyTypeId?: number;
    SupplyType?: string;
    FinancialYearId?: number;
    FinancialYearName?: string;
    SalesOrderId?: number;
    SalesOrderNo?: string;
    ProformaInvoiceId?: number;
    ProformaInvoiceNo?: string;
    OrderRefNo?: string;
    OrderRefDate?: string;
    InvoiceDetailsList?: InvoiceDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    InvoiceAmount?: number;
    RoundingOff?: number;
    GrandTotal?: number;
    PaymentTermsId?: number;
    PaymentTerms?: string;
    PaymentDueDate?: string;
    EWayBillNo?: string;
    EWayBillDate?: string;
    DeliveryNoteId?: number;
    DeliveryNoteNo?: string;
    ShippedVia?: string;
    ShippingDocketNo?: string;
    VehicleNo?: string;
    Inspection?: string;
    UploadFiles?: string;
    Remarks?: string;
    TransactionStatus?: TransactionStatus;
    IRN?: string;
    AcknowledgementDate?: string;
    AcknowledgementNo?: string;
    EInvoiceQRCode?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedByUserUsername?: string;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedByUserUsername?: string;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedByUserUsername?: string;
    AuthorizedDate?: string;
    AuthorizedStatus?: boolean;
    ModifiedByUserId?: number;
    ModifiedByUserUsername?: string;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledByUserUsername?: string;
    CancelledDate?: string;
    CancelReason?: string;
    SalesPersonId?: number;
    SalesPersonEmployeeName?: string;
}

export abstract class InvoicesRow {
    static readonly idProperty = 'InvoiceId';
    static readonly nameProperty = 'InvoiceNo';
    static readonly localTextPrefix = 'Default.Invoices';
    static readonly lookupKey = 'Default.Invoices';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<InvoicesRow>('Default.Invoices') }
    static async getLookupAsync() { return getLookupAsync<InvoicesRow>('Default.Invoices') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<InvoicesRow>();
}