using ReconnBooks.Administration;
using ReconnBooks.Default;

namespace ReconnBooks.Modules.Default.ClientUsers;
public class ClientUsersRowLookup : RowLookupScript<ClientUsersRow>
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly IPermissionService permissionService;
    public ClientUsersRowLookup(ISqlConnections sqlConnections,
        IUserAccessor userAccessor, IUserRetrieveService userRetriever,
        IPermissionService permissionService)
        : base(sqlConnections)
    {
        Expiration = TimeSpan.FromDays(-1);
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.permissionService = permissionService;
    }

    protected override void PrepareQuery(SqlQuery query)
    {
        base.PrepareQuery(query);
        AddConsultantFilter(query);
    }

    protected void AddConsultantFilter(SqlQuery query)
    {
        var userRowFields = UserRow.Fields;

        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return;
        }

        var userId = user.UserId;
        var clientId = user.ClientId;
        var consultantId = user.ConsultantId;

        if (!permissionService.HasPermission(PermissionKeys.Clients.Admin) && !permissionService.HasPermission(PermissionKeys.Clients.User))
        {
            query.Where(userRowFields.UserId == userId);
        }
        //else if (!permissionService.HasPermission(PermissionKeys.Clients.Admin) && !permissionService.HasPermission(PermissionKeys.Clients.User))
        //{
        //    query.Where(employeeRowFields.ConsultantId == consultantId.GetValueOrDefault());
        //}
    }
}
