using ReconnBooks.Administration;
using ReconnBooks.Default;
using Serenity.Services;
using System.Globalization;
using ReconnBooks.Modules.Common.Helpers;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using static ReconnBooks.Common.DashboardPageModel;
using ReconnBooks.Default.Endpoints;

namespace ReconnBooks.Common.Pages;

[Route("Dashboard/[action]")]
public class DashboardPage : Controller
{
    private CultureInfo indianCultureInfo = new CultureInfo("hi-IN");

    [PageAuthorize, HttpGet, Route("~/")]
    public ActionResult Index([FromServices] ISqlConnections sqlConnections,
        [FromServices] IUserAccessor userAccessor,
        [FromServices] IUserRetrieveService userRetriever,
        [FromServices] IPermissionService permissions)
    {
        var dashboardPageModel = new DashboardPageModel();

        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return View(MVC.Views.Common.Dashboard.DashboardIndex, dashboardPageModel);
        }

        var customerfields = CustomersRow.Fields;
        var Salesfields = InvoicesRow.Fields;
        var VendorBillsfields = VendorBillsRow.Fields;
        var Receiptsfields = ReceiptsRow.Fields;
        var VendorPaymentsfields = VendorPaymentsRow.Fields;
        var financialYearFields = FinancialYearsRow.Fields;
        var today = DateTime.Today;

        using (var dbConnection = sqlConnections.NewByKey("Default"))
        {
            var currentFinancialYearId = dbConnection.TrySingle<FinancialYearsRow>(new Criteria(financialYearFields.FromDate) <= today
                                                                                && new Criteria(financialYearFields.ToDate) >= today)
                                                                            ?.FinancialYearId;

            var allInvoicesForThisFinancialYear = dbConnection.List<InvoicesRow>(new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault());

            var invoicesDataPointsDB = allInvoicesForThisFinancialYear
                                           .OrderBy(a => a.InvoiceDate.GetValueOrDefault())
                                           .GroupBy(a => a.InvoiceDate.GetValueOrDefault().ToString("MMM"))
                                           .Select(a =>
                                           {
                                               return new DataPoint
                                               {
                                                   Month = a.Key,
                                                   InvoiceTotal = a.Sum(b => b.GrandTotal.GetValueOrDefault())
                                               };
                                           })
                                           .ToList();

            var months = new[] { "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Jan", "Feb", "Mar" };
            var invoicesDataPoints = months.Select(month => invoicesDataPointsDB.FirstOrDefault(a => a.Month.Equals(month, StringComparison.CurrentCultureIgnoreCase))
                                                  ?? new DataPoint { Month = month, InvoiceTotal = 0 }).ToList();

            dashboardPageModel.InvoicesDataPoints = invoicesDataPoints;

            var allReceiptsForThisFinancialYear = dbConnection.List<ReceiptsRow>(new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault());
            

            var receiptsDataPointsDB = allReceiptsForThisFinancialYear
                                           .OrderBy(a => a.ReceiptDate.GetValueOrDefault())
                                           .GroupBy(a => a.ReceiptDate.GetValueOrDefault().ToString("MMM"))
                                           .Select(a =>
                                           {
                                               return new DataPoint
                                               {
                                                   Month = a.Key,
                                                   ReceiptTotal = a.Sum(b => b.AmountReceived.GetValueOrDefault())
                                               };
                                           })
                                           .ToList();

            var receiptsDataPoints = months.Select(month => receiptsDataPointsDB.FirstOrDefault(a => a.Month.Equals(month, StringComparison.CurrentCultureIgnoreCase))
                                                    ?? new DataPoint { Month = month, ReceiptTotal = 0 }).ToList();

            dashboardPageModel.ReceiptsDataPoints = receiptsDataPoints;

            if (user.Username == "admin")
            {
                dashboardPageModel.SalesCount = "0";
                dashboardPageModel.VendorBillsCount = "0";
                dashboardPageModel.VendorBillsTotal = "0";
                dashboardPageModel.CustomerCount = "0";
                dashboardPageModel.SuppliersCount = "0";
                dashboardPageModel.InvoicesTotal = "0";
                dashboardPageModel.ReceiptsTotal = "0";
            }
            else
            {
                var loggedinUserClientId = user.ClientId;
                dashboardPageModel.SalesCount = dbConnection.Count<InvoicesRow>(new Criteria(Salesfields.ClientId) == loggedinUserClientId.GetValueOrDefault()
                                                                                && new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault())
                                                            .ToString();

                dashboardPageModel.VendorBillsCount = dbConnection.Count<VendorBillsRow>(new Criteria(VendorBillsfields.ClientId) == loggedinUserClientId.GetValueOrDefault()
                                                                                        && new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault())
                                                                  .ToString();

                dashboardPageModel.VendorBillsTotal = dbConnection.List<VendorBillsRow>(new Criteria(VendorBillsfields.ClientId) == loggedinUserClientId.GetValueOrDefault()
                                                                                        && new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault())
                                                                  .Sum(a => a.GrandTotal.GetValueOrDefault())
                                                                  .IndianFormatWholeNumber(cultureInfo: indianCultureInfo);

                dashboardPageModel.CustomerCount = dbConnection.Count<CustomersRow>(new Criteria(customerfields.ClientId) == loggedinUserClientId.GetValueOrDefault())
                                                                .ToString();

                dashboardPageModel.SuppliersCount = dbConnection.Count<VendorsRow>(new Criteria(customerfields.ClientId) == loggedinUserClientId.GetValueOrDefault())
                                                                .ToString();

                dashboardPageModel.InvoicesTotal = dbConnection.List<InvoicesRow>(new Criteria(Salesfields.ClientId) == loggedinUserClientId.GetValueOrDefault()
                                                                                    && new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault()
                                                                                    && new Criteria("[TransactionStatus]") != (int)TransactionStatus.Cancelled)

                                                                .Sum(a => a.GrandTotal.GetValueOrDefault())
                                                                .IndianFormatWholeNumber(cultureInfo: indianCultureInfo);

                dashboardPageModel.ReceiptsTotal = dbConnection.List<ReceiptsRow>(new Criteria(Receiptsfields.ClientId) == loggedinUserClientId.GetValueOrDefault()
                                                                                    && new Criteria("[FinancialYearId]") == currentFinancialYearId.GetValueOrDefault())
                                                                .Sum(a => a.AmountReceived.GetValueOrDefault())
                                                                .IndianFormatWholeNumber(cultureInfo: indianCultureInfo);
            }
        }

        return View(MVC.Views.Common.Dashboard.DashboardIndex, dashboardPageModel);
    }
}
