﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { OpeningBalancesRow } from "./OpeningBalancesRow";

export interface OpeningBalancesColumns {
    RowNumber: Column<OpeningBalancesRow>;
    FromDate: Column<OpeningBalancesRow>;
    ToDate: Column<OpeningBalancesRow>;
    Ammount: Column<OpeningBalancesRow>;
    LedgerAccountId: Column<OpeningBalancesRow>;
    FinancialYearName: Column<OpeningBalancesRow>;
    Remarks: Column<OpeningBalancesRow>;
    OpeningBalanceId: Column<OpeningBalancesRow>;
}

export class OpeningBalancesColumns extends ColumnsBase<OpeningBalancesRow> {
    static readonly columnsKey = 'Default.OpeningBalances';
    static readonly Fields = fieldsProxy<OpeningBalancesColumns>();
}