﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { BusinessGroupsRow } from "./BusinessGroupsRow";

export namespace BusinessGroupsService {
    export const baseUrl = 'Default/BusinessGroups';

    export declare function Create(request: SaveRequest<BusinessGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<BusinessGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<BusinessGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<BusinessGroupsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<BusinessGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<BusinessGroupsRow>>;

    export const Methods = {
        Create: "Default/BusinessGroups/Create",
        Update: "Default/BusinessGroups/Update",
        Delete: "Default/BusinessGroups/Delete",
        Retrieve: "Default/BusinessGroups/Retrieve",
        List: "Default/BusinessGroups/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>BusinessGroupsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}