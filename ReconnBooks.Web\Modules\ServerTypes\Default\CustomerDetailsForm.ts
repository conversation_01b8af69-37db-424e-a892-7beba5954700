﻿import { ServiceLookupEditor, DateEditor, StringEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { EmployeesDialog } from "../../Default/Employees/EmployeesDialog";

export interface CustomerDetailsForm {
    LastContactedByEmployeeId: ServiceLookupEditor;
    LastContactDate: DateEditor;
    Email: StringEditor;
    SendBulletin: BooleanEditor;
}

export class CustomerDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.CustomerDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CustomerDetailsForm.init)  {
            CustomerDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = DateEditor;
            var w2 = StringEditor;
            var w3 = BooleanEditor;

            initFormType(CustomerDetailsForm, [
                'LastContactedByEmployeeId', w0,
                'LastContactDate', w1,
                'Email', w2,
                'SendBulletin', w3
            ]);
        }
    }
}

queueMicrotask(() => [EmployeesDialog]); // referenced dialogs