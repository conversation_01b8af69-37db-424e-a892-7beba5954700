﻿CREATE TABLE [dbo].[InvoiceDetails] (
    [InvoiceDetailId]       INT             IDENTITY (1, 1) NOT NULL,
    [InvoiceId]             INT             NOT NULL,
    [CommodityTypeId]       INT             NOT NULL,
    [CommodityId]           BIGINT          NOT NULL,
    [CommodityDescription]  NVARCHAR (MAX)  NULL,
    [Quantity]              DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_Quantity] DEFAULT ((1)) NOT NULL,
    
    [UnitId]                INT             NOT NULL,
    [RevisedQuantity]       DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_RevQuantity] DEFAULT ((1)) NULL,
    [DummyField1]           NVARCHAR (10)   NULL,
    [UnitPrice]             DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_UnitPrice] DEFAULT ((0)) NOT NULL,
    
    [DiscountPercent]       DECIMAL (18, 2) DEFAULT ((0)) NULL,
    [DiscountAmountPerUnit] DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_Disc.AmtPerUnit] DEFAULT ((0)) NULL,
    [NetDiscountAmount]     DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_NetDiscountAmt] DEFAULT ((0)) NULL,
    
    [GSTRateId]             INT             NOT NULL,
    [IGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_IGSTRate] DEFAULT ((0)) NULL,
    [CGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_CGSTRate] DEFAULT ((0)) NULL,
    [SGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_InvoiceDetails_SGSTRate] DEFAULT ((0)) NULL,
    
    [DummyField]            NVARCHAR (200)  NULL,
    [NetPricePerUnit]       DECIMAL (18, 2) NULL,
    [NetAmount]             DECIMAL (18, 2) NULL,

    CONSTRAINT [PK_InvoiceDetails] PRIMARY KEY CLUSTERED ([InvoiceDetailId] ASC),
    CONSTRAINT [FK_InvoiceDetails_InvoiceId] FOREIGN KEY ([InvoiceId]) REFERENCES [dbo].[Invoices] ([InvoiceId]),
    CONSTRAINT [FK_InvoiceDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_InvoiceDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_InvoiceDetails_CommodityId] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_InvoiceDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [Invoices]
    ON [dbo].[InvoiceDetails]([InvoiceId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[InvoiceDetails]([CommodityId] ASC);

