﻿import { IntegerEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ReceiptCancelForm {
    ReceiptId: IntegerEditor;
    CancelReason: TextAreaEditor;
}

export class ReceiptCancelForm extends PrefixedContext {
    static readonly formKey = 'Main.ReceiptCancel';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ReceiptCancelForm.init)  {
            ReceiptCancelForm.init = true;

            var w0 = IntegerEditor;
            var w1 = TextAreaEditor;

            initFormType(ReceiptCancelForm, [
                'ReceiptId', w0,
                'CancelReason', w1
            ]);
        }
    }
}