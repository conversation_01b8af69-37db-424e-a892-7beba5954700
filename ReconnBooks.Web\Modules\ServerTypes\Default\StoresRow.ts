﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface StoresRow {
    RowNumber?: number;
    StoreId?: number;
    StoreName?: string;
    StoreDescription?: string;
    Remarks?: string;
    LocationId?: number;
    WarehouseId?: number;
    SetDefault?: boolean;
    Discontinued?: boolean;
    LocationName?: string;
    WarehouseName?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class StoresRow {
    static readonly idProperty = 'StoreId';
    static readonly nameProperty = 'StoreName';
    static readonly localTextPrefix = 'Default.Stores';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<StoresRow>();
}