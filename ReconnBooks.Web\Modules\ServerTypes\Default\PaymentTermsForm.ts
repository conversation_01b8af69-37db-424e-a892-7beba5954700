﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface PaymentTermsForm {
    PaymentTerms: StringEditor;
}

export class PaymentTermsForm extends PrefixedContext {
    static readonly formKey = 'Default.PaymentTerms';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PaymentTermsForm.init)  {
            PaymentTermsForm.init = true;

            var w0 = StringEditor;

            initFormType(PaymentTermsForm, [
                'PaymentTerms', w0
            ]);
        }
    }
}