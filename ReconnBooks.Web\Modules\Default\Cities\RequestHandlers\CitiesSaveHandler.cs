﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.CitiesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.CitiesRow;

namespace ReconnBooks.Default;

public interface ICitiesSaveHandler : ISave<PERSON>andler<MyRow, MyRequest, MyResponse> {}

public class CitiesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, ICitiesSaveHandler
{
    public CitiesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}