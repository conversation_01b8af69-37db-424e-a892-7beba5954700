﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ProductCategoriesForm {
    CategoryName: StringEditor;
    Description: StringEditor;
}

export class ProductCategoriesForm extends PrefixedContext {
    static readonly formKey = 'Default.ProductCategories';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProductCategoriesForm.init)  {
            ProductCategoriesForm.init = true;

            var w0 = StringEditor;

            initFormType(ProductCategoriesForm, [
                'CategoryName', w0,
                'Description', w0
            ]);
        }
    }
}