﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface CountriesRow {
    RowNumber?: number;
    CountryId?: number;
    CountryName?: string;
}

export abstract class CountriesRow {
    static readonly idProperty = 'CountryId';
    static readonly nameProperty = 'CountryName';
    static readonly localTextPrefix = 'Default.Countries';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CountriesRow>();
}