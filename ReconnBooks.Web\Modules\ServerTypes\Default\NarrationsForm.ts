﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface NarrationsForm {
    NarrationText: StringEditor;
    Remarks: StringEditor;
}

export class NarrationsForm extends PrefixedContext {
    static readonly formKey = 'Default.Narrations';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!NarrationsForm.init)  {
            NarrationsForm.init = true;

            var w0 = StringEditor;

            initFormType(NarrationsForm, [
                'NarrationText', w0,
                'Remarks', w0
            ]);
        }
    }
}