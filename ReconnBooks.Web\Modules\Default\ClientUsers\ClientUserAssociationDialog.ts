import { Decorators, EntityDialog, ToolButton } from "@serenity-is/corelib";
import { ClientUserAssociationForm } from "../../ServerTypes/Modules";

@Decorators.registerClass()
@Decorators.responsive()
export class ClientUserAssociationDialog extends EntityDialog<any, any> {
    protected getFormKey() { return ClientUserAssociationForm.formKey; }

    protected form = new ClientUserAssociationForm(this.idPrefix);
    constructor() {
        super();
    }

    protected getToolbarButtons(): ToolButton[] {
        let buttons = super.getToolbarButtons();

        buttons.length = 0;

        return buttons;
    }

    protected getDialogOptions() {
        var opt = super.getDialogOptions();
        opt.title = 'Change Client';// To have custom name for dialog
        return opt;
    }
}
