namespace ReconnBooks.Administration;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Administration"), <PERSON><PERSON><PERSON>("RolePermissions")]
[Disp<PERSON><PERSON><PERSON>("Role Permissions"), InstanceName("Role Permission"), GenerateFields]
[ReadPermission(PermissionKeys.Security)]
[ModifyPermission(PermissionKeys.Security)]
public sealed partial class RolePermissionRow : Row<RolePermissionRow.RowFields>, IRolePermissionRow
{
    [Display<PERSON><PERSON>("Role Permission Id"), Identity, IdProperty]
    public long? RolePermissionId { get => fields.RolePermissionId[this]; set => fields.RolePermissionId[this] = value; }

    [Di<PERSON><PERSON><PERSON><PERSON>("Role Id"), NotN<PERSON>, <PERSON><PERSON><PERSON>("Roles", "RoleId"), LeftJoin("jRole")]
    public int? RoleId { get => fields.RoleId[this]; set => fields.RoleId[this] = value; }

    [Di<PERSON><PERSON><PERSON><PERSON>("Permission Key"), <PERSON><PERSON>(100), <PERSON><PERSON><PERSON>, <PERSON><PERSON>earch, <PERSON><PERSON><PERSON><PERSON>]
    public string PermissionK<PERSON> { get => fields.PermissionKey[this]; set => fields.PermissionKey[this] = value; }

    [DisplayN<PERSON>("Role"), Expression("COALESCE(jRole.[RoleKey], jRole.[RoleName])")]
    public string RoleKeyOrName { get => fields.RoleKeyOrName[this]; set => fields.RoleKeyOrName[this] = value; }

    public StringField RoleKeyOrNameField => fields.RoleKeyOrName;
    public StringField PermissionKeyField => fields.PermissionKey;
}