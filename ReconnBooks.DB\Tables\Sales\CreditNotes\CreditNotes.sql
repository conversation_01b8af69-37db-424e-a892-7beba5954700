﻿CREATE TABLE [dbo].[CreditNotes]
(
    [CreditNoteId]			INT				NOT NULL	IDENTITY (1, 1),
    [CreditNoteNo]			NVARCHAR (50)	NOT NULL,
    [CreditNoteDate]		DATETIME			NULL,
    [CustomerId]			INT				NOT NULL,
    
    [FinancialYearId]		INT					NULL,
    [InvoiceId]				INT					NULL,
    [SalesR<PERSON>urnId]		    INT					NULL,
	[CreditNoteAmount]		DECIMAL(18, 2)		NULL,
    
    [Remarks]				NVARCHAR (MAX)		NULL,
    
-------------------Authorization Details-------------
    [ClientId]              INT	        NOT NULL    CONSTRAINT [DF_CreditNotes_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	            NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]			DATETIME        NULL,
    [AuthorizedStatus]      BIT         NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details----------------------
    CONSTRAINT [FK_CreditNotes_PreparedByUsers]     FOREIGN KEY ([PreparedByUserId])    REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_CreditNotes_VerfiedByUsers]      FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_CreditNotes_AuthorizedByUsers]   FOREIGN KEY ([AuthorizedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_CreditNotes_ModifiedByUsers]     FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_CreditNotes_CancelledByUsers]    FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_CreditNotes_Clients]	            FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients] ([ClientId]),
    -------------------Authorization Details End------------------

    CONSTRAINT [PK_CreditNotes]			PRIMARY KEY	CLUSTERED	([CreditNoteId] ASC),

    CONSTRAINT [FK_CreditNotes_Customers]		FOREIGN KEY		([CustomerId])			REFERENCES [dbo].[Customers]	([CustomerId]),
    CONSTRAINT [FK_CreditNotes_FinancialYears]	FOREIGN KEY		([FinancialYearId])		REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_CreditNotes_Invoices]		FOREIGN KEY		([InvoiceId])			REFERENCES [dbo].[Invoices]		([InvoiceId]),
    CONSTRAINT [FK_CreditNotes_SalesReturns]    FOREIGN KEY		([SalesReturnId])		REFERENCES [dbo].[SalesReturns] ([SalesReturnId]),
);
GO
CREATE NONCLUSTERED INDEX [CustomerId]
    ON [dbo].[CreditNotes]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [InvoiceId]
    ON [dbo].[CreditNotes]([InvoiceId] ASC);

GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[CreditNotes]([FinancialYearId] ASC);

GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[CreditNotes]([ClientId] ASC);

