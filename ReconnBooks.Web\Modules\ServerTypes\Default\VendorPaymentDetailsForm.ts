﻿import { ServiceLookupEditor, IntegerEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";

export interface VendorPaymentDetailsForm {
    PurchaseOrderId: ServiceLookupEditor;
    VendorBillId: IntegerEditor;
    VendorBillDetailId: IntegerEditor;
    AmountPaid: DecimalEditor;
}

export class VendorPaymentDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.VendorPaymentDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!VendorPaymentDetailsForm.init)  {
            VendorPaymentDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = IntegerEditor;
            var w2 = DecimalEditor;

            initFormType(VendorPaymentDetailsForm, [
                'PurchaseOrderId', w0,
                'VendorBillId', w1,
                'VendorBillDetailId', w1,
                'AmountPaid', w2
            ]);
        }
    }
}

queueMicrotask(() => [PurchaseOrdersDialog]); // referenced dialogs