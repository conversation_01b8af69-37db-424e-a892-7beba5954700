﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface ProformaInvoiceDetailsRow {
    RowNumber?: number;
    ProformaInvoiceDetailId?: number;
    ProformaInvoiceId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    Quantity?: number;
    UnitId?: number;
    UnitPrice?: number;
    UnitAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
    ProformaInvoiceNo?: string;
    UnitName?: string;
    GSTRateRemarks?: string;
}

export abstract class ProformaInvoiceDetailsRow {
    static readonly idProperty = 'ProformaInvoiceDetailId';
    static readonly nameProperty = 'ProformaInvoiceDetailId';
    static readonly localTextPrefix = 'Default.ProformaInvoiceDetails';
    static readonly lookupKey = 'Default.ProformaInvoiceDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ProformaInvoiceDetailsRow>('Default.ProformaInvoiceDetails') }
    static async getLookupAsync() { return getLookupAsync<ProformaInvoiceDetailsRow>('Default.ProformaInvoiceDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProformaInvoiceDetailsRow>();
}