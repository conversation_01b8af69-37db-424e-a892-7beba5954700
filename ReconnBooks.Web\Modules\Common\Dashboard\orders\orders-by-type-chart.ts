import { Chart } from "../shared/chartjs-init";

export const ordersByTypeChart = (canvas: HTMLCanvasElement): Chart<"doughnut"> => {
    // Parse data from the injected script
    const invoicesData = (window as any).invoicesData;
    const receiptsData = (window as any).receiptsData;

    // Calculate total sum of invoices and receipts
    const totalInvoices = invoicesData.reduce((sum: number, d: any) => sum + d.InvoiceTotal, 0);
    const totalReceipts = receiptsData.reduce((sum: number, g: any) => sum + g.ReceiptTotal, 0);

    return new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: ["Total Invoices", "Total Receipts"],
            datasets: [{
                label: 'Sales by Type',
                data: [totalInvoices, totalReceipts],
                backgroundColor: ['#4dc9f6', '#f67019'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });
};
