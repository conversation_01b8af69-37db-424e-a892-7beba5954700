using MimeKit;
using Serenity.Reporting;

namespace ReconnBooks.Modules.Common.Helpers.EmailHelper
{
    public class ReportEmailHelper
    {
        private readonly IReportFactory _reportFactory;
        private readonly IReportRenderer _reportRenderer;
        private readonly IEmailSender _emailSender;
        private readonly IUserAccessor _userAccessor;
        private readonly IUserRetrieveService _userRetriever;

        public ReportEmailHelper(
            IReportFactory reportFactory,
            IReportRenderer reportRenderer,
            IEmailSender emailSender,
            IUserAccessor userAccessor,
            IUserRetrieveService userRetriever)
        {
            _reportFactory = reportFactory;
            _reportRenderer = reportRenderer;
            _emailSender = emailSender;
            _userAccessor = userAccessor;
            _userRetriever = userRetriever;
        }

        public void SendReportEmail(string reportKey,
            int entityId,
            string customerEmail,
            string documentNo,
            string emailSubject,
            string emailBody)
        {
            if (string.IsNullOrEmpty(customerEmail))
                throw new ValidationError("Customer email address is required");

            var userDefinition = _userAccessor.User?.GetUserDefinition(_userRetriever);
            if (userDefinition == null)
                throw new ValidationError("User not found!");

            if (string.IsNullOrEmpty(userDefinition.Email))
                throw new ValidationError("Your email address is not configured. Please update your user profile with a valid email address.");

            string jsonEntityId = JSON.Stringify(new { ID = entityId });

            var report = _reportFactory.Create(reportKey, jsonEntityId, validatePermission: true);
            var result = _reportRenderer.Render(report, new ReportRenderOptions
            {
                ExportFormat = "pdf",
                PreviewMode = false,
                ReportKey = reportKey,
                ReportParams = jsonEntityId,
            });

            var message = new MimeMessage();
            message.From.Add(MailboxAddress.Parse(userDefinition.Email));
            message.To.Add(MailboxAddress.Parse(customerEmail));
            message.Subject = emailSubject;
            //$"{reportKey.Replace("Report", "")} {documentNo}";

            var bodyBuilder = new BodyBuilder
            {
                HtmlBody = emailBody

            };

            bodyBuilder.Attachments.Add(documentNo + ".pdf", result.ContentBytes, new ContentType("application", "pdf"));
            message.Body = bodyBuilder.ToMessageBody();

            _emailSender.Send(message);
        }
    }
}
