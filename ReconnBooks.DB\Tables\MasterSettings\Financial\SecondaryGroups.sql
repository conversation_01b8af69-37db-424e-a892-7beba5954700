﻿CREATE TABLE [dbo].[SecondaryGroups] (
    [SecondaryGroupId]   INT            IDENTITY (1, 1) NOT NULL,
    [SecondaryGroupCode] INT            NOT NULL,
    [SecondaryGroupName] NVARCHAR (500) NOT NULL,
    [PrimaryGroupId]     INT            NOT NULL,
    [Remarks]            NVARCHAR (500) NULL,
    CONSTRAINT [PK_SecondaryGroups] PRIMARY KEY CLUSTERED ([SecondaryGroupId] ASC),
    CONSTRAINT [FK_SecondaryGroups_PrimaryGroups] FOREIGN KEY ([PrimaryGroupId]) REFERENCES [dbo].[PrimaryGroups] ([PrimaryGroupId])
);


GO
CREATE NONCLUSTERED INDEX [PrimaryGroups]
    ON [dbo].[SecondaryGroups]([PrimaryGroupId] ASC);

