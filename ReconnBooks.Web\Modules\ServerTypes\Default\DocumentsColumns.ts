﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { DocumentsRow } from "./DocumentsRow";

export interface DocumentsColumns {
    RowNumber: Column<DocumentsRow>;
    DocumentId: Column<DocumentsRow>;
    DocumentName: Column<DocumentsRow>;
    DocumentShortName: Column<DocumentsRow>;
}

export class DocumentsColumns extends ColumnsBase<DocumentsRow> {
    static readonly columnsKey = 'Default.Documents';
    static readonly Fields = fieldsProxy<DocumentsColumns>();
}