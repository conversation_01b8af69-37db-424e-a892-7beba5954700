import { CitiesForm, CitiesRow, CitiesService, DistrictsRow, StatesRow } from '@/ServerTypes/Default';
import { Decorators, toId } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.CitiesDialog')

export class CitiesDialog extends PendingChangesConfirmDialog<CitiesRow> {
    protected getFormKey() { return CitiesForm.formKey; }
    protected getRowDefinition() { return CitiesRow; }
    protected getService() { return CitiesService.baseUrl; }

    protected form = new CitiesForm(this.idPrefix);

    constructor() {
        super();

        this.form.DistrictId.changeSelect2(async a => {
            let districtId = toId(this.form.DistrictId.value);
            if (districtId != null) {
                let district = (await DistrictsRow.getLookupAsync()).itemById[districtId];
                this.form.StateId.value = district.StateId.toString();
            }
            else {
                this.form.StateId.value = null;
            }
        })
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}