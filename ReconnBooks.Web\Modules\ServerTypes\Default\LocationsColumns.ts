﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { LocationsRow } from "./LocationsRow";

export interface LocationsColumns {
    RowNumber: Column<LocationsRow>;
    LocationName: Column<LocationsRow>;
    Description: Column<LocationsRow>;
    Remarks: Column<LocationsRow>;
    Discontinued: Column<LocationsRow>;
    ClientId: Column<LocationsRow>;
    LocationId: Column<LocationsRow>;
}

export class LocationsColumns extends ColumnsBase<LocationsRow> {
    static readonly columnsKey = 'Default.Locations';
    static readonly Fields = fieldsProxy<LocationsColumns>();
}