﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { InvoiceDetailsRow } from "./InvoiceDetailsRow";

export namespace InvoiceDetailsService {
    export const baseUrl = 'Default/InvoiceDetails';

    export declare function Create(request: SaveRequest<InvoiceDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<InvoiceDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<InvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<InvoiceDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<InvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<InvoiceDetailsRow>>;

    export const Methods = {
        Create: "Default/InvoiceDetails/Create",
        Update: "Default/InvoiceDetails/Update",
        Delete: "Default/InvoiceDetails/Delete",
        Retrieve: "Default/InvoiceDetails/Retrieve",
        List: "Default/InvoiceDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>InvoiceDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}