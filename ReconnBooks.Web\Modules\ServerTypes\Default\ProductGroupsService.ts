﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ProductGroupsRow } from "./ProductGroupsRow";

export namespace ProductGroupsService {
    export const baseUrl = 'Default/ProductGroups';

    export declare function Create(request: SaveRequest<ProductGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProductGroupsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProductGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProductGroupsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProductGroupsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProductGroupsRow>>;

    export const Methods = {
        Create: "Default/ProductGroups/Create",
        Update: "Default/ProductGroups/Update",
        Delete: "Default/ProductGroups/Delete",
        Retrieve: "Default/ProductGroups/Retrieve",
        List: "Default/ProductGroups/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ProductGroupsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}