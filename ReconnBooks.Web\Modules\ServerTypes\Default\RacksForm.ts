﻿import { StringEditor, TextAreaEditor, ServiceLookupEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface RacksForm {
    RackNo: StringEditor;
    CompartmentNo: StringEditor;
    BinNo: StringEditor;
    RackDescription: TextAreaEditor;
    Remarks: StringEditor;
    StoreId: ServiceLookupEditor;
    Discontinued: BooleanEditor;
}

export class RacksForm extends PrefixedContext {
    static readonly formKey = 'Default.Racks';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!RacksForm.init)  {
            RacksForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;
            var w2 = ServiceLookupEditor;
            var w3 = BooleanEditor;

            initFormType(RacksForm, [
                'RackNo', w0,
                'CompartmentNo', w0,
                'BinNo', w0,
                'RackDescription', w1,
                'Remarks', w0,
                'StoreId', w2,
                'Discontinued', w3
            ]);
        }
    }
}