import { AddressedToColumns, AddressedToRow, AddressedToService } from '@/ServerTypes/Default';
import { Decorators, gridPageInit } from '@serenity-is/corelib';
import { AddressedToDialog } from './AddressedToDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.AddressedToGrid')
export class AddressedToGrid extends EntityGridDialog<AddressedToRow, any> {
    protected getColumnsKey() { return AddressedToColumns.columnsKey; }
    protected getDialogType() { return AddressedToDialog; }
    protected getRowDefinition() { return AddressedToRow; }
    protected getService() { return AddressedToService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}