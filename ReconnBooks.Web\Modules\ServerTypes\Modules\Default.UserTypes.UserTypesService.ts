﻿import { SaveRequest, SaveR<PERSON>ponse, ServiceOptions, DeleteRequest, DeleteR<PERSON>ponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { UserTypesRow } from "../Default/UserTypesRow";

export namespace UserTypesService {
    export const baseUrl = 'Default/UserTypes';

    export declare function Create(request: SaveRequest<UserTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<UserTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<UserTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<UserTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<UserTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<UserTypesRow>>;

    export const Methods = {
        Create: "Default/UserTypes/Create",
        Update: "Default/UserTypes/Update",
        Delete: "Default/UserTypes/Delete",
        Retrieve: "Default/UserTypes/Retrieve",
        List: "Default/UserTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>UserTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}