﻿import { TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface HeaderNoteForm {
    HeaderNote: TextAreaEditor;
    Remarks: TextAreaEditor;
}

export class HeaderNoteForm extends PrefixedContext {
    static readonly formKey = 'Default.HeaderNote';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!HeaderNoteForm.init)  {
            HeaderNoteForm.init = true;

            var w0 = TextAreaEditor;

            initFormType(HeaderNoteForm, [
                'HeaderNote', w0,
                'Remarks', w0
            ]);
        }
    }
}