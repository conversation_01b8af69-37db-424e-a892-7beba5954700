﻿CREATE TABLE [dbo].[VendorBills]
(
    [Vendor<PERSON>illId]       INT             IDENTITY (1, 1) NOT NULL,
    [VendorBillNo]       NVARCHAR (50)   NOT NULL,
    [VendorBillDate]     DATETIME        NULL,
    
    [VendorId]           INT             NOT NULL,
    [SupplyTypeId]       INT             NULL,
    [FinancialYearId]    INT             NULL,
    [PurchaseOrderId]    INT             NULL,
    
    [SupplyDueDate]      DATETIME        NULL,
    [DeliveryDate]       DATETIME        NULL,
    [VendorBillUpload]   NVARCHAR (MAX)  NULL,
    
    [ShippingThru]       NVARCHAR (200)  NULL,
    [ShippingDocketNo]   NVARCHAR (50)   NULL,
    [PaymentTermsId]     INT             NULL,
    [PaymentDueDate]     DATETIME        NULL,
    
    [VendorBillAmount]   DECIMAL (18, 2) NULL,
    [TDSPercent]         DECIMAL (18, 2) NULL,
    [TDSAmount]          DECIMAL (18, 2) NULL,
    [TCSPercent]         DECIMAL (18, 2) NULL,
    [TCSAmount]          DECIMAL (18, 2) NULL,
    
    [RoundingOff]        DECIMAL (18, 2) NULL,
    [GrandTotal]         DECIMAL (18, 2) NULL,
    [Remarks]            NVARCHAR (MAX)  NULL,
    [VendorBillStatus]   BIT             NULL,
    
    [ClientId]           INT             CONSTRAINT [DF_VendorBills_ClientId] DEFAULT ((0)) NOT NULL,
    [PreparedByUserId]   INT             NULL,
    [PreparedDate]       DATETIME        NULL,
    [VerifiedByUserId]   INT             NULL,
    [VerifiedDate]       DATETIME        NULL,
    [AuthorizedByUserId] INT             NULL,
    [AuthorizedDate]     DATETIME        NULL,
    [ModifiedByUserId]   INT             NULL,
    [ModifiedDate]       DATETIME        NULL,
    [CancelledByUserId]  INT             NULL,
    [CancelledDate]      DATETIME        NULL,
    [AuthorizedStatus]   BIT             DEFAULT ((0)) NOT NULL,
    
    CONSTRAINT [PK_VendorBills] PRIMARY KEY CLUSTERED ([VendorBillId] ASC),
    CONSTRAINT [FK_VendorBills_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_VendorBills_PurchaseOrder] FOREIGN KEY ([PurchaseOrderId]) REFERENCES [dbo].[PurchaseOrders] ([PurchaseOrderId]),
    CONSTRAINT [FK_VendorBills_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorBills_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorBills_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorBills_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorBills_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorBills_Vendors] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendors] ([VendorId]),
    CONSTRAINT [FK_VendorBills_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_VendorBills_PaymentTerms] FOREIGN KEY ([PaymentTermsId]) REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
    CONSTRAINT [FK_VendorBills_SupplyTypes] FOREIGN KEY ([SupplyTypeId]) REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId])
);


GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[VendorBills]([VendorId] ASC);


GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[VendorBills]([PurchaseOrderId] ASC);


GO
CREATE NONCLUSTERED INDEX [VendorBills]
    ON [dbo].[VendorBills]([VendorBillId] ASC);


GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[VendorBills]([ClientId] ASC);


GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[VendorBills]([FinancialYearId] ASC);

