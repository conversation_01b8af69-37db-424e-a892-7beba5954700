﻿CREATE TABLE [dbo].[Quotations]
(
    [QuotationId]           INT                NOT NULL    IDENTITY (1, 1),
    [QuotationNo]           NVARCHAR (50)      NOT NULL,
    [QuotationDate]         SMALLDATETIME          NULL,
    [DocumentId]            INT                    NULL,

    [CustomerId]            INT                NOT NULL,
    [ReferenceNo]           NVARCHAR (150)         NULL,
    [ReferenceDate]         SMALLDATETIME          NULL,
    
    [HeaderNoteId]          INT                    NULL,
    [GrandTotal]            DECIMAL (18, 2)        NULL,
    [PaymentTermsId]        INT                    NULL,
    [Taxes]                 NVARCHAR (250)         NULL,
    [DeliveryPeriod]        NVARCHAR (250)         NULL,
    [Warranty]              NVARCHAR (250)         NULL,
    [Validity]              NVARCHAR (250)         NULL,
    [FootNoteId]            INT                    NULL,
    [Remarks]               NVARCHAR (MAX)         NULL,

    [SalesPerson]           INT                    NULL,        --EmployeeId from Employees table
    [FinancialYearId]       INT                    NULL,
    [ClientId]              INT                NOT NULL    CONSTRAINT [DF_Quotation_ClientId] DEFAULT ((0)),
    
    [PreparedByUserId]      INT                    NULL,
    [PreparedDate]          SMALLDATETIME          NULL,
    [VerifiedByUserId]      INT                    NULL,
    [VerifiedDate]          SMALLDATETIME          NULL,
    [AuthorizedByUserId]    INT                    NULL,
    [AuthorizedDate]        SMALLDATETIME          NULL,
    [ModifiedByUserId]      INT                    NULL,
    [ModifiedDate]          SMALLDATETIME          NULL,
    [CancelledByUserId]     INT                    NULL,
    [CancelledDate]         SMALLDATETIME          NULL,
    [AuthorizedStatus]      BIT                NOT NULL    DEFAULT ((0)),
    [QuotationStatus]       BIT                NOT NULL    DEFAULT ((0)), 
    [QuotationAmount]       DECIMAL (18, 2)        NULL,
    [RoundingOff]           DECIMAL (18, 2)        NULL,
    [PaymentDueDate]        DATETIME               NULL,

    CONSTRAINT [PK_Quotations] PRIMARY KEY          CLUSTERED   ([QuotationId] ASC),
    CONSTRAINT [FK_Quotations_PreparedByUsers]      FOREIGN KEY ([PreparedByUserId])    REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Quotations_VerifiedByUsers]      FOREIGN KEY ([VerifiedByUserId])    REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Quotations_AuthorizedByUsers]    FOREIGN KEY ([AuthorizedByUserId])  REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Quotations_ModifiedByUsers]      FOREIGN KEY ([ModifiedByUserId])    REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Quotations_CancelledByUsers]     FOREIGN KEY ([CancelledByUserId])   REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Quotations_Clients]              FOREIGN KEY ([ClientId])            REFERENCES [dbo].[Clients] ([ClientId]),
    
    CONSTRAINT [FK_Quotations_Customers]            FOREIGN KEY ([CustomerId])          REFERENCES [dbo].[Customers] ([CustomerId]),
    CONSTRAINT [FK_Quotations_FinancialYears]       FOREIGN KEY ([FinancialYearId])     REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_Quotations_HeaderNote]           FOREIGN KEY ([HeaderNoteId])        REFERENCES [dbo].[HeaderNote] ([HeaderNoteId]),
    CONSTRAINT [FK_Quotations_FootNotes]            FOREIGN KEY ([FootNoteId])          REFERENCES [dbo].[FootNotes] ([FootNoteId]),
    CONSTRAINT [FK_Quotations_Employees]            FOREIGN KEY ([SalesPerson])         REFERENCES [dbo].[Employees] ([EmployeeId]),
    CONSTRAINT [FK_Quotations_PaymentTerms]         FOREIGN KEY ([PaymentTermsId])      REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
    CONSTRAINT [FK_Quotations_Documents]            FOREIGN KEY ([DocumentId])          REFERENCES [dbo].[Documents] ([DocumentId])
);

GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[Quotations]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [QuotationDate]
    ON [dbo].[Quotations]([QuotationDate] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[Quotations]([ClientId] ASC);