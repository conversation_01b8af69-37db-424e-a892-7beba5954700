﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { BusinessTypesRow } from "./BusinessTypesRow";

export namespace BusinessTypesService {
    export const baseUrl = 'Default/BusinessTypes';

    export declare function Create(request: SaveRequest<BusinessTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<BusinessTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<BusinessTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<BusinessTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<BusinessTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<BusinessTypesRow>>;

    export const Methods = {
        Create: "Default/BusinessTypes/Create",
        Update: "Default/BusinessTypes/Update",
        Delete: "Default/BusinessTypes/Delete",
        Retrieve: "Default/BusinessTypes/Retrieve",
        List: "Default/BusinessTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>BusinessTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}