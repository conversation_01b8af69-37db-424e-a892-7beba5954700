﻿CREATE TABLE [dbo].[VendorBillDetails] 
(
    [VendorBillDetailId]    INT             IDENTITY (1, 1) NOT NULL,
    [VendorBillId]          INT             NOT NULL,
    
    [PurchaseOrderDetailId] INT             NULL,
    
    [CommodityTypeId]       INT             NOT NULL,
    [CommodityId]           BIGINT          NOT NULL,
    [CommodityDescription]  NVARCHAR (MAX)  NULL,
    
    [BillQuantity]          DECIMAL (18, 2) NOT NULL,
    [UnitId]                INT             NOT NULL,
    [UnitPrice]             DECIMAL (18, 2) NOT NULL,
    
    [DiscountPercent]       DECIMAL (18, 2) NULL,
    [DiscountAmount]        DECIMAL (18, 2) NULL,
    
    [GSTRateId]             INT             NOT NULL,
    [IGSTRate]              DECIMAL (18, 2) NULL,
    [CGSTRate]              DECIMAL (18, 2) NULL,
    [SGSTRate]              DECIMAL (18, 2) NULL,
    
    [DummyField]            NVARCHAR (200)  NULL,
    [NetPricePerUnit]       DECIMAL (18, 2) NULL,
    [NetAmount]             DECIMAL (18, 2) NOT NULL,
    
    CONSTRAINT [PK_VendorBillDetails] PRIMARY KEY CLUSTERED ([VendorBillDetailId] ASC),
    CONSTRAINT [FK_VendorBillDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_VendorBillDetails_CommodityId] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_VendorBillDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_VendorBillDetails_PODetails] FOREIGN KEY ([PurchaseOrderDetailId]) REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_VendorBillDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);

GO
CREATE NONCLUSTERED INDEX [PODetails]
    ON [dbo].[VendorBillDetails]([PurchaseOrderDetailId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[VendorBillDetails]([CommodityId] ASC);

