﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ConsultantsRow } from "./ConsultantsRow";

export namespace ConsultantsService {
    export const baseUrl = 'Default/Consultants';

    export declare function Create(request: SaveRequest<ConsultantsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ConsultantsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ConsultantsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ConsultantsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ConsultantsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ConsultantsRow>>;

    export const Methods = {
        Create: "Default/Consultants/Create",
        Update: "Default/Consultants/Update",
        Delete: "Default/Consultants/Delete",
        Retrieve: "Default/Consultants/Retrieve",
        List: "Default/Consultants/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ConsultantsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}