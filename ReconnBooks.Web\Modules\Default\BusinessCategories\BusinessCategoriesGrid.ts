import { BusinessCategoriesColumns, BusinessCategoriesRow, BusinessCategoriesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { BusinessCategoriesDialog } from './BusinessCategoriesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.BusinessCategoriesGrid')
export class BusinessCategoriesGrid extends EntityGridDialog<BusinessCategoriesRow, any> {
    protected getColumnsKey() { return BusinessCategoriesColumns.columnsKey; }
    protected getDialogType() { return BusinessCategoriesDialog; }
    protected getRowDefinition() { return BusinessCategoriesRow; }
    protected getService() { return BusinessCategoriesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}