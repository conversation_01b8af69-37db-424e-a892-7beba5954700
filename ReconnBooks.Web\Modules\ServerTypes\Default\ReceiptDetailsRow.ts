﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ReceiptDetailsRow {
    RowNumber?: number;
    ReceiptDetailId?: number;
    ReceiptId?: number;
    InvoiceId?: number;
    InvoiceNo?: string;
    InvoiceDate?: string;
    InvoiceGrandTotal?: number;
    TDSRateId?: number;
    TDSRate?: number;
    TDSAmount?: number;
    TCSRateId?: number;
    TCSRate?: number;
    TCSAmount?: number;
    AmountReceived?: number;
    ReceiptNo?: string;
    BalanceReceivable?: number;
    TaxableAmount?: number;
    IsTDSPaid?: boolean;
    IsTCSPaid?: boolean;
}

export abstract class ReceiptDetailsRow {
    static readonly idProperty = 'ReceiptDetailId';
    static readonly localTextPrefix = 'Default.ReceiptDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ReceiptDetailsRow>();
}