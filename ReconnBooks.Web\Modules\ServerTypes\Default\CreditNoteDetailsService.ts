﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CreditNoteDetailsRow } from "./CreditNoteDetailsRow";

export namespace CreditNoteDetailsService {
    export const baseUrl = 'Default/CreditNoteDetails';

    export declare function Create(request: SaveRequest<CreditNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CreditNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CreditNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CreditNoteDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CreditNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CreditNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/CreditNoteDetails/Create",
        Update: "Default/CreditNoteDetails/Update",
        Delete: "Default/CreditNoteDetails/Delete",
        Retrieve: "Default/CreditNoteDetails/Retrieve",
        List: "Default/CreditNoteDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CreditNoteDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}