﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface FeedbacksRow {
    RowNumber?: number;
    FeedbackId?: number;
    Feedback?: string;
    Description?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class FeedbacksRow {
    static readonly idProperty = 'FeedbackId';
    static readonly nameProperty = 'Feedback';
    static readonly localTextPrefix = 'Default.Feedbacks';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<FeedbacksRow>();
}