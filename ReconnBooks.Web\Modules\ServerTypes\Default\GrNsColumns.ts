﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { GrNsRow } from "./GrNsRow";

export interface GrNsColumns {
    RowNumber: Column<GrNsRow>;
    GRNNo: Column<GrNsRow>;
    GRNDate: Column<GrNsRow>;
    GRNTypeName: Column<GrNsRow>;
    VendorName: Column<GrNsRow>;
    PurchaseOrderNo: Column<GrNsRow>;
    VendorDcInvoiceNo: Column<GrNsRow>;
    VendorDcInvoiceDate: Column<GrNsRow>;
    ShippingDocketNo: Column<GrNsRow>;
    ShippedThrough: Column<GrNsRow>;
    DeliveryAddress: Column<GrNsRow>;
    DeliveryCityCityName: Column<GrNsRow>;
    VehicleNo: Column<GrNsRow>;
    VehicleType: Column<GrNsRow>;
    GatePassNo: Column<GrNsRow>;
    GatePassDate: Column<GrNsRow>;
    Inspection: Column<GrNsRow>;
    ReceivedByEmployeeEmployeeName: Column<GrNsRow>;
    FinancialYearName: Column<GrNsRow>;
    GRNMonth: Column<GrNsRow>;
    UploadDocuments: Column<GrNsRow>;
    Remarks: Column<GrNsRow>;
    ClientId: Column<GrNsRow>;
    PreparedByUserUsername: Column<GrNsRow>;
    PreparedDate: Column<GrNsRow>;
    VerifiedByUserUsername: Column<GrNsRow>;
    VerifiedDate: Column<GrNsRow>;
    AuthorizedByUserUsername: Column<GrNsRow>;
    AuthorizedDate: Column<GrNsRow>;
    ModifiedByUserUsername: Column<GrNsRow>;
    ModifiedDate: Column<GrNsRow>;
    CancelledByUserUsername: Column<GrNsRow>;
    CancelledDate: Column<GrNsRow>;
    AuthorizedStatus: Column<GrNsRow>;
    GRNId: Column<GrNsRow>;
}

export class GrNsColumns extends ColumnsBase<GrNsRow> {
    static readonly columnsKey = 'Default.GrNs';
    static readonly Fields = fieldsProxy<GrNsColumns>();
}