using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GrNsRow>;
using MyRow = ReconnBooks.Default.GrNsRow;

namespace ReconnBooks.Default;

public interface IGrNsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GrNsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGrNsRetrieveHandler
{
    public GrNsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}