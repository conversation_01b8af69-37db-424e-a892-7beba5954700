import { RacksColumns, RacksRow, RacksService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { RacksDialog } from './RacksDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.RacksGrid')
export class RacksGrid extends EntityGridDialog<RacksRow, any> {
    protected getColumnsKey() { return RacksColumns.columnsKey; }
    protected getDialogType() { return RacksDialog; }
    protected getRowDefinition() { return RacksRow; }
    protected getService() { return RacksService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}