import { ProformaInvoicesColumns, ProformaInvoicesRow, ProformaInvoicesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, LookupEditor, WidgetProps, notifySuccess, notifyError } from '@serenity-is/corelib';
import { ProformaInvoicesDialog } from './ProformaInvoicesDialog';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { ExcelExportHelper, PdfExportHelper, ReportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";

@Decorators.registerClass('ReconnBooks.Default.ProformaInvoicesGrid')
@Decorators.filterable()
export class ProformaInvoicesGrid extends EntityGrid<ProformaInvoicesRow, any> {
    protected getColumnsKey() { return ProformaInvoicesColumns.columnsKey; }
    protected getDialogType() { return ProformaInvoicesDialog; }
    protected getRowDefinition() { return ProformaInvoicesRow; }
    protected getService() { return ProformaInvoicesService.baseUrl; }

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }
    }

    protected getColumns() {
        var columns = super.getColumns();

        columns.splice(1, 0, {
            id: 'Print ProformaInvoice',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="print-proformaInvoice" title="proformaInvoice"><i class="fas fa-file-pdf" style="color: red"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        columns.splice(2, 0, {
            id: 'Email ProformaInvoice',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="email-proformaInvoice" title="email proformaInvoice"><i class="fas fa-envelope" style="color: #4CAF50"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        return columns;
    }

    protected onClick(e: Event, row: number, cell: number) {
        super.onClick(e, row, cell);
        
        var item = this.itemAt(row);
        let action = (e.target as HTMLElement)?.closest(".inline-action")?.getAttribute("data-action");
        if (action) {
            e.preventDefault();
            if (action == "print-proformaInvoice") {
                ReportHelper.execute({
                    reportKey: 'ProformaInvoiceReport',
                    params: {
                        ID: item.ProformaInvoiceId
                    }
                });
            }
            else if (action == "email-proformaInvoice") {
                this.emailProformaInvoice(item);
            }
        }
    }

    private async emailProformaInvoice(item: ProformaInvoicesRow) {
        try {
            await ProformaInvoicesService.EmailProformaInvoice({
                DocumentId: item.ProformaInvoiceId,
                DocumentNo: item.ProformaInvoiceNo,
                ToEmail: item.CustomerEMailId
            });
            notifySuccess("ProformaInvoice has been emailed successfully");
        }
        catch (e) {
            notifyError(e.message);
        }
    }

    protected getButtons() {
        var buttons = super.getButtons();

        const addButton = buttons.find(x => x.cssClass === "add-button");
        if (addButton) {
            addButton.title = "New Proforma Invoice";
        }

        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: ProformaInvoicesService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));

        return buttons;
    }
}