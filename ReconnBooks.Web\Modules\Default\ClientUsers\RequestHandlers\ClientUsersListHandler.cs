using ReconnBooks.Modules.Default.ClientUsers.RequestHandlers;
using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.ClientUsersRow>;
using MyRow = ReconnBooks.Default.ClientUsersRow;

namespace ReconnBooks.Default;

public interface IClientUsersListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class ClientUsersListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IClientUsersListHandler
{
    private readonly ISqlConnections sqlConnections;
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly IPermissionService permissionService;

    public ClientUsersListHandler(IRequestContext context, ISqlConnections sqlConnections,
        IUserAccessor userAccessor, IUserRetrieveService userRetriever,
        IPermissionService permissionService)
            : base(context)
    {
        this.sqlConnections = sqlConnections;
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.permissionService = permissionService;
    }

    protected override void ApplyFilters(SqlQuery query)
    {
        base.ApplyFilters(query);
        ConsultantFilterQuery.PrepareRetrieveQuery(query, permissionService, userRetriever, userAccessor);
    }
}