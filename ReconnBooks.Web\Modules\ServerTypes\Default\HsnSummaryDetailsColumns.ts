﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { HsnSummaryDetailsRow } from "./HsnSummaryDetailsRow";

export interface HsnSummaryDetailsColumns {
    RowNumber: Column<HsnSummaryDetailsRow>;
    InvoiceNo: Column<HsnSummaryDetailsRow>;
    InvoiceDate: Column<HsnSummaryDetailsRow>;
    HSNSACCode: Column<HsnSummaryDetailsRow>;
    Quantity: Column<HsnSummaryDetailsRow>;
    NetTaxableAmount: Column<HsnSummaryDetailsRow>;
    IGSTRate: Column<HsnSummaryDetailsRow>;
    NetIGSTAmount: Column<HsnSummaryDetailsRow>;
    CGSTRate: Column<HsnSummaryDetailsRow>;
    NetCGSTAmount: Column<HsnSummaryDetailsRow>;
    SGSTRate: Column<HsnSummaryDetailsRow>;
    NetSGSTAmount: Column<HsnSummaryDetailsRow>;
    NetAmount: Column<HsnSummaryDetailsRow>;
}

export class HsnSummaryDetailsColumns extends ColumnsBase<HsnSummaryDetailsRow> {
    static readonly columnsKey = 'Default.HsnSummaryDetailsColumns';
    static readonly Fields = fieldsProxy<HsnSummaryDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types