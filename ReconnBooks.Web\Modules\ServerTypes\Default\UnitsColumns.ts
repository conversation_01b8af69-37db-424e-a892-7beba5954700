﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { UnitsRow } from "./UnitsRow";

export interface UnitsColumns {
    RowNumber: Column<UnitsRow>;
    UnitName: Column<UnitsRow>;
    UnitDescription: Column<UnitsRow>;
    UQCQuantityName: Column<UnitsRow>;
    SetDefault: Column<UnitsRow>;
    UnitId: Column<UnitsRow>;
}

export class UnitsColumns extends ColumnsBase<UnitsRow> {
    static readonly columnsKey = 'Default.Units';
    static readonly Fields = fieldsProxy<UnitsColumns>();
}