﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface PoAmendmentDetailsRow {
    RowNumber?: number;
    POAmendmentDetailId?: number;
    POAmendmentId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    POQuantity?: number;
    POUnitId?: number;
    POUnitPrice?: number;
    POUnitAmount?: number;
    AmendedQuantity?: number;
    AmendedUnitId?: number;
    AmendedUnitPrice?: number;
    AmendedUnitAmount?: number;
    PendingQuantity?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    GSTRateRemarks?: string;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
    POAmendmentNo?: string;
    POUnitUnitName?: string;
    AmendedUnitUnitName?: string;
}

export abstract class PoAmendmentDetailsRow {
    static readonly idProperty = 'POAmendmentDetailId';
    static readonly nameProperty = 'POAmendmentDetailId';
    static readonly localTextPrefix = 'Default.PoAmendmentDetails';
    static readonly lookupKey = 'Default.PoAmendmentDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PoAmendmentDetailsRow>('Default.PoAmendmentDetails') }
    static async getLookupAsync() { return getLookupAsync<PoAmendmentDetailsRow>('Default.PoAmendmentDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PoAmendmentDetailsRow>();
}