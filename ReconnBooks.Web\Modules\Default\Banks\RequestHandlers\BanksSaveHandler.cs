﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.BanksRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.BanksRow;

namespace ReconnBooks.Default;

public interface IBanksSaveHandler : <PERSON>ave<PERSON><PERSON><PERSON><MyRow, MyRequest, MyResponse> {}

public class BanksSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IBanksSaveHandler
{
    public BanksSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}