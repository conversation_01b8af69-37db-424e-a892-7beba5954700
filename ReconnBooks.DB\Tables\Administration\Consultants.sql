﻿CREATE TABLE [dbo].[Consultants] 
(
    [ConsultantId]       INT                NOT NULL    IDENTITY (1, 1) ,
    [ConsultantName]     NVARCHAR (500)     NOT NULL,
    [ConsultantCode]     NVARCHAR (6)       NOT NULL,
    [Address]            NVARCHAR (MAX)     NOT NULL,
    [CityId]             INT                NOT NULL,
    [PINCode]            NVARCHAR (10)          NULL,
    [PhoneNo]            NVARCHAR (50)          NULL,
    [FaxNo]              NVARCHAR (50)          NULL,
    [HomePage]           NVARCHAR (150)         NULL,
    [Logo]               NVARCHAR (MAX)         NULL,
    [TagLine]            NVARCHAR (MAX)         NULL,
   
    [GSTIN]              NVARCHAR (30)          NULL,
    [PlaceOfSupplyId]    INT                NOT NULL    DEFAULT ((1)) ,
    [NatureOfSupplyId]   INT                    NULL,
    [SupplyTypeId]       INT                    NULL,
    [PAN]                NVARCHAR (30)          NULL,
    [UdyamNo]            NVARCHAR (80)          NULL,
    [IECNo]              NVARCHAR (80)          NULL,
    [CINNo]              NVARCHAR (80)          NULL,
    [TANNo]              NVARCHAR (80)          NULL,
    
    [TitleId]            INT                    NULL,
    [ContactPerson]      NVARCHAR (250)         NULL,
    [DesignationId]      INT                    NULL,
    [MobileNo]           NVARCHAR (18)          NULL,
    [AlternateNo]        NVARCHAR (18)          NULL,
    [EMail]              NVARCHAR (150)          NULL,
    [UploadDocuments]    NVARCHAR (MAX)         NULL,
    [ConsultantDSC]      NVARCHAR (MAX)         NULL,
    [BusinessTypeId]     INT                    NULL,
    [BusinessGroupId]    INT                    NULL,
    [BusinessCategoryId] INT                    NULL,
    [InvoiceNoFormat]    NVARCHAR (20)          NULL,
    [Disclaimer]         NVARCHAR (MAX)         NULL,
    
    CONSTRAINT [PK_Consultants] PRIMARY KEY         CLUSTERED ([ConsultantId] ASC),
    CONSTRAINT [FK_Consultants_SupplyTypes]         FOREIGN KEY ([SupplyTypeId])        REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_Consultants_BusinessTypes]       FOREIGN KEY ([BusinessTypeId])      REFERENCES [dbo].[BusinessTypes] ([BusinessTypeId]),
    CONSTRAINT [FK_Consultants_BusinessGroups]      FOREIGN KEY ([BusinessGroupId])     REFERENCES [dbo].[BusinessGroups] ([BusinessGroupId]),
    CONSTRAINT [FK_Consultants_BusinessCategories]  FOREIGN KEY ([BusinessCategoryId])  REFERENCES [dbo].[BusinessCategories] ([BusinessCategoryId]),
    CONSTRAINT [FK_Consultants_Titles]              FOREIGN KEY ([TitleId])             REFERENCES [dbo].[Titles] ([TitleId]),
    CONSTRAINT [FK_Consultants_Cities]              FOREIGN KEY ([CityId])              REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Consultants_States]              FOREIGN KEY ([PlaceOfSupplyId])     REFERENCES [dbo].[States] ([StateId]),
    CONSTRAINT [FK_Consultants_Designations]        FOREIGN KEY ([DesignationId])       REFERENCES [dbo].[Designations] ([DesignationId]),
    CONSTRAINT [FK_Consultants_NatureOfSupply]      FOREIGN KEY ([NatureOfSupplyId])    REFERENCES [dbo].[NatureOfSupply] ([NatureOfSupplyId]),
    CONSTRAINT [FK_Consultants_SupplyType]          FOREIGN KEY ([SupplyTypeId])        REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId])
);

GO
CREATE NONCLUSTERED INDEX [City]
    ON [dbo].[Consultants]([CityId] ASC);

GO
CREATE NONCLUSTERED INDEX [ConsultantCompanyName]
    ON [dbo].[Consultants]([ConsultantName] ASC);

GO
CREATE NONCLUSTERED INDEX [ConsultantPINCode]
    ON [dbo].[Consultants]([PINCode] ASC);

GO
CREATE NONCLUSTERED INDEX [TitleId]
    ON [dbo].[Consultants]([TitleId] ASC);

GO
CREATE UNIQUE NONCLUSTERED INDEX [Consultants]
    ON [dbo].[Consultants]([ConsultantId] ASC);

GO
CREATE UNIQUE NONCLUSTERED INDEX [Consultant]
    ON [dbo].[Consultants]([ConsultantId] ASC);


