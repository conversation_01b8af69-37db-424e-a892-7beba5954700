﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface ClientUsersRow {
    ClientUserId?: number;
    UserId?: number;
    ClientId?: number;
    Status?: boolean;
    Username?: string;
    ClientName?: string;
    ClientLogo?: string;
    ConsultantId?: number;
    ConsultantName?: string;
}

export abstract class ClientUsersRow {
    static readonly idProperty = 'ClientUserId';
    static readonly localTextPrefix = 'Default.ClientUsers';
    static readonly lookupKey = 'Administration.ClientUsers';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ClientUsersRow>('Administration.ClientUsers') }
    static async getLookupAsync() { return getLookupAsync<ClientUsersRow>('Administration.ClientUsers') }

    static readonly deletePermission = 'Administration:Consultants:User';
    static readonly insertPermission = 'Administration:Consultants:User';
    static readonly readPermission = 'Administration:Consultants:User';
    static readonly updatePermission = 'Administration:Consultants:User';

    static readonly Fields = fieldsProxy<ClientUsersRow>();
}