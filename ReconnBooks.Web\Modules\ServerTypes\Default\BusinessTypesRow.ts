﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface BusinessTypesRow {
    RowNumber?: number;
    BusinessTypeId?: number;
    BusinessType?: string;
    Description?: string;
}

export abstract class BusinessTypesRow {
    static readonly idProperty = 'BusinessTypeId';
    static readonly nameProperty = 'BusinessType';
    static readonly localTextPrefix = 'Default.BusinessTypes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<BusinessTypesRow>();
}