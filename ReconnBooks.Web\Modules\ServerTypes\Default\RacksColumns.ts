﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { RacksRow } from "./RacksRow";

export interface RacksColumns {
    RowNumber: Column<RacksRow>;
    RackNo: Column<RacksRow>;
    CompartmentNo: Column<RacksRow>;
    BinNo: Column<RacksRow>;
    RackDescription: Column<RacksRow>;
    Remarks: Column<RacksRow>;
    StoreName: Column<RacksRow>;
    Discontinued: Column<RacksRow>;
    ClientId: Column<RacksRow>;
    RackId: Column<RacksRow>;
}

export class RacksColumns extends ColumnsBase<RacksRow> {
    static readonly columnsKey = 'Default.Racks';
    static readonly Fields = fieldsProxy<RacksColumns>();
}