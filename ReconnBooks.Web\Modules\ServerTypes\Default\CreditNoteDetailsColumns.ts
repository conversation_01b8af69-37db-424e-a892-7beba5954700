﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { CreditNoteDetailsRow } from "./CreditNoteDetailsRow";

export interface CreditNoteDetailsColumns {
    RowNumber: Column<CreditNoteDetailsRow>;
    CreditNoteNo: Column<CreditNoteDetailsRow>;
    CommodityName: Column<CreditNoteDetailsRow>;
    CommodityCode: Column<CreditNoteDetailsRow>;
    CommodityType: Column<CreditNoteDetailsRow>;
    InvoiceQuantity: Column<CreditNoteDetailsRow>;
    InvoiceUnitUnitName: Column<CreditNoteDetailsRow>;
    ReturnedQuantity: Column<CreditNoteDetailsRow>;
    ReturnedUnitUnitName: Column<CreditNoteDetailsRow>;
    SerialNos: Column<CreditNoteDetailsRow>;
    UnitPrice: Column<CreditNoteDetailsRow>;
    UnitAmount: Column<CreditNoteDetailsRow>;
    DiscountPercent: Column<CreditNoteDetailsRow>;
    NetDiscountAmount: Column<CreditNoteDetailsRow>;
    NetTaxableAmount: Column<CreditNoteDetailsRow>;
    GSTRateRemarks: Column<CreditNoteDetailsRow>;
    IGSTRate: Column<CreditNoteDetailsRow>;
    IGSTAmountPerUnit: Column<CreditNoteDetailsRow>;
    NetIGSTAmount: Column<CreditNoteDetailsRow>;
    CGSTRate: Column<CreditNoteDetailsRow>;
    CGSTAmountPerUnit: Column<CreditNoteDetailsRow>;
    NetCGSTAmount: Column<CreditNoteDetailsRow>;
    SGSTRate: Column<CreditNoteDetailsRow>;
    SGSTAmountPerUnit: Column<CreditNoteDetailsRow>;
    NetSGSTAmount: Column<CreditNoteDetailsRow>;
    NetAmount: Column<CreditNoteDetailsRow>;
    NetPricePerUnit: Column<CreditNoteDetailsRow>;
    RejectionReason: Column<CreditNoteDetailsRow>;
    AssessmentRemarks: Column<CreditNoteDetailsRow>;
    ReplacementMethod: Column<CreditNoteDetailsRow>;
    Remarks: Column<CreditNoteDetailsRow>;
    InvoiceDetailCommodityDescription: Column<CreditNoteDetailsRow>;
    SalesReturnDetailCommodityDescription: Column<CreditNoteDetailsRow>;
    CreditNoteDetailId: Column<CreditNoteDetailsRow>;
}

export class CreditNoteDetailsColumns extends ColumnsBase<CreditNoteDetailsRow> {
    static readonly columnsKey = 'Default.CreditNoteDetails';
    static readonly Fields = fieldsProxy<CreditNoteDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types