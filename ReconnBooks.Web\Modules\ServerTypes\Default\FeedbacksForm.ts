﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface FeedbacksForm {
    Feedback: StringEditor;
    Description: StringEditor;
}

export class FeedbacksForm extends PrefixedContext {
    static readonly formKey = 'Default.Feedbacks';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!FeedbacksForm.init)  {
            FeedbacksForm.init = true;

            var w0 = StringEditor;

            initFormType(FeedbacksForm, [
                'Feedback', w0,
                'Description', w0
            ]);
        }
    }
}