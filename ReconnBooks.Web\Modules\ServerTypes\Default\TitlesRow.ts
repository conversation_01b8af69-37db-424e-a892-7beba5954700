﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface TitlesRow {
    RowNumber?: number;
    TitleId?: number;
    TitleOfRespect?: string;
    Gender?: string;
}

export abstract class TitlesRow {
    static readonly idProperty = 'TitleId';
    static readonly nameProperty = 'TitleOfRespect';
    static readonly localTextPrefix = 'Default.Titles';
    static readonly lookupKey = 'Default.Titles';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<TitlesRow>('Default.Titles') }
    static async getLookupAsync() { return getLookupAsync<TitlesRow>('Default.Titles') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<TitlesRow>();
}