using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Administration.RoleRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Administration.RoleRow;

namespace ReconnBooks.Administration;
public interface IRoleSaveHandler : ISave<PERSON>and<PERSON><MyRow, MyRequest, MyResponse> { }
public class RoleSaveHandler(IRequestContext context) :
    SaveRequestHandler<MyRow, MyRequest, MyResponse>(context), IRoleSaveHandler
{
    protected override void InvalidateCacheOnCommit()
    {
        base.InvalidateCacheOnCommit();

        Cache.InvalidateOnCommit(UnitOfWork, UserPermissionRow.Fields);
        Cache.InvalidateOnCommit(UnitOfWork, RolePermissionRow.Fields);
    }
}