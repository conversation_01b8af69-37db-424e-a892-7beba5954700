﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ConsultantBankAccountsRow } from "./ConsultantBankAccountsRow";

export namespace ConsultantBankAccountsService {
    export const baseUrl = 'Default/ConsultantBankAccounts';

    export declare function Create(request: SaveRequest<ConsultantBankAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ConsultantBankAccountsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ConsultantBankAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ConsultantBankAccountsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ConsultantBankAccountsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ConsultantBankAccountsRow>>;

    export const Methods = {
        Create: "Default/ConsultantBankAccounts/Create",
        Update: "Default/ConsultantBankAccounts/Update",
        Delete: "Default/ConsultantBankAccounts/Delete",
        Retrieve: "Default/ConsultantBankAccounts/Retrieve",
        List: "Default/ConsultantBankAccounts/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>ConsultantBankAccountsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}