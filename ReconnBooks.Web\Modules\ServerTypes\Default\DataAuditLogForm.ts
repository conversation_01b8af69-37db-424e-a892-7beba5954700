﻿import { EnumEditor, DateTimeEditor, LookupEditor, StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { DataAuditLogType } from "./DataAuditLogType";

export interface DataAuditLogForm {
    LogType: EnumEditor;
    LogDate: DateTimeEditor;
    UserId: LookupEditor;
    Tablename: LookupEditor;
    RecordId: StringEditor;
    FieldName: LookupEditor;
    OldValue: StringEditor;
    NewValue: StringEditor;
}

export class DataAuditLogForm extends PrefixedContext {
    static readonly formKey = 'Default.DataAuditLog';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DataAuditLogForm.init)  {
            DataAuditLogForm.init = true;

            var w0 = EnumEditor;
            var w1 = DateTimeEditor;
            var w2 = LookupEditor;
            var w3 = StringEditor;

            initFormType(DataAuditLogForm, [
                'LogType', w0,
                'LogDate', w1,
                'UserId', w2,
                'Tablename', w2,
                'RecordId', w3,
                'FieldName', w2,
                'OldValue', w3,
                'NewValue', w3
            ]);
        }
    }
}

[DataAuditLogType]; // referenced types