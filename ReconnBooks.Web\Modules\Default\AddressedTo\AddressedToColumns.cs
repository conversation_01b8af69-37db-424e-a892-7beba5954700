using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.AddressedTo")]
[BasedOnRow(typeof(AddressedToRow), CheckNames = true)]
public class AddressedToColumns
{
    public long RowNumber { get; set; }
    [EditLink, DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int AddressedToId { get; set; }
    
    [EditLink, Width(300)]
    public string AddressedTo { get; set; }
}