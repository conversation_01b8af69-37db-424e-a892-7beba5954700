﻿import { StringEditor, DecimalEditor, TextAreaEditor, DateEditor, LookupEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface TcsRatesForm {
    Section: StringEditor;
    TCSRate: DecimalEditor;
    NatureOfTransaction: TextAreaEditor;
    Collector: StringEditor;
    Collectee: StringEditor;
    WefDate: DateEditor;
    FinancialYearId: LookupEditor;
    IsDefault: BooleanEditor;
    Description: TextAreaEditor;
}

export class TcsRatesForm extends PrefixedContext {
    static readonly formKey = 'Default.TcsRates';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!TcsRatesForm.init)  {
            TcsRatesForm.init = true;

            var w0 = StringEditor;
            var w1 = DecimalEditor;
            var w2 = TextAreaEditor;
            var w3 = DateEditor;
            var w4 = LookupEditor;
            var w5 = BooleanEditor;

            initFormType(TcsRatesForm, [
                'Section', w0,
                'TCSRate', w1,
                'NatureOfTransaction', w2,
                'Collector', w0,
                'Collectee', w0,
                'WefDate', w3,
                'FinancialYearId', w4,
                'IsDefault', w5,
                'Description', w2
            ]);
        }
    }
}