﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface PurchaseReturnDetailsRow {
    RowNumber?: number;
    PurchaseReturnDetailId?: number;
    PurchaseReturnId?: number;
    PurchaseOrderDetailId?: number;
    GRNDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    RejectedQuantity?: number;
    UnitId?: number;
    RejectedAmount?: number;
    RejectedItemSerialNo?: string;
    RejectionReasonId?: number;
    AssessmentRemarks?: string;
    ReplacementMethodId?: number;
    Remarks?: string;
    PurchaseReturnNo?: string;
    PurchaseOrderDetailCommodityDescription?: string;
    GRNDetailCommodityDescription?: string;
    UnitName?: string;
    RejectionReason?: string;
    ReplacementMethod?: string;
}

export abstract class PurchaseReturnDetailsRow {
    static readonly idProperty = 'PurchaseReturnDetailId';
    static readonly nameProperty = 'PurchaseReturnDetailId';
    static readonly localTextPrefix = 'Default.PurchaseReturnDetails';
    static readonly lookupKey = 'Default.PurchaseReturnDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PurchaseReturnDetailsRow>('Default.PurchaseReturnDetails') }
    static async getLookupAsync() { return getLookupAsync<PurchaseReturnDetailsRow>('Default.PurchaseReturnDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PurchaseReturnDetailsRow>();
}