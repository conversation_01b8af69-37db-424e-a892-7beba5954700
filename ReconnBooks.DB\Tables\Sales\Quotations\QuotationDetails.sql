﻿CREATE TABLE [dbo].[QuotationDetails] (
    [QuotationDetailId]     INT             IDENTITY (1, 1) NOT NULL,
    [QuotationId]           INT             NOT NULL,
    [CommodityTypeId]       INT             NOT NULL,
    [CommodityId]           BIGINT          NOT NULL,
    [CommodityDescription]  NVARCHAR (MAX)  NULL,
    
    [Quantity]              DECIMAL (18, 2) CONSTRAINT [DF_QuotationDetails_Quantity] DEFAULT ((1)) NOT NULL,
    [UnitId]                INT             NOT NULL,
    [RevisedQuantity]       DECIMAL (18, 2) NULL,
    [UnitPrice]             DECIMAL (18, 2) CONSTRAINT [DF_QuotationDetails_UnitPrice] DEFAULT ((0)) NOT NULL,
    
    [DiscountPercent]       DECIMAL (18, 2) DEFAULT ((0)) NULL,
    [DiscountAmountPerUnit] DECIMAL (18, 2) NULL,
    [DiscountAmount]        DECIMAL (18, 2) NULL,
    
    [GSTRateId]             INT             NOT NULL,
    [IGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_QuotationDetails_IGSTRate] DEFAULT ((0)) NULL,
    [CGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_QuotationDetails_CGSTRate] DEFAULT ((0)) NULL,
    [SGSTRate]              DECIMAL (18, 2) CONSTRAINT [DF_QuotationDetails_SGSTRate] DEFAULT ((0)) NULL,
    
    [PerUnitPrice]          DECIMAL (18, 2) NULL,
    [NetAmount]             DECIMAL (18, 2) NULL,
    [GrossTotal]            DECIMAL (18, 2) NULL,
    
    CONSTRAINT [PK_QuotationDetails] PRIMARY KEY CLUSTERED ([QuotationDetailId] ASC),
    CONSTRAINT [FK_QuotationDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_QuotationDetails_CommodityTypes] FOREIGN KEY ([CommodityTypeId]) REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_QuotationDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_QuotationDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_QuotationDetails_Quotations] FOREIGN KEY ([QuotationId]) REFERENCES [dbo].[Quotations] ([QuotationId])
);


GO
CREATE NONCLUSTERED INDEX [QuotationId]
    ON [dbo].[QuotationDetails]([QuotationId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[QuotationDetails]([CommodityId] ASC);

