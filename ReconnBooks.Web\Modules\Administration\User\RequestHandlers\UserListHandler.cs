using ReconnBooks.Modules.Administration.User.Authentication.Claims;
using MyRequest = ReconnBooks.Administration.UserListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Administration.UserRow>;
using MyRow = ReconnBooks.Administration.UserRow;

namespace ReconnBooks.Administration;
public interface IUserListHandler : IListHandler<MyRow, MyRequest, MyResponse> { }

public class UserListHandler(IRequestContext context) :
    ListRequestHandler<MyRow, MyRequest, MyResponse>(context), IUserListHandler
{
    protected override void OnReturn()
    {
        base.OnReturn();

        if (Request.DataProtector != null &&
            Request.ClientHash != null &&
            Request.IncludeColumns != null &&
            Request.IncludeColumns.Contains("ImpersonationToken") &&
            Permissions.HasPermission("ImpersonateAs") &&
            !Response.Entities.IsEmptyOrNull())
        {
            foreach (var entity in Response.Entities)
                if (string.Compare(entity.Username, "admin", StringComparison.OrdinalIgnoreCase) != 0)
                    entity.ImpersonationToken = UserHelper.GetImpersonationToken(Request.DataProtector,
                        Request.ClientHash, Context.User.Identity.Name, entity.Username);
        }
    }

    protected override void ApplyFilters(SqlQuery query)
    {
        base.ApplyFilters(query);

        var username = User.Identity.Name;

        if (username == "admin")
            return;

        if (User.GetConsultantId().HasValue)
        {
            query.Where(MyRow.Fields.ConsultantId == User.GetConsultantId().Value);
        }
        else if (User.GetClientId().HasValue)
        {
            query.Where(MyRow.Fields.ClientId == User.GetClientId().Value);
        }
    }
}