﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { TransactionStatus } from "../Modules/Common.Helpers.TransactionStatus";
import { ReceiptDetailsRow } from "./ReceiptDetailsRow";

export interface ReceiptsRow {
    RowNumber?: number;
    ReceiptId?: number;
    ReceiptNo?: string;
    ReceiptDate?: string;
    ReceiptMonth?: string;
    CustomerId?: number;
    FinancialYearId?: number;
    ModeOfPaymentId?: number;
    TotalReceivable?: number;
    OnAccount?: number;
    TDSRateId?: number;
    TDSRateTransaction?: string;
    TCSRateId?: number;
    TCSRateNatureOfTransaction?: string;
    AmountReceived?: number;
    Narration?: string;
    ReceiptDetailsList?: ReceiptDetailsRow[];
    ChequeDdNo?: string;
    ChequeDdDate?: string;
    BankBranchName?: string;
    PaymentRefNo?: string;
    Remarks?: string;
    ClientId?: number;
    CancelReason?: string;
    TransactionStatus?: TransactionStatus;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    ModeOfPayment?: string;
    FinancialYearName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class ReceiptsRow {
    static readonly idProperty = 'ReceiptId';
    static readonly nameProperty = 'ReceiptNo';
    static readonly localTextPrefix = 'Default.Receipts';
    static readonly lookupKey = 'Default.Receipts';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ReceiptsRow>('Default.Receipts') }
    static async getLookupAsync() { return getLookupAsync<ReceiptsRow>('Default.Receipts') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ReceiptsRow>();
}