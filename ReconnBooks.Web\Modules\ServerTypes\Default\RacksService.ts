﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { RacksRow } from "./RacksRow";

export namespace RacksService {
    export const baseUrl = 'Default/Racks';

    export declare function Create(request: SaveRequest<RacksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<RacksRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<RacksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<RacksRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<RacksRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<RacksRow>>;

    export const Methods = {
        Create: "Default/Racks/Create",
        Update: "Default/Racks/Update",
        Delete: "Default/Racks/Delete",
        Retrieve: "Default/Racks/Retrieve",
        List: "Default/Racks/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>RacksService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}