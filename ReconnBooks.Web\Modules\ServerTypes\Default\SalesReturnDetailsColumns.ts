﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { SalesReturnDetailsRow } from "./SalesReturnDetailsRow";

export interface SalesReturnDetailsColumns {
    RowNumber: Column<SalesReturnDetailsRow>;
    SalesReturnNo: Column<SalesReturnDetailsRow>;
    CommodityName: Column<SalesReturnDetailsRow>;
    CommodityCode: Column<SalesReturnDetailsRow>;
    CommodityType: Column<SalesReturnDetailsRow>;
    RejectedQuantity: Column<SalesReturnDetailsRow>;
    UnitName: Column<SalesReturnDetailsRow>;
    RejectionReason: Column<SalesReturnDetailsRow>;
    AssessmentRemarks: Column<SalesReturnDetailsRow>;
    ReplacementMethod: Column<SalesReturnDetailsRow>;
    CommodityDescription: Column<SalesReturnDetailsRow>;
    RejectedItemSerialNo: Column<SalesReturnDetailsRow>;
    Remarks: Column<SalesReturnDetailsRow>;
    DeliveryNoteDetailProductDescription: Column<SalesReturnDetailsRow>;
    InvoiceDetailCommodityDescription: Column<SalesReturnDetailsRow>;
    SalesReturnDetailId: Column<SalesReturnDetailsRow>;
}

export class SalesReturnDetailsColumns extends ColumnsBase<SalesReturnDetailsRow> {
    static readonly columnsKey = 'Default.SalesReturnDetails';
    static readonly Fields = fieldsProxy<SalesReturnDetailsColumns>();
}