﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface QuotationDetailsRow {
    RowNumber?: number;
    QuotationDetailId?: number;
    QuotationId?: number;
    QuotationNo?: string;
    CommodityTypeId?: number;
    CommodityType?: string;
    CommodityId?: number;
    CommodityName?: string;
    CommodityCode?: string;
    CommodityDescription?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    Quantity?: number;
    UnitId?: number;
    UnitName?: string;
    RevisedQuantity?: number;
    UnitPrice?: number;
    SalesPrice?: number;
    Amount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    DiscountAmount?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    GSTRateId?: number;
    GSTRateRemarks?: string;
    IGSTRate?: number;
    PerUnitIGSTAmount?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    PerUnitCGSTAmount?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    PerUnitSGSTAmount?: number;
    NetSGSTAmount?: number;
    PerUnitPrice?: number;
    NetAmount?: number;
    GrossTotal?: number;
}

export abstract class QuotationDetailsRow {
    static readonly idProperty = 'QuotationDetailId';
    static readonly nameProperty = 'QuotationDetailId';
    static readonly localTextPrefix = 'Default.QuotationDetails';
    static readonly lookupKey = 'Default.QuotationDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<QuotationDetailsRow>('Default.QuotationDetails') }
    static async getLookupAsync() { return getLookupAsync<QuotationDetailsRow>('Default.QuotationDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<QuotationDetailsRow>();
}