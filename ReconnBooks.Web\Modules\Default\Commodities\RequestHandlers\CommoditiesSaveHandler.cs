﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.CommoditiesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.CommoditiesRow;

namespace ReconnBooks.Default;

public interface ICommoditiesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class CommoditiesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, ICommoditiesSaveHandler
{
    public CommoditiesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}