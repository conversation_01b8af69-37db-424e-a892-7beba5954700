﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { GrnDetailsRow } from "./GrnDetailsRow";

export interface GrNsRow {
    RowNumber?: number;
    GRNId?: number;
    GRNNo?: string;
    GRNDate?: string;
    GRNMonth?: string;
    VendorId?: number;
    GRNTypeId?: number;
    GSTIN?: string;
    VendorEMailId?: string;
    PurchaseOrderId?: number;
    FinancialYearId?: number;
    FinancialYearName?: string;
    VendorDcInvoiceNo?: string;
    VendorDcInvoiceDate?: string;
    GrnDetailsList?: GrnDetailsRow[];
    ShippedThrough?: string;
    ShippingDocketNo?: string;
    DeliveryAddress?: string;
    DeliveryCityId?: number;
    DeliveryPinCode?: number;
    VehicleNo?: string;
    VehicleType?: string;
    GatePassNo?: string;
    GatePassDate?: string;
    Inspection?: string;
    ReceivedByEmployeeId?: number;
    UploadDocuments?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    GRNTypeName?: string;
    VendorName?: string;
    PurchaseOrderNo?: string;
    DeliveryCityCityName?: string;
    ReceivedByEmployeeEmployeeName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class GrNsRow {
    static readonly idProperty = 'GRNId';
    static readonly nameProperty = 'GRNNo';
    static readonly localTextPrefix = 'Default.GrNs';
    static readonly lookupKey = 'Default.GrNs';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<GrNsRow>('Default.GrNs') }
    static async getLookupAsync() { return getLookupAsync<GrNsRow>('Default.GrNs') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GrNsRow>();
}