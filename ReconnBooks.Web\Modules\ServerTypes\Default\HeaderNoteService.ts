﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { HeaderNoteRow } from "./HeaderNoteRow";

export namespace HeaderNoteService {
    export const baseUrl = 'Default/HeaderNote';

    export declare function Create(request: SaveRequest<HeaderNoteRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<HeaderNoteRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<HeaderNoteRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<HeaderNoteRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<HeaderNoteRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<HeaderNoteRow>>;

    export const Methods = {
        Create: "Default/HeaderNote/Create",
        Update: "Default/HeaderNote/Update",
        Delete: "Default/HeaderNote/Delete",
        Retrieve: "Default/HeaderNote/Retrieve",
        List: "Default/HeaderNote/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>HeaderNoteService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}