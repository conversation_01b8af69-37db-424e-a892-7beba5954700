import { alertDialog, Decorators, EntityDialog, isEmptyOrNull } from "@serenity-is/corelib";
import { InvoiceCancelForm } from "../../../ServerTypes/Modules";

@Decorators.registerClass()
export class InvoiceCancelDialog extends EntityDialog<any, any> {
    protected getFormKey() { return InvoiceCancelForm.formKey; }
    protected form = new InvoiceCancelForm(this.idPrefix);
    public returnData = (CancelReason: string) => { };
    constructor(opt?: number) {
        super();
        this.form.InvoiceId.value = opt;
    }

    protected getToolbarButtons() {
        var buttons = super.getToolbarButtons();
        // Remove all buttons.
        buttons.splice(0, buttons.length);
        buttons.push({
            title: 'Confirm',
            // cssClass: 'export-pdf-button',
            onClick: () => {                    
                    if (isEmptyOrNull(this.form.CancelReason.value))
                        alertDialog("Please enter a reason for cancellation")
                    else {
                        this.returnData(this.form.CancelReason.value);
                        this.dialogClose();
                    }
            }
        });
        return buttons;
    }
}
