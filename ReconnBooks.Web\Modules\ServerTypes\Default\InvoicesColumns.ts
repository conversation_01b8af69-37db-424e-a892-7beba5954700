﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { TransactionStatus } from "../Modules/Common.Helpers.TransactionStatus";
import { InvoicesRow } from "./InvoicesRow";

export interface InvoicesColumns {
    RowNumber: Column<InvoicesRow>;
    InvoiceNo: Column<InvoicesRow>;
    InvoiceDate: Column<InvoicesRow>;
    CustomerCompanyName: Column<InvoicesRow>;
    GSTIN: Column<InvoicesRow>;
    PlaceOfSupplyStateName: Column<InvoicesRow>;
    SupplyType: Column<InvoicesRow>;
    NetTaxableAmount: Column<InvoicesRow>;
    NetCGSTAmount: Column<InvoicesRow>;
    NetSGSTAmount: Column<InvoicesRow>;
    NetIGSTAmount: Column<InvoicesRow>;
    InvoiceAmount: Column<InvoicesRow>;
    RoundingOff: Column<InvoicesRow>;
    GrandTotal: Column<InvoicesRow>;
    SalesOrderNo: Column<InvoicesRow>;
    ProformaInvoiceNo: Column<InvoicesRow>;
    OrderRefNo: Column<InvoicesRow>;
    OrderRefDate: Column<InvoicesRow>;
    EWayBillNo: Column<InvoicesRow>;
    EWayBillDate: Column<InvoicesRow>;
    DeliveryNoteNo: Column<InvoicesRow>;
    PaymentTerms: Column<InvoicesRow>;
    PaymentDueDate: Column<InvoicesRow>;
    ShippingAddress: Column<InvoicesRow>;
    ShippedVia: Column<InvoicesRow>;
    ShippingDocketNo: Column<InvoicesRow>;
    VehicleNo: Column<InvoicesRow>;
    FinancialYearName: Column<InvoicesRow>;
    InvoiceMonth: Column<InvoicesRow>;
    TransactionStatus: Column<InvoicesRow>;
    InvoiceId: Column<InvoicesRow>;
    Inspection: Column<InvoicesRow>;
    UploadFiles: Column<InvoicesRow>;
    Remarks: Column<InvoicesRow>;
    ClientId: Column<InvoicesRow>;
    PreparedByUserUsername: Column<InvoicesRow>;
    PreparedDate: Column<InvoicesRow>;
    VerifiedByUserUsername: Column<InvoicesRow>;
    VerifiedDate: Column<InvoicesRow>;
    AuthorizedByUserUsername: Column<InvoicesRow>;
    AuthorizedDate: Column<InvoicesRow>;
    ModifiedByUserUsername: Column<InvoicesRow>;
    ModifiedDate: Column<InvoicesRow>;
    CancelledByUserUsername: Column<InvoicesRow>;
    CancelledDate: Column<InvoicesRow>;
    AuthorizedStatus: Column<InvoicesRow>;
}

export class InvoicesColumns extends ColumnsBase<InvoicesRow> {
    static readonly columnsKey = 'Default.Invoices';
    static readonly Fields = fieldsProxy<InvoicesColumns>();
}

[IndianNumberFormatter, TransactionStatus]; // referenced types