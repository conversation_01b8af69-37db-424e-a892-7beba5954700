﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { PoAmendmentDetailsRow } from "./PoAmendmentDetailsRow";

export interface PoAmendmentsRow {
    RowNumber?: number;
    POAmendmentId?: number;
    POAmendmentNo?: string;
    POAmendmentDate?: string;
    POAmendmentMonth?: string;
    FinancialYearId?: number;
    PurchaseOrderId?: number;
    PurchaseOrderDetailId?: number;
    VendorId?: number;
    VendorName?: string;
    GSTIN?: string;
    PlaceOfSupplyStateName?: string;
    PoAmendmentDetailsList?: PoAmendmentDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    POAmendmentAmount?: number;
    TDSRateId?: number;
    TCSRateId?: number;
    RoundingOff?: number;
    GrandTotal?: number;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    FinancialYearName?: string;
    PurchaseOrderNo?: string;
    PurchaseOrderDetailCommodityDescription?: string;
    TDSRateTransaction?: string;
    TCSRateNatureOfTransaction?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class PoAmendmentsRow {
    static readonly idProperty = 'POAmendmentId';
    static readonly nameProperty = 'POAmendmentNo';
    static readonly localTextPrefix = 'Default.PoAmendments';
    static readonly lookupKey = 'Default.PoAmendments';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PoAmendmentsRow>('Default.PoAmendments') }
    static async getLookupAsync() { return getLookupAsync<PoAmendmentsRow>('Default.PoAmendments') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PoAmendmentsRow>();
}