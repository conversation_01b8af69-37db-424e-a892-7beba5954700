﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { SalesReturnDetailsRow } from "./SalesReturnDetailsRow";
import { SalesReturnsRow } from "./SalesReturnsRow";

export namespace SalesReturnsService {
    export const baseUrl = 'Default/SalesReturns';

    export declare function Create(request: SaveRequest<SalesReturnsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SalesReturnsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SalesReturnsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SalesReturnsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SalesReturnsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesReturnsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromInvoiceDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<SalesReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesReturnDetailsRow>>;

    export const Methods = {
        Create: "Default/SalesReturns/Create",
        Update: "Default/SalesReturns/Update",
        Delete: "Default/SalesReturns/Delete",
        Retrieve: "Default/SalesReturns/Retrieve",
        List: "Default/SalesReturns/List",
        GetNextNumber: "Default/SalesReturns/GetNextNumber",
        GetFromInvoiceDetails: "Default/SalesReturns/GetFromInvoiceDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromInvoiceDetails'
    ].forEach(x => {
        (<any>SalesReturnsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}