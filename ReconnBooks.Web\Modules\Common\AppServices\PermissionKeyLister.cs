using ReconnBooks.Administration;

namespace ReconnBooks.AppServices;
public class PermissionKeyLister(ISqlConnections sqlConnections, ITwoLevelCache cache, ITypeSource typeSource)
    : BasePermissionKeyLister(cache, typeSource)
{
    protected override string GetCacheGroupKey()
    {
        return RoleRow.Fields.GenerationKey;
    }

    protected override IEnumerable<string> GetRoleKeys()
    {
        return RoleHelper.GetRoleById(cache, sqlConnections).Values
            .Select(x => x.RoleKey)
            .Where(x => !string.IsNullOrEmpty(x));
    }

    protected override IEnumerable<string> GetPrivatePermissions()
    {
        return [
            .. base.GetPrivatePermissions(),
            PermissionKeys.Clients.User,
            PermissionKeys.Clients.Admin
        ];
    }

    public override IEnumerable<string> ListPermissionKeys(bool includeRoles)
    {
        var permissionKeys =  base.ListPermissionKeys(includeRoles).ToList();

        permissionKeys.Remove(PermissionKeys.Clients.User);
        permissionKeys.Remove(PermissionKeys.Clients.Admin);
        //result.Remove(PermissionKeys.Consultants.User);
        permissionKeys.Remove(PermissionKeys.Consultants.Admin);

        return permissionKeys;
    }
}