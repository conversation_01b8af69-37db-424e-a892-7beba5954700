import { Decorators, BaseDialog } from "@serenity-is/corelib";

@Decorators.resizable()
@Decorators.maximizable()
@Decorators.registerClass('Serenity.Demo.BasicSamples.ChartInDialog')
export class Calculator<P = {}> extends BaseDialog<P> {

    //private canvas: HTMLCanvasElement;

    //protected onDialogOpen() {
    //    super.onDialogOpen();

    //    BasicSamplesService.OrdersByShipper({}, response => {
    //        new Chart(this.canvas, {
    //            type: "bar",
    //            data: {
    //                labels: response.Values.map(x => x.Month),
    //                datasets: response.ShipperKeys.map((shipperKey, shipperIdx) => ({
    //                    label: response.ShipperLabels[shipperIdx],
    //                    backgroundColor: chartColors[shipperIdx % chartColors.length],
    //                    data: response.Values.map((x, ix) => response.Values[ix][shipperKey])
    //                }))
    //            }
    //        });
    //    });
    //}

    protected renderContents(): any {
        return (
        //<iframe
        //    src="https://www.desmos.com/scientific"
        //    width="400"
        //    height="500"
        //    style="border: none;">
            //</iframe>
            <iframe
                src="https://www.desmos.com/scientific"
                width="100%"
                height="500"
                style="border: none; display: block; margin: 0 auto;">
            </iframe>
        );
    }

    protected getDialogOptions() {
        var opt = super.getDialogOptions();
        opt.title = 'Calculator';
        opt.modal = false;
        opt.backdrop = true;
        return opt;
    }
}