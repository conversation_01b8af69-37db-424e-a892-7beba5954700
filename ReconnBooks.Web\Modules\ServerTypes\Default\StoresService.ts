﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { StoresRow } from "./StoresRow";

export namespace StoresService {
    export const baseUrl = 'Default/Stores';

    export declare function Create(request: SaveRequest<StoresRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<StoresRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<StoresRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<StoresRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<StoresRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<StoresRow>>;

    export const Methods = {
        Create: "Default/Stores/Create",
        Update: "Default/Stores/Update",
        Delete: "Default/Stores/Delete",
        Retrieve: "Default/Stores/Retrieve",
        List: "Default/Stores/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>StoresService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}