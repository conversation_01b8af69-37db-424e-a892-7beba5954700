using ReconnBooks.Modules.Common.Helpers;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("HSNSummary")]
[DisplayName("HSN Code wise Summary"), InstanceName("Hsn Summary"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class HsnSummaryRow : Row<HsnSummaryRow.RowFields>, IIdRow, INameRow
{
    const string jFinancialYear = nameof(jFinancialYear);

    [DisplayName("Hsn Summary Id"), Column("HSNSummaryId"), PrimaryKey, NotNull]
    public int? HsnSummaryId { get => fields.HsnSummaryId[this]; set => fields.HsnSummaryId[this] = value; }

    [DisplayName("Financial Year"), NotNull, ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }

    [DisplayName("Financial Year"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName)), LookupInclude]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }

    [DisplayName("Month"), Expression("FORMAT(InvoiceDate, 'MMM')"), QuickFilter]
    [LookupEditor(typeof(MonthLookup))]
    public string InvoiceMonth { get => fields.InvoiceMonth[this]; set => fields.InvoiceMonth[this] = value; }

    [DisplayName("Invoice Id")]
    public int? InvoiceId { get => fields.InvoiceId[this]; set => fields.InvoiceId[this] = value; }

    [DisplayName("Invoice Detail Id")]
    public int? InvoiceDetailId { get => fields.InvoiceDetailId[this]; set => fields.InvoiceDetailId[this] = value; }

    [DisplayName("Description"), QuickSearch, NameProperty]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("HSN Code"), IdProperty]
    public string HsnCode { get => fields.HsnCode[this]; set => fields.HsnCode[this] = value; }

    //[DisplayName("HSN Code"), ]
    //public string HsnCodeId { get => fields.HsnCodeId[this]; set => fields.HsnCodeId[this] = value; }

    [DisplayName("HSN Description")]
    public string HsnDescription { get => fields.HsnDescription[this]; set => fields.HsnDescription[this] = value; }

    [DisplayName("Quantity")]
    public decimal? Quantity { get => fields.Quantity[this]; set => fields.Quantity[this] = value; }

    [DisplayName("UQC")]
    public string UQC { get => fields.UQC[this]; set => fields.UQC[this] = value; }

    [DisplayName("Taxable Value")]
    public decimal? TaxableValue { get => fields.TaxableValue[this]; set => fields.TaxableValue[this] = value; }

    [DisplayName("CGST")]
    public decimal? Cgst { get => fields.Cgst[this]; set => fields.Cgst[this] = value; }

    [DisplayName("SGST")]
    public decimal? Sgst { get => fields.Sgst[this]; set => fields.Sgst[this] = value; }

    [DisplayName("IGST")]
    public decimal? Igst { get => fields.Igst[this]; set => fields.Igst[this] = value; }

    [DisplayName("CESS")]
    public decimal? Cess { get => fields.Cess[this]; set => fields.Cess[this] = value; }

    [DisplayName("Net Amount")]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }

    [DisplayName("Invoices")]
    public List<HsnSummaryDetailsRow> Invoices { get => fields.Invoices[this]; set => fields.Invoices[this] = value; } 

}