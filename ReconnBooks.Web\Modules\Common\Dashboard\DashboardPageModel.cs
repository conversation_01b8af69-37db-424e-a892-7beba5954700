namespace ReconnBooks.Common;

[ScriptInclude]
public class DashboardPageModel
{
    public int OpenOrders { get; set; }
    public int ClosedOrderPercent { get; set; }
    public int ProductCount { get; set; }
    public string SalesCount { get; set; }
    public string VendorBillsCount { get; set; }
    public string VendorBillsTotal { get; set; }
    public string CustomerCount { get; set; }
    public string SuppliersCount { get; set; }
    public string PendingInvoicesCount { get; set; }
    public string PendingVendorBillsCount { get; set; }
    public string InvoicesTotal { get; set; }
    public string ReceiptsTotal { get; set; }
    public List<DataPoint> InvoicesDataPoints { get; set; } = new();
    public List<DataPoint> ReceiptsDataPoints { get; set; } = new();

    public class DataPoint
    {
        public string Month { get; set; }
        public decimal InvoiceTotal { get; set; }
        public decimal ReceiptTotal { get; set; }

    }
}