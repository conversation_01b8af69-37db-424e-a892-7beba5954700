﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface FootNotesRow {
    RowNumber?: number;
    FootNoteId?: number;
    FootNote?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class FootNotesRow {
    static readonly idProperty = 'FootNoteId';
    static readonly nameProperty = 'FootNote';
    static readonly localTextPrefix = 'Default.FootNotes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<FootNotesRow>();
}