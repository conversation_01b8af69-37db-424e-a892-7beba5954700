﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { WarehousesRow } from "./WarehousesRow";

export interface WarehousesColumns {
    RowNumber: Column<WarehousesRow>;
    WarehouseName: Column<WarehousesRow>;
    Description: Column<WarehousesRow>;
    LocationName: Column<WarehousesRow>;
    Remarks: Column<WarehousesRow>;
    Discontinued: Column<WarehousesRow>;
    ClientId: Column<WarehousesRow>;
    WarehouseId: Column<WarehousesRow>;
}

export class WarehousesColumns extends ColumnsBase<WarehousesRow> {
    static readonly columnsKey = 'Default.Warehouses';
    static readonly Fields = fieldsProxy<WarehousesColumns>();
}