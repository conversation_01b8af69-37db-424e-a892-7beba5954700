using ReconnBooks.Modules.Default.ClientUsers;
using Serenity.Reporting;
using System.Globalization;
using MyRow = ReconnBooks.Default.ClientUsersRow;
using System.Threading.Tasks;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/ClientUsers/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class ClientUsersEndpoint : ServiceEndpoint
{
    private readonly ISqlConnections sqlConnections;
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;

    public ClientUsersEndpoint(ISqlConnections sqlConnections, IUserAccessor userAccessor, IUserRetrieveService userRetriever)
    {
        this.sqlConnections = sqlConnections;
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
    }

    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IClientUsersSaveHandler handler)
    {
        return handler.Create(uow, request);
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public async Task<SaveResponse> Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IClientUsersSaveHandler handler,
        [FromServices] IClientUsersListHandler listHandler,
        [FromServices] IClientUsersRetrieveHandler retrieveHandler,
        [FromServices] ITwoLevelCache cache,
        [FromServices] IReplaceableUserClaimCreator replaceableClaimCreator)
    {
        SaveResponse oldSaveRespone = new SaveResponse(); //We need this variable to return old response if there is something wrong while updating with new clientID
        
        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return oldSaveRespone;
        }


        var userClientsRowFields = MyRow.Fields;
        var activeUserClientRecord = this.GetActiveUserClientsRow(listHandler);

        if (activeUserClientRecord != null)
        {
            activeUserClientRecord.Status = false;
            oldSaveRespone = handler.Update(uow, new SaveRequest<MyRow> { Entity = activeUserClientRecord });
        }

        var newUserClientRecord = retrieveHandler.Retrieve(uow.Connection, new RetrieveRequest { EntityId = Convert.ToInt32(request.EntityId) }).Entity;

        if (newUserClientRecord != null)
        {
            newUserClientRecord.Status = true;

            //TODO: what should be done if newUserClientRecord == null?
            var response = handler.Update(uow, new SaveRequest<MyRow> { Entity = newUserClientRecord });

            //cache.Remove("UserByName_" + username);

            //var userId = Convert.ToInt32(userAccessor.User.GetIdentifier(), CultureInfo.InvariantCulture);

            //cache.Remove($"UserPermissions:{userId}");
            //cache.Remove($"ScriptUserPermissions:{userId}");

            //cache.Remove("PermissionsUsedFromScript");
            //Cache.Remove("DynamicScript:RemoteData.Administration.PermissionKeys");



            await replaceableClaimCreator.ReplaceClaimAsync("ClientId", newUserClientRecord.ClientId.ToString());

            userRetriever.RemoveCachedUser(user, cache);


            return response;
        }

        return oldSaveRespone;
    }

    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] IClientUsersDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] IClientUsersRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] IClientUsersListHandler handler)
    {
        return handler.List(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] IClientUsersListHandler handler,
        [FromServices] IExcelExporter exporter)
    {
        var data = List(connection, request, handler).Entities;
        var bytes = exporter.Export(data, typeof(Columns.ClientUsersColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "ClientUsersList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }

    public ClientUserServiceResponse ActiveClient([FromServices] IClientUsersListHandler handler)
    {
        MyRow activeClienUsertRecord = null;

        activeClienUsertRecord = GetActiveUserClientsRow(handler);

        return new ClientUserServiceResponse
        {
            ClientName = activeClienUsertRecord?.ClientName ?? string.Empty,
            ClientCode = activeClienUsertRecord?.ClientName ?? string.Empty,
            ClientId = activeClienUsertRecord?.ClientUserId.GetValueOrDefault() ?? 0,
            ClientLogo = activeClienUsertRecord?.ClientLogo ?? string.Empty
        };
    }

    private MyRow GetActiveUserClientsRow(IClientUsersListHandler handler)
    {
        return List(sqlConnections.NewFor<MyRow>(), new ListRequest
        {
            Criteria = new Criteria("UserId") == userAccessor.User?.GetUserDefinition(userRetriever).Id
                                & new Criteria("Status") == 1,
            IncludeColumns = new HashSet<string> { "ClientName", "ClientName", "ClientLogo" }
        }, handler)
        .Entities.FirstOrDefault();
    }

    public ListResponse<MyRow> GetListOfUserClients(IDbConnection connection, RetrieveRequest request)
    {
        return new ListResponse<MyRow> { Entities = connection.List<MyRow>(new Criteria(MyRow.Fields.UserId) == Convert.ToInt32(request.EntityId)) };
    }
}