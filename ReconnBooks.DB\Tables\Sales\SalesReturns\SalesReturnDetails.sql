﻿CREATE TABLE [dbo].[SalesReturnDetails](
    [SalesReturnDetailId]   INT				NOT NULL	IDENTITY (1, 1),
    [SalesReturnId]			INT				NOT NULL,
    [DeliveryNoteDetailId]  INT                 NULL,

    [CommodityTypeId]       INT			    NOT NULL,
    [CommodityId]           BIGINT			NOT NULL,
	[CommodityDescription]  NVARCHAR (MAX)      NULL,

    [InvoiceDetailId]       INT                 NULL,
    [GRNDetailId]           INT                 NULL,
    --Fetch Invoice Item and GRN Item Details 
    [InvoiceQuantity]        DECIMAL (18, 2) NOT NULL,
    [InvoiceUnitId]          INT             NOT NULL,
    [ReceivedQuantity]		DECIMAL (18,2)	    NULL,   --GRN Details Received Quantity
    [ReceivedUnitId]	    INT				    NULL,   --GRN Details Received Unit
    [SerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader. Multiple Sl.Nos are possible

    --[RevisedQuantity]       DECIMAL (18, 2)    NULL,
    
    [UnitPrice]             DECIMAL (18, 2) NOT NULL,   
    [DiscountPercent]       DECIMAL (18, 2)     NULL,   
    [DiscountAmountPerUnit] DECIMAL (18, 2)     NULL,
    [NetDiscountAmount]     DECIMAL (18, 2)     NULL,
        
    [GSTRateId]             INT                 NULL,
    [IGSTRate]              DECIMAL (18, 2)     NULL,      
    [CGSTRate]              DECIMAL (18, 2)     NULL,      
    [SGSTRate]              DECIMAL (18, 2)     NULL,      

    [DummyField]            DECIMAL (18, 2) NULL,
    [NetPricePerUnit]       DECIMAL (18, 2)     NULL,   -- Per Unit Price = Net Amount/Quantity
    [NetAmount]             DECIMAL (18, 2)     NULL,   -- Net Amount = Net Price x Quantity
    
    --Sales Return Item Details---------------
    [RejectedQuantity]      DECIMAL (18, 2) NOT NULL,
    [RejectedUnitId]        INT             NOT NULL,
    [RejectedItemSerialNo]  NVARCHAR (MAX)  NULL,
    
    [RejectionReasonId]     INT             NULL,
    [AssessmentRemarks]     NVARCHAR (MAX)  NULL,
    [ReplacementMethodId]   INT             NULL,
    [Remarks]               NVARCHAR (MAX)  NULL,

    CONSTRAINT [PK_SalesReturnDetails] PRIMARY KEY CLUSTERED ([SalesReturnDetailId] ASC),
    CONSTRAINT [FK_SalesReturnDetails_SalesReturns] FOREIGN KEY ([SalesReturnId]) REFERENCES [dbo].[SalesReturns] ([SalesReturnId]),
    CONSTRAINT [FK_SalesReturnDetails_DNDetails] FOREIGN KEY ([DeliveryNoteDetailId]) REFERENCES [dbo].[DeliveryNoteDetails] ([DeliveryNoteDetailId]),
    CONSTRAINT [FK_SalesReturnDetails_InvoiceDetails] FOREIGN KEY ([InvoiceDetailId]) REFERENCES [dbo].[InvoiceDetails] ([InvoiceDetailId]),
    CONSTRAINT [FK_SalesReturnDetails_RejectionReasons] FOREIGN KEY ([RejectionReasonId]) REFERENCES [dbo].[RejectionReasons] ([RejectionReasonId]),
    CONSTRAINT [FK_SalesReturnDetails_ReplacementMethods] FOREIGN KEY ([ReplacementMethodId]) REFERENCES [dbo].[ReplacementMethods] ([ReplacementMethodId]),
    CONSTRAINT [FK_SalesReturnDetails_GSTRates] FOREIGN KEY ([GSTRateId]) REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_SalesReturnDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_SalesReturnDetails_GRNDetails] FOREIGN KEY ([GRNDetailId]) REFERENCES [dbo].[GRNDetails] ([GRNDetailId]),
    CONSTRAINT [FK_SalesReturnDetails_InvoiceUnits] FOREIGN KEY ([InvoiceUnitId]) REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_SalesReturnDetails_RejectedUnits] FOREIGN KEY ([RejectedUnitId]) REFERENCES [dbo].[Units] ([UnitId])
);
GO
CREATE NONCLUSTERED INDEX [SalesReturns]
    ON [dbo].[SalesReturnDetails]([SalesReturnId] ASC);
GO
CREATE NONCLUSTERED INDEX [DeliveryNoteDetails]
    ON [dbo].[SalesReturnDetails]([DeliveryNoteDetailId] ASC);
GO
CREATE NONCLUSTERED INDEX [InvoiceDetails]
    ON [dbo].[SalesReturnDetails]([InvoiceDetailId] ASC);
    
GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[SalesReturnDetails]([CommodityId] ASC);

