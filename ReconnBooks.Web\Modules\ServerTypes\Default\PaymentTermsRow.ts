﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface PaymentTermsRow {
    RowNumber?: number;
    PaymentTermsId?: number;
    PaymentTerms?: string;
}

export abstract class PaymentTermsRow {
    static readonly idProperty = 'PaymentTermsId';
    static readonly nameProperty = 'PaymentTerms';
    static readonly localTextPrefix = 'Default.PaymentTerms';
    static readonly lookupKey = 'Default.PaymentTerms';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PaymentTermsRow>('Default.PaymentTerms') }
    static async getLookupAsync() { return getLookupAsync<PaymentTermsRow>('Default.PaymentTerms') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PaymentTermsRow>();
}