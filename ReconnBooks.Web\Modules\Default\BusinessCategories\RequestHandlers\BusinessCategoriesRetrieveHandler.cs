﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.BusinessCategoriesRow>;
using MyRow = ReconnBooks.Default.BusinessCategoriesRow;

namespace ReconnBooks.Default;

public interface IBusinessCategoriesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessCategoriesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessCategoriesRetrieveHandler
{
    public BusinessCategoriesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}