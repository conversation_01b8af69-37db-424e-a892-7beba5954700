﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ProductTypesRow {
    RowNumber?: number;
    ProductTypeId?: number;
    ProductType?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class ProductTypesRow {
    static readonly idProperty = 'ProductTypeId';
    static readonly nameProperty = 'ProductType';
    static readonly localTextPrefix = 'Default.ProductTypes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProductTypesRow>();
}