﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { HsnSummaryRetrieveRequest } from "./HsnSummaryRetrieveRequest";
import { HsnSummaryRow } from "./HsnSummaryRow";

export namespace HsnSummaryService {
    export const baseUrl = 'Default/HsnSummary';

    export declare function Create(request: SaveRequest<HsnSummaryRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<HsnSummaryRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: HsnSummaryRetrieveRequest, onSuccess?: (response: RetrieveResponse<HsnSummaryRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<HsnSummaryRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<HsnSummaryRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<HsnSummaryRow>>;

    export const Methods = {
        Create: "Default/HsnSummary/Create",
        Update: "Default/HsnSummary/Update",
        Delete: "Default/HsnSummary/Delete",
        Retrieve: "Default/HsnSummary/Retrieve",
        List: "Default/HsnSummary/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>HsnSummaryService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}