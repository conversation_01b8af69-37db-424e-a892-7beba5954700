﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { SupplyTypesRow } from "./SupplyTypesRow";

export namespace SupplyTypesService {
    export const baseUrl = 'Default/SupplyTypes';

    export declare function Create(request: SaveRequest<SupplyTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SupplyTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SupplyTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SupplyTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SupplyTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SupplyTypesRow>>;

    export const Methods = {
        Create: "Default/SupplyTypes/Create",
        Update: "Default/SupplyTypes/Update",
        Delete: "Default/SupplyTypes/Delete",
        Retrieve: "Default/SupplyTypes/Retrieve",
        List: "Default/SupplyTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>SupplyTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}