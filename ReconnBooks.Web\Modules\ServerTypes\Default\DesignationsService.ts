﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { DesignationsRow } from "./DesignationsRow";

export namespace DesignationsService {
    export const baseUrl = 'Default/Designations';

    export declare function Create(request: SaveRequest<DesignationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DesignationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DesignationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DesignationsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DesignationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DesignationsRow>>;

    export const Methods = {
        Create: "Default/Designations/Create",
        Update: "Default/Designations/Update",
        Delete: "Default/Designations/Delete",
        Retrieve: "Default/Designations/Retrieve",
        List: "Default/Designations/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>DesignationsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}