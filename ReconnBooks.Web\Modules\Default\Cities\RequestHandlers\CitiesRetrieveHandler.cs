﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.CitiesRow>;
using MyRow = ReconnBooks.Default.CitiesRow;

namespace ReconnBooks.Default;

public interface ICitiesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class CitiesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, ICitiesRetrieveHandler
{
    public CitiesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}