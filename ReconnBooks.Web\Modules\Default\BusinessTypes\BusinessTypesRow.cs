using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("BusinessTypes")]
[DisplayName("Business Types"), InstanceName("Business Types"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class BusinessTypesRow : Row<BusinessTypesRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Business Type Id"), Identity, IdProperty,Hidden]
    public int? BusinessTypeId { get => fields.BusinessTypeId[this]; set => fields.BusinessTypeId[this] = value; }

    [DisplayName("Business Type"), Size(150), NotNull, QuickSearch, NameProperty]
    public string BusinessType { get => fields.BusinessType[this]; set => fields.BusinessType[this] = value; }

    [DisplayName("Description"), Size(500)]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }
}