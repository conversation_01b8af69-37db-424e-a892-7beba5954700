﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { DeliveryNoteDetailsRow } from "./DeliveryNoteDetailsRow";

export interface DeliveryNotesRow {
    RowNumber?: number;
    DeliveryNoteId?: number;
    DeliveryNoteNo?: string;
    DeliveryNoteDate?: string;
    DeliveryNoteMonth?: string;
    CustomerId?: number;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    CustomerEMailId?: string;
    PlaceOfSupplyStateName?: string;
    ShipToCustomerId?: number;
    ShipToCustomerName?: string;
    ShippingAddress?: string;
    ShippingCityName?: string;
    ShippingPinCode?: string;
    ShippingGSTIN?: string;
    ShippingPlaceOfSupplyStateName?: string;
    SupplyTypeId?: number;
    FinancialYearId?: number;
    ClientName?: string;
    OrderRefNo?: string;
    OrderRefDate?: string;
    SalesOrderId?: number;
    QuotationId?: number;
    ReasonToTransport?: string;
    DeliveryNoteDetailsList?: DeliveryNoteDetailsRow[];
    CGSTAmount?: number;
    SGSTAmount?: number;
    IGSTAmount?: number;
    EWayBillNo?: string;
    EWayBillNoDate?: string;
    ShippedVia?: string;
    ShippingDocketNo?: string;
    VehicleNo?: string;
    UploadDocketCopy?: string;
    Inspection?: string;
    Remarks?: string;
    ClientId?: number;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    SupplyType?: string;
    FinancialYearName?: string;
    SalesOrderNo?: string;
    SalesOrderRefNo?: string;
    SalesOrderRefDate?: string;
    QuotationNo?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class DeliveryNotesRow {
    static readonly idProperty = 'DeliveryNoteId';
    static readonly nameProperty = 'DeliveryNoteNo';
    static readonly localTextPrefix = 'Default.DeliveryNotes';
    static readonly lookupKey = 'Default.DeliveryNotes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<DeliveryNotesRow>('Default.DeliveryNotes') }
    static async getLookupAsync() { return getLookupAsync<DeliveryNotesRow>('Default.DeliveryNotes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DeliveryNotesRow>();
}