﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { SalesOrderDetailsRow } from "./SalesOrderDetailsRow";

export interface SalesOrdersRow {
    RowNumber?: number;
    SalesOrderId?: number;
    SalesOrderNo?: string;
    SalesOrderDate?: string;
    SalesOrderMonth?: string;
    CustomerId?: number;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    CustomerEMailId?: string;
    PlaceOfSupplyStateName?: string;
    ShipToCustomerId?: number;
    ShipToCustomerName?: string;
    ShippingAddress?: string;
    ShippingCityName?: string;
    ShippingPinCode?: string;
    ShippingGSTIN?: string;
    ShippingPlaceOfSupplyStateName?: string;
    SalesOrderDetailsList?: SalesOrderDetailsRow[];
    NetTaxableAmount?: number;
    NetIGSTAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    SupplyTypeId?: number;
    QuotationId?: number;
    QuotationNo?: string;
    OrderRefNo?: string;
    OrderRefDate?: string;
    GrandTotal?: number;
    PaymentTermsId?: number;
    FinancialYearId?: number;
    ClientId?: number;
    DeliveryDueDate?: string;
    UploadOrderCopy?: string;
    Inspection?: string;
    Remarks?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    SupplyType?: string;
    FinancialYearName?: string;
    PaymentTerms?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
    ClientName?: string;
}

export abstract class SalesOrdersRow {
    static readonly idProperty = 'SalesOrderId';
    static readonly nameProperty = 'SalesOrderNo';
    static readonly localTextPrefix = 'Default.SalesOrders';
    static readonly lookupKey = 'Default.SalesOrders';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<SalesOrdersRow>('Default.SalesOrders') }
    static async getLookupAsync() { return getLookupAsync<SalesOrdersRow>('Default.SalesOrders') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SalesOrdersRow>();
}