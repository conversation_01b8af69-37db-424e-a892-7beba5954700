﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.CommoditiesRow;

namespace ReconnBooks.Default;

public interface ICommoditiesDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class CommoditiesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, ICommoditiesDeleteHandler
{
    public CommoditiesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}