﻿import { fieldsProxy } from "@serenity-is/corelib";
import { DebitNoteDetailsRow } from "./DebitNoteDetailsRow";

export interface DebitNotesRow {
    RowNumber?: number;
    DebitNoteId?: number;
    DebitNoteNo?: string;
    DebitNoteDate?: string;
    DebitNoteMonth?: string;
    VendorId?: number;
    PlaceOfSupplyStateName?: string;
    FinancialYearId?: number;
    PurchaseOrderId?: number;
    PurchaseReturnId?: number;
    DebitNoteDetailsList?: DebitNoteDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    DebitNoteAmount?: number;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    VendorName?: string;
    FinancialYearName?: string;
    PurchaseOrderNo?: string;
    PurchaseReturnNo?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class DebitNotesRow {
    static readonly idProperty = 'DebitNoteId';
    static readonly nameProperty = 'DebitNoteNo';
    static readonly localTextPrefix = 'Default.DebitNotes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DebitNotesRow>();
}