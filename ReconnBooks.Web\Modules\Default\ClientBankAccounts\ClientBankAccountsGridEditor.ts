import { ClientBankAccountsColumns, ClientBankAccountsRow } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { GridEditorBase } from '@serenity-is/extensions';
import { ClientBankAccountsDialog } from './ClientBankAccountsDialog';

@Decorators.registerEditor('ReconnBooks.Default.ClientBankAccountsGridEditor')
export class ClientBankAccountsGridEditor extends GridEditorBase<ClientBankAccountsRow> {
    protected getColumnsKey() { return ClientBankAccountsColumns.columnsKey; }
    protected getDialogType() { return ClientBankAccountsDialog; }
    protected getLocalTextDbPrefix() { return ClientBankAccountsRow.localTextPrefix; }

    
    protected getAddButtonCaption() {
        return "Add";
    }

}