﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface DocumentsRow {
    RowNumber?: number;
    DocumentId?: number;
    DocumentName?: string;
    DocumentShortName?: string;
}

export abstract class DocumentsRow {
    static readonly idProperty = 'DocumentId';
    static readonly nameProperty = 'DocumentName';
    static readonly localTextPrefix = 'Default.Documents';
    static readonly lookupKey = 'Default.Documents';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<DocumentsRow>('Default.Documents') }
    static async getLookupAsync() { return getLookupAsync<DocumentsRow>('Default.Documents') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<DocumentsRow>();
}