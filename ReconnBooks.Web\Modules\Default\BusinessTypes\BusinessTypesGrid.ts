import { BusinessTypesColumns, BusinessTypesRow, BusinessTypesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid } from '@serenity-is/corelib';
import { BusinessTypesDialog } from './BusinessTypesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.BusinessTypesGrid')
export class BusinessTypesGrid extends EntityGridDialog<BusinessTypesRow, any> {
    protected getColumnsKey() { return BusinessTypesColumns.columnsKey; }
    protected getDialogType() { return BusinessTypesDialog; }
    protected getRowDefinition() { return BusinessTypesRow; }
    protected getService() { return BusinessTypesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}