﻿import { StringEditor, ServiceLookupEditor, LookupEditor, MultipleImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { AddressedToDialog } from "../../Default/AddressedTo/AddressedToDialog";
import { BanksDialog } from "../../Default/Banks/BanksDialog";
import { CitiesDialog } from "../../Default/Cities/CitiesDialog";
import { VendorContactsGridEditor } from "../../Default/VendorContacts/VendorContactsGridEditor";

export interface VendorsForm {
    VendorName: StringEditor;
    ShortName: StringEditor;
    AddressedToId: ServiceLookupEditor;
    BillingAddress: StringEditor;
    BillingCityId: ServiceLookupEditor;
    BillingPinCode: StringEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyId: ServiceLookupEditor;
    NatureOfSupplyId: ServiceLookupEditor;
    SupplyTypeId: ServiceLookupEditor;
    PAN: StringEditor;
    TAN: StringEditor;
    UdyamNo: StringEditor;
    IECNo: StringEditor;
    CINNo: StringEditor;
    CorrespondenceAddress: StringEditor;
    CorrespondenceCityId: LookupEditor;
    CorrespondencePinCode: StringEditor;
    PhoneNo: StringEditor;
    MobileNo: StringEditor;
    EMailId: StringEditor;
    FaxNo: StringEditor;
    HomePage: StringEditor;
    VendorContactsList: VendorContactsGridEditor;
    BankId: ServiceLookupEditor;
    BranchName: StringEditor;
    AccountName: StringEditor;
    AccountNumber: StringEditor;
    IFSCCode: StringEditor;
    BranchCode: StringEditor;
    UploadFiles: MultipleImageUploadEditor;
}

export class VendorsForm extends PrefixedContext {
    static readonly formKey = 'Default.Vendors';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!VendorsForm.init)  {
            VendorsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = LookupEditor;
            var w3 = VendorContactsGridEditor;
            var w4 = MultipleImageUploadEditor;

            initFormType(VendorsForm, [
                'VendorName', w0,
                'ShortName', w0,
                'AddressedToId', w1,
                'BillingAddress', w0,
                'BillingCityId', w1,
                'BillingPinCode', w0,
                'GSTIN', w0,
                'PlaceOfSupplyId', w1,
                'NatureOfSupplyId', w1,
                'SupplyTypeId', w1,
                'PAN', w0,
                'TAN', w0,
                'UdyamNo', w0,
                'IECNo', w0,
                'CINNo', w0,
                'CorrespondenceAddress', w0,
                'CorrespondenceCityId', w2,
                'CorrespondencePinCode', w0,
                'PhoneNo', w0,
                'MobileNo', w0,
                'EMailId', w0,
                'FaxNo', w0,
                'HomePage', w0,
                'VendorContactsList', w3,
                'BankId', w1,
                'BranchName', w0,
                'AccountName', w0,
                'AccountNumber', w0,
                'IFSCCode', w0,
                'BranchCode', w0,
                'UploadFiles', w4
            ]);
        }
    }
}

queueMicrotask(() => [AddressedToDialog, CitiesDialog, BanksDialog]); // referenced dialogs