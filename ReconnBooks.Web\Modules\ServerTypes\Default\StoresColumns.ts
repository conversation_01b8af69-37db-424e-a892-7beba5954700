﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { StoresRow } from "./StoresRow";

export interface StoresColumns {
    RowNumber: Column<StoresRow>;
    StoreName: Column<StoresRow>;
    StoreDescription: Column<StoresRow>;
    Remarks: Column<StoresRow>;
    LocationName: Column<StoresRow>;
    WarehouseName: Column<StoresRow>;
    SetDefault: Column<StoresRow>;
    Discontinued: Column<StoresRow>;
    ClientId: Column<StoresRow>;
    StoreId: Column<StoresRow>;
}

export class StoresColumns extends ColumnsBase<StoresRow> {
    static readonly columnsKey = 'Default.Stores';
    static readonly Fields = fieldsProxy<StoresColumns>();
}