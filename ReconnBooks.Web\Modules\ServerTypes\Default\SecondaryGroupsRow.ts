﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface SecondaryGroupsRow {
    RowNumber?: number;
    SecondaryGroupId?: number;
    SecondaryGroupCode?: number;
    SecondaryGroupName?: string;
    PrimaryGroupId?: number;
    Remarks?: string;
    PrimaryGroupName?: string;
}

export abstract class SecondaryGroupsRow {
    static readonly idProperty = 'SecondaryGroupId';
    static readonly nameProperty = 'SecondaryGroupName';
    static readonly localTextPrefix = 'Default.SecondaryGroups';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SecondaryGroupsRow>();
}