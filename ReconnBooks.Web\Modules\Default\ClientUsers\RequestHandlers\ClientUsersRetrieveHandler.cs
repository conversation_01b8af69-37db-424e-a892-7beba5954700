using PuppeteerSharp;
using ReconnBooks.Modules.Default.ClientUsers.RequestHandlers;
using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.ClientUsersRow>;
using MyRow = ReconnBooks.Default.ClientUsersRow;

namespace ReconnBooks.Default;

public interface IClientUsersRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientUsersRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IClientUsersRetrieveHandler
{
    private readonly ISqlConnections sqlConnections;
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly IPermissionService permissionService;

    public ClientUsersRetrieveHandler(IRequestContext context, ISqlConnections sqlConnections,
        IUserAccessor userAccessor, IUserRetrieveService userRetriever,
        IPermissionService permissionService)
            : base(context)
    {
        this.sqlConnections = sqlConnections;
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.permissionService = permissionService;
    }

    protected override void PrepareQuery(SqlQuery query)
    {
        base.PrepareQuery(query);
        ConsultantFilterQuery.PrepareRetrieveQuery(query, permissionService, userRetriever, userAccessor);
    }
}