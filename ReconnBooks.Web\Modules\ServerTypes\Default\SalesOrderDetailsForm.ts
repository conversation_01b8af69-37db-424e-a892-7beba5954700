﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { UnitsDialog } from "../../Default/Units/UnitsDialog";

export interface SalesOrderDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    OfferQuantity: DecimalEditor;
    OfferUnitId: ServiceLookupEditor;
    OfferPrice: DecimalEditor;
    OrderQuantity: DecimalEditor;
    OrderUnitId: ServiceLookupEditor;
    OrderUnitPrice: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmountPerUnit: DecimalEditor;
    NetDiscountAmount: DecimalEditor;
    OrderUnitAmount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: DecimalEditor;
    PricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
}

export class SalesOrderDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.SalesOrderDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SalesOrderDetailsForm.init)  {
            SalesOrderDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;

            initFormType(SalesOrderDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'OfferQuantity', w4,
                'OfferUnitId', w0,
                'OfferPrice', w4,
                'OrderQuantity', w4,
                'OrderUnitId', w0,
                'OrderUnitPrice', w4,
                'DiscountPercent', w4,
                'DiscountAmountPerUnit', w4,
                'NetDiscountAmount', w4,
                'OrderUnitAmount', w4,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w4,
                'NetTaxableAmount', w4,
                'IGSTRate', w4,
                'IGSTAmountPerUnit', w4,
                'NetIGSTAmount', w4,
                'CGSTRate', w4,
                'CGSTAmountPerUnit', w4,
                'NetCGSTAmount', w4,
                'SGSTRate', w4,
                'SGSTAmountPerUnit', w4,
                'NetSGSTAmount', w4,
                'DummyField', w4,
                'PricePerUnit', w4,
                'NetAmount', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, UnitsDialog]); // referenced dialogs