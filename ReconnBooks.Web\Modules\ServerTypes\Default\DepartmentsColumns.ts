﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { DepartmentsRow } from "./DepartmentsRow";

export interface DepartmentsColumns {
    RowNumber: Column<DepartmentsRow>;
    DepartmentId: Column<DepartmentsRow>;
    DepartmentName: Column<DepartmentsRow>;
    Description: Column<DepartmentsRow>;
}

export class DepartmentsColumns extends ColumnsBase<DepartmentsRow> {
    static readonly columnsKey = 'Default.Departments';
    static readonly Fields = fieldsProxy<DepartmentsColumns>();
}