﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.BusinessGroupsRow>;
using MyRow = ReconnBooks.Default.BusinessGroupsRow;

namespace ReconnBooks.Default;

public interface IBusinessGroupsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessGroupsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessGroupsListHandler
{
    public BusinessGroupsListHandler(IRequestContext context)
            : base(context)
    {
    }
}