using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.Banks")]
[BasedOnRow(typeof(BanksRow), CheckNames = true)]
public class BanksColumns
{
    public long RowNumber { get; set; }

    [EditLink, Width(400)]
    public string BankName { get; set; }

    [Width(150), DisplayName("Short Name")]
    public string BankShortName { get; set; }

    [DisplayName("Db.Shared.RecordId"), Width(50), SortOrder(1, descending: false), AlignCenter]
    public int BankId { get; set; }
}