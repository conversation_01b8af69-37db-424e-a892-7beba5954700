﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { ProformaInvoiceDetailsRow } from "./ProformaInvoiceDetailsRow";

export interface ProformaInvoicesRow {
    RowNumber?: number;
    ProformaInvoiceId?: number;
    ProformaInvoiceNo?: string;
    ProformaInvoiceDate?: string;
    ProformaInvoiceMonth?: string;
    CustomerId?: number;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    CustomerEMailId?: string;
    PlaceOfSupplyStateName?: string;
    SupplyTypeId?: number;
    QuotationId?: number;
    SalesOrderId?: number;
    OrderRefNo?: string;
    OrderRefDate?: string;
    ShipToCustomerId?: number;
    ShipToCustomerName?: string;
    ShippingAddress?: string;
    ShippingCityName?: string;
    ShippingPinCode?: string;
    ShippingGSTIN?: string;
    ShippingPlaceOfSupplyStateName?: string;
    ProformaInvoiceDetailsList?: ProformaInvoiceDetailsRow[];
    NetTaxableAmount?: number;
    NetCGSTAmount?: number;
    NetSGSTAmount?: number;
    NetIGSTAmount?: number;
    ProformaInvoiceAmt?: number;
    RoundingOff?: number;
    DummyField?: number;
    GrandTotal?: number;
    DeliveryNoteId?: number;
    PaymentTermsId?: number;
    PaymentDueDate?: string;
    ShippedVia?: string;
    DocketNo?: string;
    VehicleNo?: string;
    UploadFiles?: string;
    Inspection?: string;
    FinancialYearId?: number;
    Remarks?: string;
    ClientId?: number;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    SupplyType?: string;
    QuotationNo?: string;
    SalesOrderNo?: string;
    DeliveryNoteNo?: string;
    PaymentTerms?: string;
    FinancialYearName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
    ClientName?: string;
}

export abstract class ProformaInvoicesRow {
    static readonly idProperty = 'ProformaInvoiceId';
    static readonly nameProperty = 'ProformaInvoiceNo';
    static readonly localTextPrefix = 'Default.ProformaInvoices';
    static readonly lookupKey = 'Default.ProformaInvoices';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ProformaInvoicesRow>('Default.ProformaInvoices') }
    static async getLookupAsync() { return getLookupAsync<ProformaInvoicesRow>('Default.ProformaInvoices') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProformaInvoicesRow>();
}