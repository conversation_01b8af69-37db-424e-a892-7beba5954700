﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface SupplyTypesRow {
    RowNumber?: number;
    SupplyType?: string;
    NatureOfSupplyId?: number;
    NatureOfSupply?: string;
    SupplyTypeId?: number;
    SetDefault?: boolean;
}

export abstract class SupplyTypesRow {
    static readonly idProperty = 'SupplyTypeId';
    static readonly nameProperty = 'SupplyType';
    static readonly localTextPrefix = 'Default.SupplyTypes';
    static readonly lookupKey = 'Default.SupplyTypes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<SupplyTypesRow>('Default.SupplyTypes') }
    static async getLookupAsync() { return getLookupAsync<SupplyTypesRow>('Default.SupplyTypes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SupplyTypesRow>();
}