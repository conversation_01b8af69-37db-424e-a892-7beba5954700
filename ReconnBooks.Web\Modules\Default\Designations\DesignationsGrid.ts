import { DesignationsColumns, DesignationsRow, DesignationsService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { DesignationsDialog } from './DesignationsDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.DesignationsGrid')
export class DesignationsGrid extends EntityGridDialog<DesignationsRow, any> {
    protected getColumnsKey() { return DesignationsColumns.columnsKey; }
    protected getDialogType() { return DesignationsDialog; }
    protected getRowDefinition() { return DesignationsRow; }
    protected getService() { return DesignationsService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}