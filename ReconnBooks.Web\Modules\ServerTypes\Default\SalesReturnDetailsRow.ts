﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface SalesReturnDetailsRow {
    RowNumber?: number;
    SalesReturnDetailId?: number;
    SalesReturnId?: number;
    DeliveryNoteDetailId?: number;
    InvoiceDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    RejectedQuantity?: number;
    RejectedUnitId?: number;
    RejectedItemSerialNo?: string;
    RejectionReasonId?: number;
    AssessmentRemarks?: string;
    ReplacementMethodId?: number;
    Remarks?: string;
    SalesReturnNo?: string;
    DeliveryNoteDetailProductDescription?: string;
    InvoiceDetailCommodityDescription?: string;
    CommodityName?: string;
    CommodityType?: string;
    UnitName?: string;
    RejectionReason?: string;
    ReplacementMethod?: string;
    InvoiceQuantity?: number;
    InvoiceUnitId?: number;
    UnitPrice?: number;
    NetUnitAmount?: number;
    NetPricePerUnit?: number;
    NetAmount?: number;
}

export abstract class SalesReturnDetailsRow {
    static readonly idProperty = 'SalesReturnDetailId';
    static readonly nameProperty = 'SalesReturnDetailId';
    static readonly localTextPrefix = 'Default.SalesReturnDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SalesReturnDetailsRow>();
}