using Microsoft.AspNetCore.Mvc;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.DependencyInjection;
using MyRow = ReconnBooks.Default.GrNsRow;
using MimeKit;
using ReconnBooks.Modules.Common.Helpers.EmailHelper;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/GrNs/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class GrNsEndpoint : ServiceEndpoint
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;
    private readonly IServiceProvider serviceProvider;
    private readonly ReportEmailHelper _emailHelper;

    public GrNsEndpoint(IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections, IServiceProvider serviceProvider, ReportEmailHelper emailHelper)
    {
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
        this.serviceProvider = serviceProvider;
        this._emailHelper = emailHelper;
    }

    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IGrNsSaveHandler handler)
    {
        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return null;
        }

        var GrNsNextNumber = GetNextNumber(uow.Connection, new GetNextNumberRequest
        {
            Prefix = request.Entity.GRNNo
        });
        request.Entity.GRNNo = GrNsNextNumber.Serial;
        SaveResponse saveResponse = handler.Create(uow, request);

        return saveResponse;
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IGrNsSaveHandler handler)
    {
        return handler.Update(uow, request);
    }

    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] IGrNsDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] IGrNsRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] IGrNsListHandler handler)
    {
        return handler.List(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] IGrNsListHandler handler,
        [FromServices] IExcelExporter exporter)
    {
        var data = List(connection, request, handler).Entities;
        var bytes = exporter.Export(data, typeof(Columns.GrNsColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "GrNsList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }
    public GetNextNumberResponse GetNextNumber(IDbConnection connection, GetNextNumberRequest request)
    {
        if (!string.IsNullOrWhiteSpace(request.Prefix))
        {
            request.Prefix = $"{request.Prefix}/";
        }

        request.Length = request.Prefix.Length + 3;

        return GetNextNumberHelper.GetNextNumber(connection, request, MyRow.Fields.GRNNo);
    }

    public ListResponse<GrnDetailsRow> GetFromPurchaseOrderDetails(RetrieveRequest request)
    {
        using (var connection = sqlConnections.NewByKey("Default"))
        {
            var purchaseOrderDetailsRowFields = PurchaseOrderDetailsRow.Fields;
            var purchaseOrderRetrieveHandler = serviceProvider.GetRequiredService<IPurchaseOrdersRetrieveHandler>();
            var purchaseOrdersRow = purchaseOrderRetrieveHandler.Retrieve(connection, new RetrieveRequest()
            {
                EntityId = request.EntityId
            });

            int id = 1;
            List<GrnDetailsRow> listGrnDetailsRow = new List<GrnDetailsRow>();

            var commoditiesRowFields = CommoditiesRow.Fields;
            purchaseOrdersRow.Entity.PurchaseOrderDetailsList.ForEach(q =>
            {
                var commodityRow = connection.Single<CommoditiesRow>(new Criteria(commoditiesRowFields.CommodityId) == q.CommodityId.GetValueOrDefault());
                listGrnDetailsRow.Add(new GrnDetailsRow
                {
                    RowNumber = id++,
                    CommodityId = q.CommodityId,
                    CommodityName = q.CommodityName,
                    CommodityCode = q.CommodityCode,
                    CommodityDescription = q.CommodityDescription,
                    CommodityTypeId = q.CommodityTypeId,
                    CommodityType = q.CommodityType,
                    PoUnitId = q.UnitId,
                    PoQuantity = q.Quantity,
                });
            });

            return new ListResponse<GrnDetailsRow>()
            {
                Entities = listGrnDetailsRow
            };
        }
    }
    
    [HttpPost]
    public ServiceResponse EmailGRN(EmailRequest request)
    {
        var subject = "GRN " + request.DocumentNo;
        var body = "Please find your GRN attached. If you have any questions or concerns, please don't hesitate to reach out to us.";

        _emailHelper.SendReportEmail("GRNReport",
             request.DocumentId,
             request.ToEmail,
             request.DocumentNo,
             subject, body);

        return new ServiceResponse();
    }
}