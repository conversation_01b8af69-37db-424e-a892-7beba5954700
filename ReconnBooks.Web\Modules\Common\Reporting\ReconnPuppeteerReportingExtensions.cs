using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Serenity.Reporting;

namespace ReconnBooks.Modules.Common.Reporting;

public static class ReconnPuppeteerReportingExtensions
{
    public static void AddReconnHtmlToPdf(this IServiceCollection services)
    {
        ArgumentNullException.ThrowIfNull(services);

        services.TryAddSingleton<IHtmlToPdfConverter, ReconnHtmlToPdfConverter>();
    }
}
