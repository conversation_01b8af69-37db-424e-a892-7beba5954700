﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface ProductMakeRow {
    RowNumber?: number;
    ProductMakeId?: number;
    ProductMake?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class ProductMakeRow {
    static readonly idProperty = 'ProductMakeId';
    static readonly nameProperty = 'ProductMake';
    static readonly localTextPrefix = 'Default.ProductMake';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<ProductMakeRow>();
}