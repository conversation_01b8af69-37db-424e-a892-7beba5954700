import { alertDialog, Decorators } from "@serenity-is/corelib";
import { BulkServiceAction } from "@serenity-is/extensions";
import { BulkReportsService } from "../../ServerTypes/Modules";

@Decorators.registerClass('ReconnBooks.Modules.Common.Reporting.GenericBulkAction')

export class BulkReportAction extends BulkServiceAction {
    private key: string;
    private pdfFileName: string;
    private zippedFileName: string;

    constructor(options: {
        key: string;
        pdfFileName: string;
        zippedFileName: string;
    }) {
        super();
        this.key = options.key;
        this.pdfFileName = options.pdfFileName;
        this.zippedFileName = options.zippedFileName;
    }

    protected getParallelRequests() {
        return 10; // Default value; can be overridden
    }

    protected getBatchSize() {
        return 5; // Default value; can be overridden
    }

    protected executeForBatch(batch: any[]) {
        BulkReportsService.Download(
            {
                Key: this.key,
                EntityIDs: JSON.stringify(batch.map(x => parseInt(x))),
                PDFFileName: this.pdfFileName,
                ZippedFileName: this.zippedFileName,
            },
            response => {
                if (response && response.Content) {
                    // Decode Base64 content
                    const binaryString = atob(response.Content);
                    const binaryLength = binaryString.length;
                    const binaryArray = new Uint8Array(binaryLength);

                    for (let i = 0; i < binaryLength; i++) {
                        binaryArray[i] = binaryString.charCodeAt(i);
                    }

                    // Create a Blob from the binary data
                    const blob = new Blob([binaryArray], { type: response.ContentType });

                    // Create a link element for download
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = response.FileName || 'download.zip';

                    // Append the link to the DOM, trigger click, and remove it
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alertDialog("Failed to download the file. No content received.");
                }

                this.set_successCount(this.get_successCount() + batch.length);
            },
            {
                blockUI: false,
                onError: () => this.set_errorCount(this.get_errorCount() + batch.length),
                onCleanup: () => this.serviceCallCleanup(),
            }
        );
    }
}
