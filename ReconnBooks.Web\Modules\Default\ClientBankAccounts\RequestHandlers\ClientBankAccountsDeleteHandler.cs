﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.ClientBankAccountsRow;

namespace ReconnBooks.Default;

public interface IClientBankAccountsDeleteHandler : <PERSON>eleteHandler<MyRow, MyRequest, MyResponse> {}

public class ClientBankAccountsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IClientBankAccountsDeleteHandler
{
    public ClientBankAccountsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}