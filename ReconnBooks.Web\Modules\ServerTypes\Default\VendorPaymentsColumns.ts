﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { VendorPaymentsRow } from "./VendorPaymentsRow";

export interface VendorPaymentsColumns {
    RowNumber: Column<VendorPaymentsRow>;
    PaymentVoucherNo: Column<VendorPaymentsRow>;
    PaymentVoucherDate: Column<VendorPaymentsRow>;
    VendorName: Column<VendorPaymentsRow>;
    TotalPayable: Column<VendorPaymentsRow>;
    OnAccount: Column<VendorPaymentsRow>;
    TDSRateTransaction: Column<VendorPaymentsRow>;
    TDSAmount: Column<VendorPaymentsRow>;
    TCSRateNatureOfTransaction: Column<VendorPaymentsRow>;
    TCSAmount: Column<VendorPaymentsRow>;
    NetPayable: Column<VendorPaymentsRow>;
    AmountPaid: Column<VendorPaymentsRow>;
    Narration: Column<VendorPaymentsRow>;
    FinancialYearName: Column<VendorPaymentsRow>;
    PaymentVoucherMonth: Column<VendorPaymentsRow>;
    ModeOfPayment: Column<VendorPaymentsRow>;
    ChequeDdNo: Column<VendorPaymentsRow>;
    ChequeDdDate: Column<VendorPaymentsRow>;
    BankBranchName: Column<VendorPaymentsRow>;
    PaymentRefNo: Column<VendorPaymentsRow>;
    Remarks: Column<VendorPaymentsRow>;
    PreparedByUserUsername: Column<VendorPaymentsRow>;
    PreparedDate: Column<VendorPaymentsRow>;
    VerifiedByUserUsername: Column<VendorPaymentsRow>;
    VerifiedDate: Column<VendorPaymentsRow>;
    AuthorizedByUserUsername: Column<VendorPaymentsRow>;
    AuthorizedDate: Column<VendorPaymentsRow>;
    ModifiedByUserUsername: Column<VendorPaymentsRow>;
    ModifiedDate: Column<VendorPaymentsRow>;
    CancelledByUserUsername: Column<VendorPaymentsRow>;
    CancelledDate: Column<VendorPaymentsRow>;
    AuthorizedStatus: Column<VendorPaymentsRow>;
    VendorPaymentId: Column<VendorPaymentsRow>;
}

export class VendorPaymentsColumns extends ColumnsBase<VendorPaymentsRow> {
    static readonly columnsKey = 'Default.VendorPayments';
    static readonly Fields = fieldsProxy<VendorPaymentsColumns>();
}

[IndianNumberFormatter]; // referenced types