using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("ClientBankAccounts")]
[DisplayName("Client Bank Accounts"), InstanceName("Client Bank Accounts"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class ClientBankAccountsRow : Row<ClientBankAccountsRow.RowFields>, IIdRow, INameRow
{
    const string jBank = nameof(jBank);

    [DisplayName("Client Bank Account Id"), Identity, IdProperty]
    public int? ClientBankAccountId { get => fields.ClientBankAccountId[this]; set => fields.ClientBankAccountId[this] = value; }

    [DisplayName("Client Id"), NotNull]
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }
    
    [DisplayName("Account Name"), Size(50)]
    public string AccountName { get => fields.AccountName[this]; set => fields.AccountName[this] = value; }

    [DisplayName("Bank Name"), NotNull, ForeignKey(typeof(BanksRow)), LeftJoin(jBank), TextualField(nameof(BankName))]
    [ServiceLookupEditor(typeof(BanksRow), InplaceAdd =true, Service = "Default/Banks/List")]
    public int? BankId { get => fields.BankId[this]; set => fields.BankId[this] = value; }
    
    [DisplayName("Bank Name"), Origin(jBank, nameof(BanksRow.BankName)), LookupInclude]
    public string BankName { get => fields.BankName[this]; set => fields.BankName[this] = value; }

    [DisplayName("Branch Name"), Size(150), QuickSearch, NameProperty]
    public string BranchName { get => fields.BranchName[this]; set => fields.BranchName[this] = value; }


    [DisplayName("Account No."), Size(50)]
    public string AccountNumber { get => fields.AccountNumber[this]; set => fields.AccountNumber[this] = value; }

    [DisplayName("IFSC Code"), Column("IFSCCode"), Size(50)]
    public string IFSCCode { get => fields.IFSCCode[this]; set => fields.IFSCCode[this] = value; }

    [DisplayName("Branch Code"), Size(50)]
    public string BranchCode { get => fields.BranchCode[this]; set => fields.BranchCode[this] = value; }

    [DisplayName("Swift Code"), Size(50)]
    public string SwiftCode { get => fields.SwiftCode[this]; set => fields.SwiftCode[this] = value; }

    [DisplayName("QR Code"), Column("QRCode"), Size(500)]
    public string QRCode { get => fields.QRCode[this]; set => fields.QRCode[this] = value; }

    [DisplayName("Status"), NotNull]
    public bool? Status { get => fields.Status[this]; set => fields.Status[this] = value; }

}