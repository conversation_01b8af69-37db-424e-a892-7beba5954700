﻿import { ServiceLookupEditor, LookupEditor, StringEditor, DateEditor, MultipleImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CitiesDialog } from "../../Default/Cities/CitiesDialog";

export interface EmployeesForm {
    UserTypeId: ServiceLookupEditor;
    ClientId: LookupEditor;
    ConsultantId: LookupEditor;
    ClientsConsultantName: StringEditor;
    EmployeeName: StringEditor;
    Address: StringEditor;
    CityId: ServiceLookupEditor;
    PostalCode: StringEditor;
    MobileNo: StringEditor;
    AlternateNo: StringEditor;
    PhoneNo: StringEditor;
    EmailId: StringEditor;
    DateOfBirth: DateEditor;
    HireDate: DateEditor;
    Notes: StringEditor;
    UploadDocuments: MultipleImageUploadEditor;
}

export class EmployeesForm extends PrefixedContext {
    static readonly formKey = 'Default.Employees';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!EmployeesForm.init)  {
            EmployeesForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = LookupEditor;
            var w2 = StringEditor;
            var w3 = DateEditor;
            var w4 = MultipleImageUploadEditor;

            initFormType(EmployeesForm, [
                'UserTypeId', w0,
                'ClientId', w1,
                'ConsultantId', w1,
                'ClientsConsultantName', w2,
                'EmployeeName', w2,
                'Address', w2,
                'CityId', w0,
                'PostalCode', w2,
                'MobileNo', w2,
                'AlternateNo', w2,
                'PhoneNo', w2,
                'EmailId', w2,
                'DateOfBirth', w3,
                'HireDate', w3,
                'Notes', w2,
                'UploadDocuments', w4
            ]);
        }
    }
}

queueMicrotask(() => [CitiesDialog]); // referenced dialogs