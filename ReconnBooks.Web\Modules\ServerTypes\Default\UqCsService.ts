﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { UqCsRow } from "./UqCsRow";

export namespace UqCsService {
    export const baseUrl = 'Default/UqCs';

    export declare function Create(request: SaveRequest<UqCsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<UqCsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<UqCsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<UqCsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<UqCsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<UqCsRow>>;

    export const Methods = {
        Create: "Default/UqCs/Create",
        Update: "Default/UqCs/Update",
        Delete: "Default/UqCs/Delete",
        Retrieve: "Default/UqCs/Retrieve",
        List: "Default/UqCs/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>UqCsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}