﻿CREATE TABLE [dbo].[DebitNotes]
(
    [DebitNoteId]       INT				NOT NULL	IDENTITY (1, 1),
    [DebitNoteNo]		NVARCHAR (50)	NOT NULL,
    [DebitNoteDate]		DATETIME			NULL,
    [VendorId]			INT				NOT NULL,
   
    [FinancialYearId]	INT					NULL,
    [PurchaseOrderId]	INT					NULL,
    [PurchaseReturnId]	INT					NULL,
	[DebitNoteAmount]	DECIMAL(18, 2)		NULL,
    
    [Remarks]		    NVARCHAR (MAX)		NULL,
    
-------------------Authorization Details-------------
    [ClientId]              INT	        NOT NULL    CONSTRAINT [DF_DebitNotes_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	            NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]			DATETIME        NULL,
    [AuthorizationStatus]   BIT         NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details----------------------
    CONSTRAINT [FK_DebitNotes_PreparedByUsers]     FOREIGN KEY ([PreparedByUserId])    REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_DebitNotes_VerfiedByUsers]      FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_DebitNotes_AuthorizedByUsers]   FOREIGN KEY ([AuthorizedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_DebitNotes_ModifiedByUsers]     FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_DebitNotes_CancelledByUsers]    FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_DebitNotes_Clients]	            FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients] ([ClientId]),
    -------------------Authorization Details End------------------

    CONSTRAINT [PK_DebitNotes]      PRIMARY KEY	CLUSTERED	([DebitNoteId] ASC),
    CONSTRAINT [FK_DebitNotes_Vendors]		    FOREIGN KEY	([VendorId])	    REFERENCES [dbo].[Vendors]	([VendorId]),
    CONSTRAINT [FK_DebitNotes_FinancialYears]	FOREIGN KEY	([FinancialYearId])	REFERENCES [dbo].[FinancialYears]  ([FinancialYearId]),
    CONSTRAINT [FK_DebitNotes_PurchaseOrders]	FOREIGN KEY	([PurchaseOrderId])	REFERENCES [dbo].[PurchaseOrders]  ([PurchaseOrderId]),
    CONSTRAINT [FK_DebitNotes_PurchaseReturns]  FOREIGN KEY	([PurchaseReturnId])REFERENCES [dbo].[PurchaseReturns] ([PurchaseReturnId]),
);
GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[DebitNotes]([VendorId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseOrders]
    ON [dbo].[DebitNotes]([PurchaseOrderId] ASC);

GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[DebitNotes]([FinancialYearId] ASC);

GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[DebitNotes]([ClientId] ASC);

