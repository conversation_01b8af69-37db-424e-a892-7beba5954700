﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { CitiesRow } from "./CitiesRow";

export interface CitiesColumns {
    RowNumber: Column<CitiesRow>;
    CityId: Column<CitiesRow>;
    CityName: Column<CitiesRow>;
    PINCode: Column<CitiesRow>;
    District: Column<CitiesRow>;
    StateName: Column<CitiesRow>;
}

export class CitiesColumns extends ColumnsBase<CitiesRow> {
    static readonly columnsKey = 'Default.Cities';
    static readonly Fields = fieldsProxy<CitiesColumns>();
}