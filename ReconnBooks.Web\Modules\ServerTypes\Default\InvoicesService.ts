﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { InvoiceDetailsRow } from "./InvoiceDetailsRow";
import { InvoicesRow } from "./InvoicesRow";

export namespace InvoicesService {
    export const baseUrl = 'Default/Invoices';

    export declare function Create(request: SaveRequest<InvoicesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<InvoicesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<InvoicesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<InvoicesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<InvoicesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<InvoicesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromProformaInvoiceDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<InvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<InvoiceDetailsRow>>;
    export declare function EmailInvoice(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/Invoices/Create",
        Update: "Default/Invoices/Update",
        Delete: "Default/Invoices/Delete",
        Retrieve: "Default/Invoices/Retrieve",
        List: "Default/Invoices/List",
        GetNextNumber: "Default/Invoices/GetNextNumber",
        GetFromProformaInvoiceDetails: "Default/Invoices/GetFromProformaInvoiceDetails",
        EmailInvoice: "Default/Invoices/EmailInvoice"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromProformaInvoiceDetails', 
        'EmailInvoice'
    ].forEach(x => {
        (<any>InvoicesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}