﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface UserTypesRow {
    UserTypeId?: number;
    UserTypeName?: string;
    Description?: string;
}

export abstract class UserTypesRow {
    static readonly idProperty = 'UserTypeId';
    static readonly nameProperty = 'UserTypeName';
    static readonly localTextPrefix = 'Default.UserTypes';
    static readonly lookupKey = 'Default.UserTypes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<UserTypesRow>('Default.UserTypes') }
    static async getLookupAsync() { return getLookupAsync<UserTypesRow>('Default.UserTypes') }

    static readonly deletePermission = 'Administration:Security';
    static readonly insertPermission = 'Administration:Security';
    static readonly readPermission = 'Administration:Security';
    static readonly updatePermission = 'Administration:Security';

    static readonly Fields = fieldsProxy<UserTypesRow>();
}