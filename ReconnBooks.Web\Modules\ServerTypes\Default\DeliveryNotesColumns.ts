﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { DeliveryNotesRow } from "./DeliveryNotesRow";

export interface DeliveryNotesColumns {
    RowNumber: Column<DeliveryNotesRow>;
    DeliveryNoteNo: Column<DeliveryNotesRow>;
    DeliveryNoteDate: Column<DeliveryNotesRow>;
    CustomerCompanyName: Column<DeliveryNotesRow>;
    BillingAddress: Column<DeliveryNotesRow>;
    BillingCityCityName: Column<DeliveryNotesRow>;
    BillingPinCode: Column<DeliveryNotesRow>;
    SalesOrderNo: Column<DeliveryNotesRow>;
    GSTIN: Column<DeliveryNotesRow>;
    PlaceOfSupplyStateName: Column<DeliveryNotesRow>;
    SupplyType: Column<DeliveryNotesRow>;
    CGSTAmount: Column<DeliveryNotesRow>;
    SGSTAmount: Column<DeliveryNotesRow>;
    IGSTAmount: Column<DeliveryNotesRow>;
    FinancialYearName: Column<DeliveryNotesRow>;
    DeliveryNoteMonth: Column<DeliveryNotesRow>;
    QuotationNo: Column<DeliveryNotesRow>;
    EWayBillNo: Column<DeliveryNotesRow>;
    EWayBillNoDate: Column<DeliveryNotesRow>;
    OrderRefNo: Column<DeliveryNotesRow>;
    OrderRefDate: Column<DeliveryNotesRow>;
    ReasonToTransport: Column<DeliveryNotesRow>;
    ShippedVia: Column<DeliveryNotesRow>;
    ShippingDocketNo: Column<DeliveryNotesRow>;
    VehicleNo: Column<DeliveryNotesRow>;
    UploadDocketCopy: Column<DeliveryNotesRow>;
    Inspection: Column<DeliveryNotesRow>;
    Remarks: Column<DeliveryNotesRow>;
    ClientId: Column<DeliveryNotesRow>;
    PreparedByUserUsername: Column<DeliveryNotesRow>;
    PreparedDate: Column<DeliveryNotesRow>;
    VerifiedByUserUsername: Column<DeliveryNotesRow>;
    VerifiedDate: Column<DeliveryNotesRow>;
    AuthorizedByUserUsername: Column<DeliveryNotesRow>;
    AuthorizedDate: Column<DeliveryNotesRow>;
    ModifiedByUserUsername: Column<DeliveryNotesRow>;
    ModifiedDate: Column<DeliveryNotesRow>;
    CancelledByUserUsername: Column<DeliveryNotesRow>;
    CancelledDate: Column<DeliveryNotesRow>;
    AuthorizedStatus: Column<DeliveryNotesRow>;
    DeliveryNoteId: Column<DeliveryNotesRow>;
}

export class DeliveryNotesColumns extends ColumnsBase<DeliveryNotesRow> {
    static readonly columnsKey = 'Default.DeliveryNotes';
    static readonly Fields = fieldsProxy<DeliveryNotesColumns>();
}

[IndianNumberFormatter]; // referenced types