using ReconnBooks.Administration;
using ReconnBooks.Modules.Administration.User.Authentication.Claims;

namespace ReconnBooks.Modules.Default.Clients;

public class MultiClientBehavior : IImplicitBehavior,
        ISaveBehavior, IDeleteBehavior, IListBehavior, IRetrieveBehavior
{
    private Int32Field fldClientId;

    public bool ActivateFor(IRow row)
    {
        if (row is not IMultiClientRow multiClientRow)
            return false;

        fldClientId = multiClientRow.ClientIdField;
        return true;
    }

    public void OnPrepareQuery(IRetrieveRequestHandler handler,
        SqlQuery query)
    {
        if (!handler.Context.Permissions.HasPermission(PermissionKeys.Clients.User))
        {
            if (handler.Context.User.GetClientId().HasValue) //The user is Client
                query.Where(fldClientId == handler.Context.User.GetClientId().Value);
            //else if (handler.Context.User.GetConsultantId().HasValue) // The user is Consultant
            //    query.Where(fldClientId == handler.Context.User.GetConsultantId().Value);
        }
    }

    public void OnPrepareQuery(IListRequestHandler handler,
        SqlQuery query)
    {
        //TODO: same code snippet as above, extract a method
        if (!handler.Context.Permissions.HasPermission(PermissionKeys.Clients.User))
        {
            //Also consider the scenario where a consultant could not have any active client working - newly added consultant
            //or assigned client (that consultant was actively working for currently) is removed. The clientId in that case sould be 0 or something.


            //if (handler.Context.User.GetClientId().HasValue) //The user is Client
                query.Where(fldClientId == handler.Context.User.GetClientId().GetValueOrDefault(0));
            //else if(handler.Context.User.GetConsultantId().HasValue) // The user is Consultant
            //    query.Where(fldClientId == handler.Context.User.GetConsultantId().Value);
        }
    }

    public void OnSetInternalFields(ISaveRequestHandler handler)
    {
        if (handler.IsCreate)
        {
            if (!fldClientId[handler.Row].HasValue)
                fldClientId[handler.Row] = handler.Context.User.GetClientId();
        }
    }

    public void OnValidateRequest(ISaveRequestHandler handler)
    {
        if (handler.IsUpdate)
        {
            if (fldClientId[handler.Old] != fldClientId[handler.Row])
                handler.Context.Permissions.ValidatePermission(PermissionKeys.Clients.Admin, handler.Context.Localizer);
        }
    }

    public void OnValidateRequest(IDeleteRequestHandler handler)
    {
        if (fldClientId[handler.Row] != handler.Context.User.GetClientId())
            handler.Context.Permissions.ValidatePermission(PermissionKeys.Clients.Admin, handler.Context.Localizer);
    }

    public void OnAfterDelete(IDeleteRequestHandler handler) { }
    public void OnAfterExecuteQuery(IRetrieveRequestHandler handler) { }
    public void OnAfterExecuteQuery(IListRequestHandler handler) { }
    public void OnAfterSave(ISaveRequestHandler handler) { }
    public void OnApplyFilters(IListRequestHandler handler, SqlQuery query) { }
    public void OnAudit(IDeleteRequestHandler handler) { }
    public void OnAudit(ISaveRequestHandler handler) { }
    public void OnBeforeDelete(IDeleteRequestHandler handler) { }
    public void OnBeforeExecuteQuery(IRetrieveRequestHandler handler) { }
    public void OnBeforeExecuteQuery(IListRequestHandler handler) { }
    public void OnBeforeSave(ISaveRequestHandler handler) { }
    public void OnPrepareQuery(IDeleteRequestHandler handler, SqlQuery query) { }
    public void OnPrepareQuery(ISaveRequestHandler handler, SqlQuery query) { }
    public void OnReturn(IDeleteRequestHandler handler) { }
    public void OnReturn(IRetrieveRequestHandler handler) { }
    public void OnReturn(IListRequestHandler handler) { }
    public void OnReturn(ISaveRequestHandler handler) { }
    public void OnValidateRequest(IRetrieveRequestHandler handler) { }
    public void OnValidateRequest(IListRequestHandler handler) { }
}

