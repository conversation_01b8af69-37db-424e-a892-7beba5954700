﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { SalesOrdersRow } from "./SalesOrdersRow";

export interface SalesOrdersColumns {
    RowNumber: Column<SalesOrdersRow>;
    SalesOrderNo: Column<SalesOrdersRow>;
    SalesOrderDate: Column<SalesOrdersRow>;
    CustomerCompanyName: Column<SalesOrdersRow>;
    GrandTotal: Column<SalesOrdersRow>;
    QuotationNo: Column<SalesOrdersRow>;
    OrderRefNo: Column<SalesOrdersRow>;
    OrderRefDate: Column<SalesOrdersRow>;
    DeliveryDueDate: Column<SalesOrdersRow>;
    SupplyType: Column<SalesOrdersRow>;
    NetTaxableAmount: Column<SalesOrdersRow>;
    NetCGSTAmount: Column<SalesOrdersRow>;
    NetSGSTAmount: Column<SalesOrdersRow>;
    NetIGSTAmount: Column<SalesOrdersRow>;
    PaymentTerms: Column<SalesOrdersRow>;
    FinancialYearName: Column<SalesOrdersRow>;
    SalesOrderMonth: Column<SalesOrdersRow>;
    Inspection: Column<SalesOrdersRow>;
    Remarks: Column<SalesOrdersRow>;
    ClientId: Column<SalesOrdersRow>;
    PreparedByUserUsername: Column<SalesOrdersRow>;
    PreparedDate: Column<SalesOrdersRow>;
    VerifiedByUserUsername: Column<SalesOrdersRow>;
    VerifiedDate: Column<SalesOrdersRow>;
    AuthorizedByUserUsername: Column<SalesOrdersRow>;
    AuthorizedDate: Column<SalesOrdersRow>;
    ModifiedByUserUsername: Column<SalesOrdersRow>;
    ModifiedDate: Column<SalesOrdersRow>;
    CancelledByUserUsername: Column<SalesOrdersRow>;
    CancelledDate: Column<SalesOrdersRow>;
    AuthorizedStatus: Column<SalesOrdersRow>;
    SalesOrderId: Column<SalesOrdersRow>;
}

export class SalesOrdersColumns extends ColumnsBase<SalesOrdersRow> {
    static readonly columnsKey = 'Default.SalesOrders';
    static readonly Fields = fieldsProxy<SalesOrdersColumns>();
}

[IndianNumberFormatter]; // referenced types