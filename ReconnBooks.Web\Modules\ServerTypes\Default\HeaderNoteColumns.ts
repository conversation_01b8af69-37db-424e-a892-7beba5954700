﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { HeaderNoteRow } from "./HeaderNoteRow";

export interface HeaderNoteColumns {
    RowNumber: Column<HeaderNoteRow>;
    HeaderNoteId: Column<HeaderNoteRow>;
    HeaderNote: Column<HeaderNoteRow>;
    Remarks: Column<HeaderNoteRow>;
}

export class HeaderNoteColumns extends ColumnsBase<HeaderNoteRow> {
    static readonly columnsKey = 'Default.HeaderNote';
    static readonly Fields = fieldsProxy<HeaderNoteColumns>();
}