﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.AddressedToRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.AddressedToRow;

namespace ReconnBooks.Default;

public interface IAddressedToSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class AddressedToSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IAddressedToSaveHandler
{
    public AddressedToSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}