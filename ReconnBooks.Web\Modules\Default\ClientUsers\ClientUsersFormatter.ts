import { Decorators, Formatter, Lookup } from "@serenity-is/corelib";
import { FormatterContext } from "@serenity-is/sleekgrid";
import { ClientsRow } from "../../ServerTypes/Default";

let lookup: Lookup<ClientsRow>;
let promise: Promise<Lookup<ClientsRow>>;

@Decorators.registerFormatter('ReconnBooks.Default.ClientUsersFormatter')
export class ClientUsersFormatter implements Formatter {

    format(ctx: FormatterContext) {

        let idList = ctx.value as number[];
        if (!idList || !idList.length)
            return "";

        let byId = lookup?.itemById;
        if (byId) {
            return idList.map(x => {
                var z = byId[x];
                return ctx.escape(z == null ? x : z.ClientCode);
            }).join(", ");
        }

        promise ??= ClientsRow.getLookupAsync().then(l => {
            lookup = l;
            try {
                ctx.grid?.invalidate();
            }
            finally {
                lookup = null;
                promise = null;
            }
        }).catch(() => promise = null);

        return `<i class="fa fa-spinner"></i>`;
    }
}