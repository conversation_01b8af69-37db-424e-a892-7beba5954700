﻿import { ImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface CommoditiesExcelImportForm {
    FileName: ImageUploadEditor;
}

export class CommoditiesExcelImportForm extends PrefixedContext {
    static readonly formKey = 'Default.CommoditiesExcelImportForm';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CommoditiesExcelImportForm.init)  {
            CommoditiesExcelImportForm.init = true;

            var w0 = ImageUploadEditor;

            initFormType(CommoditiesExcelImportForm, [
                'FileName', w0
            ]);
        }
    }
}