import { CreditNotesForm, CreditNotesRow, CreditNotesService, DocumentsRow, CustomersRow, StatesRow } from '@/ServerTypes/Default';
import { Decorators, toId, getRemoteData, alertDialog, formatDate, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';

@Decorators.registerClass('ReconnBooks.Default.CreditNotesDialog')
@Decorators.responsive()
@Decorators.panel()
export class CreditNotesDialog extends VerifyAuthorizeDialog<CreditNotesRow> {
    protected getFormKey() { return CreditNotesForm.formKey; }
    protected getRowDefinition() { return CreditNotesRow; }
    protected getService() { return CreditNotesService.baseUrl; }

    protected form = new CreditNotesForm(this.idPrefix);
    private docType: string;

    constructor(props: WidgetProps<CreditNotesRow>) {
        super(props);

        (this.form.CreditNoteDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.updateTotal();
            this.form.CreditNoteDetailsList.getGrid().focus();

            const grid = this.form.CreditNoteDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        (this.form.CreditNoteDetailsList.view as any).onDataChanged.subscribe((e) => {
            e.preventDefault = false;
            this.updateTotal();
            // Add your custom logic here
        });

        this.form.CustomerId.changeSelect2(a => {
            setTimeout(async () => {
                var CustomerId = toId(this.form.CustomerId.value);
                if (CustomerId != null) {
                    var customer = (await CustomersRow.getLookupAsync()).itemById[CustomerId];
                    this.form.PlaceOfSupplyStateName.value = customer.PlaceOfSupplyStateName;

                    var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
                    this.form.CreditNoteDetailsList.SetPlaceOfSupply(customer.PlaceOfSupplyId == userSupplyStateId ? true : false);
                }
                else {
                    this.clearCustomerFields();
                }
            }, 100);
        })

        this.form.InvoiceId.changeSelect2(e => {
            if (this.form.InvoiceId.value === '') {
                // Clear the details in the grid
                this.form.CreditNoteDetailsList.value = [];
            }
            else {
                CreditNotesService.GetFromInvoiceDetails({
                    EntityId: toId(this.form.InvoiceId.value)
                },
                    response => {
                        this.form.CreditNoteDetailsList.value = response.Entities;
                    });
            }
        });

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
    }

    private updateTotal(): void {
        var total = 0;
        for (var k of this.form.CreditNoteDetailsList.view.getItems()) {
            total += k.NetAmount || 0;
        }
        //this.form.CreditNoteAmount.value = total;
        const roundOff = total - Math.floor(total);
        const RoundingOff = parseFloat((roundOff >= 0.50 ? 1 - roundOff : -roundOff).toFixed(2));
        this.form.CreditNoteAmount.value = total + RoundingOff;
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                    this.getNextNumber();
                    this.setDialogsLoadedState();
                });
            }
            else {
                this.getNextNumber();
                this.setDialogsLoadedState();
            }
        }
        else {
            var placeOfSupplyStateId = (await StatesRow.getLookupAsync()).items.find(x => x.StateName == this.form.PlaceOfSupplyStateName.value)?.StateId;

            var userSupplyStateId = getRemoteData("UserData").PlaceOfSupplyStateId;
            this.form.CreditNoteDetailsList.SetPlaceOfSupply(placeOfSupplyStateId == userSupplyStateId ? true : false);

            this.setDialogsLoadedState();
        }
    }

    protected updateInterface() {

        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    private getNextNumber(): any {

        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "Credit Note")[0].DocumentShortName;
        }

        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.CreditNoteNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.CreditNoteDetailsList.value || this.form.CreditNoteDetailsList.value.length === 0) {
            alertDialog("Credit Note cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }
    private clearCustomerFields() {
        this.form.PlaceOfSupplyStateName.value = undefined;
        this.form.CreditNoteDetailsList.value = undefined;
    }

    protected updateTitle() {
        this.dialogTitle = "New Credit Note";
    }
}