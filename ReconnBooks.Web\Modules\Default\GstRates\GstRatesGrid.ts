import { GstRatesColumns, GstRatesRow, GstRatesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { GstRatesDialog } from './GstRatesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.GstRatesGrid')
export class GstRatesGrid extends EntityGridDialog<GstRatesRow, any> {
    protected getColumnsKey() { return GstRatesColumns.columnsKey; }
    protected getDialogType() { return GstRatesDialog; }
    protected getRowDefinition() { return GstRatesRow; }
    protected getService() { return GstRatesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}