﻿using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Administration.LanguageRow>;
using MyRow = ReconnBooks.Administration.LanguageRow;


namespace ReconnBooks.Administration;
public interface ILanguageListHandler : IList<PERSON>andler<MyRow, MyRequest, MyResponse> { }

public class LanguageListHandler(IRequestContext context) :
    ListRequestHandler<MyRow, MyRequest, MyResponse>(context), ILanguageListHandler
{
}