﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface RejectionReasonsRow {
    RowNumber?: number;
    RejectionReasonId?: number;
    RejectionReason?: string;
    Description?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class RejectionReasonsRow {
    static readonly idProperty = 'RejectionReasonId';
    static readonly nameProperty = 'RejectionReason';
    static readonly localTextPrefix = 'Default.RejectionReasons';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<RejectionReasonsRow>();
}