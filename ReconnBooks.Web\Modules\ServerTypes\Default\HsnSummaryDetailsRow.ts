﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface HsnSummaryDetailsRow {
    HsnSummaryDetailId?: number;
    RowNumber?: number;
    InvoiceNo?: string;
    InvoiceDate?: string;
    HSNSACCode?: string;
    Quantity?: number;
    NetTaxableAmount?: number;
    IGSTRate?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    NetSGSTAmount?: number;
    NetAmount?: number;
}

export abstract class HsnSummaryDetailsRow {
    static readonly idProperty = 'HsnSummaryDetailId';
    static readonly nameProperty = 'HsnSummaryDetailId';
    static readonly localTextPrefix = 'Default.HsnSummaryDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<HsnSummaryDetailsRow>();
}