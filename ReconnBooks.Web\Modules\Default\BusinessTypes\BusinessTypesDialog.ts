import { BusinessTypesForm, BusinessTypesRow, BusinessTypesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.BusinessTypesDialog')
export class BusinessTypesDialog extends PendingChangesConfirmDialog<BusinessTypesRow> {
    protected getFormKey() { return BusinessTypesForm.formKey; }
    protected getRowDefinition() { return BusinessTypesRow; }
    protected getService() { return BusinessTypesService.baseUrl; }

    protected form = new BusinessTypesForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}