﻿import { StringEditor, TextAreaEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface LocationsForm {
    LocationName: StringEditor;
    Description: TextAreaEditor;
    Remarks: StringEditor;
    Discontinued: BooleanEditor;
}

export class LocationsForm extends PrefixedContext {
    static readonly formKey = 'Default.Locations';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!LocationsForm.init)  {
            LocationsForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;
            var w2 = BooleanEditor;

            initFormType(LocationsForm, [
                'LocationName', w0,
                'Description', w1,
                'Remarks', w0,
                'Discontinued', w2
            ]);
        }
    }
}