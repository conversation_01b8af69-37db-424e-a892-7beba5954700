﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, DateTimeEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { DebitNoteDetailsGridEditor } from "../../Default/DebitNoteDetails/DebitNoteDetailsGridEditor";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";
import { PurchaseReturnsDialog } from "../../Default/PurchaseReturns/PurchaseReturnsDialog";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface DebitNotesForm {
    DebitNoteNo: StringEditor;
    DebitNoteDate: DateEditor;
    VendorId: ServiceLookupEditor;
    PurchaseOrderId: ServiceLookupEditor;
    PurchaseReturnId: ServiceLookupEditor;
    PlaceOfSupplyStateName: StringEditor;
    DebitNoteDetailsList: DebitNoteDetailsGridEditor;
    FinancialYearId: LookupEditor;
    DebitNoteAmount: DecimalEditor;
    Remarks: TextAreaEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    AuthorizedStatus: BooleanEditor;
}

export class DebitNotesForm extends PrefixedContext {
    static readonly formKey = 'Default.DebitNotes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DebitNotesForm.init)  {
            DebitNotesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = DebitNoteDetailsGridEditor;
            var w4 = LookupEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = DateTimeEditor;
            var w8 = BooleanEditor;

            initFormType(DebitNotesForm, [
                'DebitNoteNo', w0,
                'DebitNoteDate', w1,
                'VendorId', w2,
                'PurchaseOrderId', w2,
                'PurchaseReturnId', w2,
                'PlaceOfSupplyStateName', w0,
                'DebitNoteDetailsList', w3,
                'FinancialYearId', w4,
                'DebitNoteAmount', w5,
                'Remarks', w6,
                'PreparedByUserId', w4,
                'PreparedDate', w7,
                'VerifiedByUserId', w4,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w4,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w4,
                'ModifiedDate', w1,
                'CancelledByUserId', w4,
                'CancelledDate', w1,
                'AuthorizedStatus', w8
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, PurchaseOrdersDialog, PurchaseReturnsDialog]); // referenced dialogs