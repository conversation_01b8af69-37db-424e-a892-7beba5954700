﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface EmployeesRow {
    RowNumber?: number;
    EmployeeId?: number;
    UserTypeId?: number;
    ClientId?: number;
    ConsultantId?: number;
    EmployeeName?: string;
    Address?: string;
    CityId?: number;
    PostalCode?: string;
    MobileNo?: string;
    AlternateNo?: string;
    PhoneNo?: string;
    EmailId?: string;
    DateOfBirth?: string;
    HireDate?: string;
    Notes?: string;
    UploadDocuments?: string;
    CityName?: string;
    ClientName?: string;
    ConsultantName?: string;
    ClientsConsultantName?: string;
}

export abstract class EmployeesRow {
    static readonly idProperty = 'EmployeeId';
    static readonly nameProperty = 'EmployeeName';
    static readonly localTextPrefix = 'Default.Employees';
    static readonly lookupKey = 'Default.Employees';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<EmployeesRow>('Default.Employees') }
    static async getLookupAsync() { return getLookupAsync<EmployeesRow>('Default.Employees') }

    static readonly deletePermission = 'Administration:Employees:Delete';
    static readonly insertPermission = 'Administration:Employees:Insert';
    static readonly readPermission = 'Administration:Employees:View';
    static readonly updatePermission = 'Administration:Employees:Modify';

    static readonly Fields = fieldsProxy<EmployeesRow>();
}