using ReconnBooks.Administration;
using ReconnBooks.Modules.Default.ClientUsers;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using ReconnBooks.Modules.Default.Consultants;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON>ame("ClientUsers")]
[<PERSON><PERSON>layName("Client Users"), InstanceName("Client Users"), GenerateFields]
[ReadPermission(PermissionKeys.Consultants.User)]//TODO: change it to Consultans only permission
[ModifyPermission(PermissionKeys.Consultants.User)]
[LookupScript("Administration.ClientUsers", LookupType = typeof(ClientUsersRowLookup))]
public sealed partial class ClientUsersRow : Row<ClientUsersRow.RowFields>, IIdRow, IMultiConsultantRow
{
    const string jUser = nameof(jUser);
    const string jClient = nameof(jClient);
    const string jConsultant = nameof(jConsultant);

    [DisplayName("Client User Id"), Identity, IdProperty]
    public int? ClientUserId { get => fields.ClientUserId[this]; set => fields.ClientUserId[this] = value; }

    [DisplayName("User"), PrimaryKey, NotNull, ForeignKey(typeof(Administration.UserRow)), LeftJoin(jUser), TextualField(nameof(Username))]
    [LookupEditor(typeof(Administration.UserRow))]
    public int? UserId { get => fields.UserId[this]; set => fields.UserId[this] = value; }

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [LookupEditor(typeof(ClientsRow))]
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Is Currently Active")]
    public bool? Status { get => fields.Status[this]; set => fields.Status[this] = value; }

    [DisplayName("User Username"), Origin(jUser, nameof(Administration.UserRow.Username))]
    public string Username { get => fields.Username[this]; set => fields.Username[this] = value; }

    [DisplayName("Client Name"), Origin(jClient, nameof(ClientsRow.ClientName)), LookupInclude]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }

    [DisplayName("Client Logo"), Origin(jClient, nameof(ClientsRow.Logo)), LookupInclude]
    public string ClientLogo { get => fields.ClientLogo[this]; set => fields.ClientLogo[this] = value; }

    [DisplayName("Consultant"), Origin(jClient, nameof(ClientsRow.ConsultantId)), ForeignKey(typeof(ConsultantsRow)), LeftJoin(jConsultant), TextualField("ConsultantName")]
    public Int32? ConsultantId
    {
        get { return fields.ConsultantId[this]; }
        set { fields.ConsultantId[this] = value; }
    }

    [DisplayName("Consultant Name"), Origin(jConsultant, nameof(ConsultantsRow.ConsultantName)), LookupInclude]
    public String ConsultantName
    {
        get { return fields.ConsultantName[this]; }
        set { fields.ConsultantName[this] = value; }
    }

    

    public Int32Field ConsultantIdField => fields.ConsultantId; 
}