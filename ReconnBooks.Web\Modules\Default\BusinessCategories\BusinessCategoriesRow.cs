using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("BusinessCategories")]
[DisplayName("Business Categories"), InstanceName("Business Categories"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class BusinessCategoriesRow : Row<BusinessCategoriesRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Business Category Id"), Identity, IdProperty,Hidden]
    public int? BusinessCategoryId { get => fields.BusinessCategoryId[this]; set => fields.BusinessCategoryId[this] = value; }

    [DisplayName("Business Category"), Size(150), NotNull, QuickSearch, NameProperty]
    public string BusinessCategory { get => fields.BusinessCategory[this]; set => fields.BusinessCategory[this] = value; }

    [DisplayName("Description"), Size(500)]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }
}