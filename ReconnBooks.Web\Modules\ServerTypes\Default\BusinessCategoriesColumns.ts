﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { BusinessCategoriesRow } from "./BusinessCategoriesRow";

export interface BusinessCategoriesColumns {
    RowNumber: Column<BusinessCategoriesRow>;
    BusinessCategoryId: Column<BusinessCategoriesRow>;
    BusinessCategory: Column<BusinessCategoriesRow>;
    Description: Column<BusinessCategoriesRow>;
}

export class BusinessCategoriesColumns extends ColumnsBase<BusinessCategoriesRow> {
    static readonly columnsKey = 'Default.BusinessCategories';
    static readonly Fields = fieldsProxy<BusinessCategoriesColumns>();
}