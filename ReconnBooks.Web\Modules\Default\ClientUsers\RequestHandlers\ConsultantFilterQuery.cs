using ReconnBooks.Administration;
using ReconnBooks.Default;

namespace ReconnBooks.Modules.Default.ClientUsers.RequestHandlers;

internal class ConsultantFilterQuery
{
    public static void PrepareRetrieveQuery(SqlQuery query, IPermissionService permissionService, IUserRetrieveService userRetriever, IUserAccessor userAccessor)
    {
        var clientUsersRowFields = ClientUsersRow.Fields;

        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return;
        }

        var consultantId = user.ConsultantId;
        var useriD = user.UserId;

        if (useriD != 0 && !permissionService.HasPermission(PermissionKeys.Consultants.Admin))
        {
            query.Where(clientUsersRowFields.ConsultantId == consultantId.GetValueOrDefault());
        }
    }
}
