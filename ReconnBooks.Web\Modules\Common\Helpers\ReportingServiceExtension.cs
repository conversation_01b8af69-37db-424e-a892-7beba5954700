using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Serenity.Reporting;

namespace ReconnBooks.Modules.Common.Helpers;

public static class ReportingServiceExtension
{
    /// <summary>
    /// Adds excel exporter
    /// </summary>
    /// <param name="services">The service collection.</param>
    public static void AddExcelExporter(this IServiceCollection services)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));

        services.TryAddSingleton<IDataReportExcelRenderer, DataReportExcelRenderer>();
        services.TryAddSingleton<IExcelExporter, ExcelExporter>();
    }

    public static void AddHtmlToPdf(this IServiceCollection services)
    {
        if (services == null)
            throw new ArgumentNullException(nameof(services));

        services.TryAddSingleton<ISiteAbsoluteUrl, SiteAbsoluteUrl>();
        services.TryAddSingleton<IHtmlReportCallbackUrlBuilder, HtmlReportCallbackUrlBuilder>();
        services.TryAddSingleton<IHtmlReportRenderUrlBuilder, HtmlReportCallbackUrlBuilder>();
        //services.TryAddSingleton<IWKHtmlToPdfConverter, PuppeteerHtmlToPdfConverter>();
        services.TryAddSingleton<IHtmlToPdfConverter, WKHtmlToPdfConverter>();
        services.TryAddSingleton<IHtmlReportPdfRenderer, HtmlReportPdfRenderer>();
    }

    public static void AddPuppeteerReporting(this IServiceCollection services)
    {
        services.TryAddSingleton<IReportRegistry, ReportRegistry>();
        services.TryAddSingleton<IReportRetrieveHandler, DefaultReportRetrieveHandler>();
        services.TryAddSingleton<IReportTreeFactory, DefaultReportTreeFactory>();
        services.TryAddSingleton<IReportFactory, DefaultReportFactory>();
        services.TryAddSingleton<IReportRenderer, DefaultReportRenderer>();
        services.AddExcelExporter();
        services.AddHtmlToPdf();
    }
}
