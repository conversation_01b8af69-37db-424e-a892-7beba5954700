﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface RacksRow {
    RowNumber?: number;
    RackId?: number;
    RackNo?: string;
    CompartmentNo?: string;
    BinNo?: string;
    RackDescription?: string;
    Remarks?: string;
    StoreId?: number;
    Discontinued?: boolean;
    StoreName?: string;
    ClientId?: number;
    ClientName?: string;
}

export abstract class RacksRow {
    static readonly idProperty = 'RackId';
    static readonly nameProperty = 'RackNo';
    static readonly localTextPrefix = 'Default.Racks';
    static readonly lookupKey = 'Default.Racks';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<RacksRow>('Default.Racks') }
    static async getLookupAsync() { return getLookupAsync<RacksRow>('Default.Racks') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<RacksRow>();
}