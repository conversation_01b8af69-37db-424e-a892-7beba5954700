using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("TCSRates")]
[DisplayName("TCS Rates"), InstanceName("TCS Rates"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class TcsRatesRow : Row<TcsRatesRow.RowFields>, IIdRow, INameRow,IRowNumberedRow
{
    const string jFinancialYear = nameof(jFinancialYear);
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("TCSRate Id"), Column("TCSRateId"), Identity, IdProperty,Hidden,LookupInclude]
    public int? TCSRateId { get => fields.TCSRateId[this]; set => fields.TCSRateId[this] = value; }

    [DisplayName("Section"), Size(50), NameProperty,]
    public string Section { get => fields.Section[this]; set => fields.Section[this] = value; }

    [DisplayName("TCS Rate"), Column("TCSRate"), Size(18), Scale(2), NotNull, LookupInclude]
    public decimal? TCSRate { get => fields.TCSRate[this]; set => fields.TCSRate[this] = value; }

    [DisplayName("TCS Rate "), Expression("CONCAT(T0.[TCSRate], ' %  - ', T0.[Section])")]
    public string TCSRateWithSection { get => fields.TCSRateWithSection[this]; set => fields.TCSRateWithSection[this] = value; }

    [DisplayName("Nature Of Transaction"), NotNull, QuickSearch, LookupInclude]
    public string NatureOfTransaction { get => fields.NatureOfTransaction[this]; set => fields.NatureOfTransaction[this] = value; }

    [DisplayName("Collector")]
    public string Collector { get => fields.Collector[this]; set => fields.Collector[this] = value; }

    [DisplayName("Collectee")]
    public string Collectee { get => fields.Collectee[this]; set => fields.Collectee[this] = value; }

    [DisplayName("w.e.f Date"), Column("wefDate")]
    public DateTime? WefDate { get => fields.WefDate[this]; set => fields.WefDate[this] = value; }

    [DisplayName("Financial Year"), Column("FinancialYearID"), NotNull, ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }

    [DisplayName("Is Default")]
    public bool? IsDefault { get => fields.IsDefault[this]; set => fields.IsDefault[this] = value; }

    [DisplayName("Remarks")]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }

    [DisplayName("Financial Year Financial Year Name"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName))]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }
}