﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.BanksRow>;
using MyRow = ReconnBooks.Default.BanksRow;

namespace ReconnBooks.Default;

public interface IBanksRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class BanksRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IBanksRetrieveHandler
{
    public BanksRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}