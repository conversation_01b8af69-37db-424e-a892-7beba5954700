using Microsoft.Extensions.DependencyInjection;
using Serenity.Extensions.Pages;
using System.IO;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Hosting;
using Serenity.Reporting;
using Microsoft.Net.Http.Headers;
using System.Net;
using System.Globalization;
using System.IO.Compression;
using System.Text.Json;
using Newtonsoft.Json;

namespace ReconnBooks.Modules.Common.Reporting;


[Route("Services/Default/BulkReports/[action]")]
public class BulkReportsEndpoint : ServiceEndpoint
{
    private readonly IReportFactory reportFactory ;
    private readonly IReportRenderer reportRenderer;

    public BulkReportsEndpoint(IReportFactory reportFactory, IReportRenderer reportRenderer)
    {
        this.reportFactory = reportFactory;
        this.reportRenderer = reportRenderer;
    }

    public FileContentResponse Download(BulkReportServiceRequest request, [FromServices] IReportFactory reportFactory, [FromServices] IReportRenderer reportRenderer)
    {
        var key = request.Key;
        var entityIDs = request.EntityIDs;

        if (string.IsNullOrEmpty(key) || string.IsNullOrEmpty(entityIDs))
        {
            throw new ValidationError("InvalidRequest", "Key or Entity IDs cannot be null or empty.");
        }

        var entityIds = JsonConvert.DeserializeObject<List<int>>(entityIDs);

        if (entityIds == null || !entityIds.Any())
        {
            throw new ValidationError("InvalidRequest", "No Entity IDs provided.");
        }

        using (var zipStream = new MemoryStream())
        {
            using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
            {
                foreach (var entityId in entityIds)
                {
                    string jsonEntityId = JSON.Stringify(new { ID = entityId });
                    // Generate report for each ID
                    var report = reportFactory.Create(key, jsonEntityId, validatePermission: true);
                    var result = reportRenderer.Render(report, new ReportRenderOptions
                    {
                        ExportFormat = "pdf",
                        PreviewMode = false,
                        ReportKey = key,
                        ReportParams = jsonEntityId,
                    });

                    // Add the PDF to the ZIP archive
                    var entryName = $"{request.PDFFileName}_{entityId}.pdf";
                    var zipEntry = archive.CreateEntry(entryName, CompressionLevel.Fastest);

                    using (var entryStream = zipEntry.Open())
                    {
                        entryStream.Write(result.ContentBytes, 0, result.ContentBytes.Length);
                    }
                }
            }

            var content = Convert.ToBase64String(zipStream.ToArray());
            return new FileContentResponse
            {
                Content = content,
                ContentType = "application/zip",
                FileName = $"{request.ZippedFileName}_{DateTime.Today.ToString("ddMMyyyy")}.zip"
            };
        }
    }

    public class BulkReportServiceRequest : ServiceRequest
    {
        public string Key { get; set; }
        public string EntityIDs { get; set; }
        public string PDFFileName { get; set; }
        public string ZippedFileName { get; set; }
    }

    public class FileContentResponse : ServiceResponse
    {
        public string Content { get; set; }
        public string ContentType { get; set; }
        public string FileName { get; set; }
    }
}


