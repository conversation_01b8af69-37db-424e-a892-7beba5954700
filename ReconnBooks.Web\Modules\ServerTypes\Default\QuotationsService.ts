﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { QuotationsRow } from "./QuotationsRow";

export namespace QuotationsService {
    export const baseUrl = 'Default/Quotations';

    export declare function Create(request: SaveRequest<QuotationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<QuotationsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<QuotationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<QuotationsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<QuotationsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<QuotationsRow>>;
    export declare function EmailQuotation(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/Quotations/Create",
        Update: "Default/Quotations/Update",
        Delete: "Default/Quotations/Delete",
        Retrieve: "Default/Quotations/Retrieve",
        List: "Default/Quotations/List",
        EmailQuotation: "Default/Quotations/EmailQuotation"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'EmailQuotation'
    ].forEach(x => {
        (<any>QuotationsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}