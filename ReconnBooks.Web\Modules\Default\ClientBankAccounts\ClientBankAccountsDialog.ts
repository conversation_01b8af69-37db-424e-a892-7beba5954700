import { ClientBankAccountsForm, ClientBankAccountsRow } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { GridEditorDialog } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.ClientBankAccountsDialog')
export class ClientBankAccountsDialog extends GridEditorDialog<ClientBankAccountsRow> {
    protected getFormKey() { return ClientBankAccountsForm.formKey; }
    protected getLocalTextDbPrefix() { return ClientBankAccountsRow.localTextPrefix; }

    protected form = new ClientBankAccountsForm(this.idPrefix);
}