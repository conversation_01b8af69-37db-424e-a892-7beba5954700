﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { PoAmendmentDetailsRow } from "./PoAmendmentDetailsRow";
import { PoAmendmentsRow } from "./PoAmendmentsRow";

export namespace PoAmendmentsService {
    export const baseUrl = 'Default/PoAmendments';

    export declare function Create(request: SaveRequest<PoAmendmentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PoAmendmentsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PoAmendmentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PoAmendmentsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PoAmendmentsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PoAmendmentsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromPurchaseOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<PoAmendmentDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PoAmendmentDetailsRow>>;

    export const Methods = {
        Create: "Default/PoAmendments/Create",
        Update: "Default/PoAmendments/Update",
        Delete: "Default/PoAmendments/Delete",
        Retrieve: "Default/PoAmendments/Retrieve",
        List: "Default/PoAmendments/List",
        GetNextNumber: "Default/PoAmendments/GetNextNumber",
        GetFromPurchaseOrderDetails: "Default/PoAmendments/GetFromPurchaseOrderDetails"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromPurchaseOrderDetails'
    ].forEach(x => {
        (<any>PoAmendmentsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}