﻿CREATE TABLE [dbo].[ReceiptDetails] (
    [ReceiptDetailId]   INT             IDENTITY (1, 1) NOT NULL,
    [ReceiptId]         INT             NOT NULL,
    [InvoiceId]         INT             NOT NULL,
    [TDSRateId]         INT             NULL,
    [TDSAmount]         DECIMAL (18, 2) NULL,
    [TCSRateId]         INT             NULL,
    [TCSAmount]         DECIMAL (18, 2) NULL,
    [AmountReceived]    DECIMAL (18, 2) NULL,
    [InvoiceGrandTotal] DECIMAL (18, 2) NULL,
    [TaxableAmount]     DECIMAL (18, 2) NULL,
    [BalanceReceivable] DECIMAL (18, 2) NULL,
    CONSTRAINT [PK_ReceiptDetails] PRIMARY KEY CLUSTERED ([ReceiptDetailId] ASC),
    CONSTRAINT [FK_ReceiptDetails_Invoices] FOREIGN KEY ([InvoiceId]) REFERENCES [dbo].[Invoices] ([InvoiceId]),
    CONSTRAINT [FK_ReceiptDetails_Receipts] FOREIGN KEY ([ReceiptId]) REFERENCES [dbo].[Receipts] ([ReceiptId]),
    CONSTRAINT [FK_ReceiptDetails_TDSRates] FOREIGN KEY ([TDSRateId]) REFERENCES [dbo].[TDSRates] ([TDSRateId]),
    CONSTRAINT [FK_ReceiptDetails_TCSRates] FOREIGN KEY ([TCSRateId]) REFERENCES [dbo].[TCSRates] ([TCSRateId])
);


GO
CREATE NONCLUSTERED INDEX [Invoices]
    ON [dbo].[ReceiptDetails]([InvoiceId] ASC);


GO
CREATE NONCLUSTERED INDEX [Receipts]
    ON [dbo].[ReceiptDetails]([ReceiptId] ASC);

