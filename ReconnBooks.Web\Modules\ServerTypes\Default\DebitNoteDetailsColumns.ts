﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { DebitNoteDetailsRow } from "./DebitNoteDetailsRow";

export interface DebitNoteDetailsColumns {
    RowNumber: Column<DebitNoteDetailsRow>;
    DebitNoteNo: Column<DebitNoteDetailsRow>;
    CommodityName: Column<DebitNoteDetailsRow>;
    CommodityCode: Column<DebitNoteDetailsRow>;
    CommodityType: Column<DebitNoteDetailsRow>;
    PoQuantity: Column<DebitNoteDetailsRow>;
    PoUnitUnitName: Column<DebitNoteDetailsRow>;
    ReturnedQuantity: Column<DebitNoteDetailsRow>;
    ReturnedUnitUnitName: Column<DebitNoteDetailsRow>;
    SerialNos: Column<DebitNoteDetailsRow>;
    UnitPrice: Column<DebitNoteDetailsRow>;
    UnitAmount: Column<DebitNoteDetailsRow>;
    DiscountPercent: Column<DebitNoteDetailsRow>;
    DiscountAmountPerUnit: Column<DebitNoteDetailsRow>;
    NetDiscountAmount: Column<DebitNoteDetailsRow>;
    TaxableAmountPerUnit: Column<DebitNoteDetailsRow>;
    NetTaxableAmount: Column<DebitNoteDetailsRow>;
    GSTRateRemarks: Column<DebitNoteDetailsRow>;
    IGSTRate: Column<DebitNoteDetailsRow>;
    IGSTAmountPerUnit: Column<DebitNoteDetailsRow>;
    NetIGSTAmount: Column<DebitNoteDetailsRow>;
    CGSTRate: Column<DebitNoteDetailsRow>;
    CGSTAmountPerUnit: Column<DebitNoteDetailsRow>;
    NetCGSTAmount: Column<DebitNoteDetailsRow>;
    SGSTRate: Column<DebitNoteDetailsRow>;
    SGSTAmountPerUnit: Column<DebitNoteDetailsRow>;
    NetSGSTAmount: Column<DebitNoteDetailsRow>;
    NetAmount: Column<DebitNoteDetailsRow>;
    NetPricePerUnit: Column<DebitNoteDetailsRow>;
    RejectionReason: Column<DebitNoteDetailsRow>;
    AssessmentRemarks: Column<DebitNoteDetailsRow>;
    ReplacementMethod: Column<DebitNoteDetailsRow>;
    Remarks: Column<DebitNoteDetailsRow>;
    PurchaseOrderDetailCommodityDescription: Column<DebitNoteDetailsRow>;
    PurchaseReturnDetailCommodityDescription: Column<DebitNoteDetailsRow>;
    CommodityDescription: Column<DebitNoteDetailsRow>;
    DebitNoteDetailId: Column<DebitNoteDetailsRow>;
}

export class DebitNoteDetailsColumns extends ColumnsBase<DebitNoteDetailsRow> {
    static readonly columnsKey = 'Default.DebitNoteDetails';
    static readonly Fields = fieldsProxy<DebitNoteDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types