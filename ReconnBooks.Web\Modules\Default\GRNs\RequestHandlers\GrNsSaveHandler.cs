using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GrNsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GrNsRow;

namespace ReconnBooks.Default;

public interface IGrNsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GrNsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGrNsSaveHandler
{
    public GrNsSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}