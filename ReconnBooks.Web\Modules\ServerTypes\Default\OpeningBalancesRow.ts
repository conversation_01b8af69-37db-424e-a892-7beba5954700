﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface OpeningBalancesRow {
    RowNumber?: number;
    OpeningBalanceId?: number;
    FromDate?: string;
    ToDate?: string;
    Ammount?: number;
    LedgerAccountId?: number;
    FinancialYearId?: number;
    Remarks?: string;
    FinancialYearName?: string;
}

export abstract class OpeningBalancesRow {
    static readonly idProperty = 'OpeningBalanceId';
    static readonly nameProperty = 'Remarks';
    static readonly localTextPrefix = 'Default.OpeningBalances';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<OpeningBalancesRow>();
}