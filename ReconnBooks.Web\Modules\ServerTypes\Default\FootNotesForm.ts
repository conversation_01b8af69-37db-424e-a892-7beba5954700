﻿import { TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface FootNotesForm {
    FootNote: TextAreaEditor;
    Remarks: TextAreaEditor;
}

export class FootNotesForm extends PrefixedContext {
    static readonly formKey = 'Default.FootNotes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!FootNotesForm.init)  {
            FootNotesForm.init = true;

            var w0 = TextAreaEditor;

            initFormType(FootNotesForm, [
                'FootNote', w0,
                'Remarks', w0
            ]);
        }
    }
}