namespace ReconnBooks.Modules.Common.Helpers;

public static class NumberToWordsConverter
{
    public static string Convert(int number)
    {
        if (number == 0)
            return "Zero";

        if (number < 0)
            return "minus " + Convert(Math.Abs(number));

        string words = "";

        if ((number / 10000000) > 0)
        {
            words += Convert(number / 10000000) + " Crore ";
            number %= 10000000;
        }

        if ((number / 100000) > 0)
        {
            words += Convert(number / 100000) + " Lakh ";
            number %= 100000;
        }

        if ((number / 1000) > 0)
        {
            words += Convert(number / 1000) + " Thousand ";
            number %= 1000;
        }

        if ((number / 100) > 0)
        {
            words += Convert(number / 100) + " Hundred ";
            number %= 100;
        }

        if (number > 0)
        {
            if (words != "")
                words += "and ";

            var unitsMap = new[] { "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };
            var tensMap = new[] { "Zero", "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };

            if (number < 20)
                words += unitsMap[number];
            else
            {
                words += tensMap[number / 10];
                if ((number % 10) > 0)
                    words += "-" + unitsMap[number % 10];
            }
        }

        return words;
    }

    public static string Convert(decimal number)
    {
        if (number == 0)
            return "Zero";

        if (number < 0)
            return "minus " + Convert(Math.Abs(number));

        string words = "";

        int intPortion = (int)number;
        decimal fraction = (number - intPortion) * 100;
        int decPortion = (int)fraction;

        words = Convert(intPortion);
        if (decPortion > 0)
        {
            words += " and ";
            words += Convert(decPortion);
        }
        return words + " Only";
    }
}
