﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { BanksRow } from "./BanksRow";

export interface BanksColumns {
    RowNumber: Column<BanksRow>;
    BankName: Column<BanksRow>;
    BankShortName: Column<BanksRow>;
    BankId: Column<BanksRow>;
}

export class BanksColumns extends ColumnsBase<BanksRow> {
    static readonly columnsKey = 'Default.Banks';
    static readonly Fields = fieldsProxy<BanksColumns>();
}