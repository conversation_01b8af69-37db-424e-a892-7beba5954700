import { DataGrid } from "@serenity-is/corelib";
import { ToolButton } from "@serenity-is/corelib";

export interface ClientExportOptions {
    grid: DataGrid<any, any>;
    fileName?: string;
    hint?: string;
    title?: string;
    separator?: boolean;
}

export function DetailsGridExcelExportButton(options: ClientExportOptions): ToolButton {
    return {
        hint: options.hint ?? "Export current view to Excel",
        title: options.title ?? "Excel",
        cssClass: "export-xlsx-button",
        onClick: () => {
            const view = options.grid?.getView();
            const items = view?.getItems();

            if (!items || items.length === 0) {
                alert("No data to export.");  
                return;
            }

            const slickGrid = (options.grid as any).slickGrid;
            const allColumns = slickGrid.getColumns();

            const keys = allColumns.map((c: any) => c.field);
            const headers = allColumns.map((c: any) => `"${c.name?.replace(/"/g, '""')}"`);

            const dataRows = items.map(item =>
                keys.map(key => {
                    let value = item[key as keyof typeof item];
                    if (value == null) return '';
                    if (typeof value === 'object') return '';
                    if (typeof value === 'string') {
                        value = value.replace(/\r?\n|\r/g, ' ').replace(/"/g, '""');
                    }
                    return `"${value}"`;
                }).join(',')
            );

            const csvContent = [headers.join(','), ...dataRows].join('\r\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${options.fileName ?? 'Export'}_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },
        separator: options.separator ?? true
    };
}
