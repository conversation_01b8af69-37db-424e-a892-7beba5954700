﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface UqCsRow {
    RowNumber?: number;
    UqcId?: number;
    QuantityName?: string;
    QuantityType?: string;
    UniqueQuantityCode?: string;
    UqcName?: string;
    UQCDescription?: string;
}

export abstract class UqCsRow {
    static readonly idProperty = 'UqcId';
    static readonly nameProperty = 'QuantityName';
    static readonly localTextPrefix = 'Default.UqCs';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<UqCsRow>();
}