﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { ReceiptsRow } from "./ReceiptsRow";

export namespace ReceiptsService {
    export const baseUrl = 'Default/Receipts';

    export declare function Create(request: SaveRequest<ReceiptsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ReceiptsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ReceiptsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ReceiptsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ReceiptsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ReceiptsRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;

    export const Methods = {
        Create: "Default/Receipts/Create",
        Update: "Default/Receipts/Update",
        Delete: "Default/Receipts/Delete",
        Retrieve: "Default/Receipts/Retrieve",
        List: "Default/Receipts/List",
        GetNextNumber: "Default/Receipts/GetNextNumber"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber'
    ].forEach(x => {
        (<any>ReceiptsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}