﻿import { StringEditor, ServiceLookupEditor, DateEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ReturnsFilingFormsForm {
    ReturnsFilingForm: StringEditor;
    NatureOfSupplyId: ServiceLookupEditor;
    SupplyTypeId: ServiceLookupEditor;
    Description: StringEditor;
    Filer: StringEditor;
    Frequency: StringEditor;
    DueDate: DateEditor;
    Remarks: StringEditor;
}

export class ReturnsFilingFormsForm extends PrefixedContext {
    static readonly formKey = 'Default.ReturnsFilingForms';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ReturnsFilingFormsForm.init)  {
            ReturnsFilingFormsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;
            var w2 = DateEditor;

            initFormType(ReturnsFilingFormsForm, [
                'ReturnsFilingForm', w0,
                'NatureOfSupplyId', w1,
                'SupplyTypeId', w1,
                'Description', w0,
                'Filer', w0,
                'Frequency', w0,
                'DueDate', w2,
                'Remarks', w0
            ]);
        }
    }
}