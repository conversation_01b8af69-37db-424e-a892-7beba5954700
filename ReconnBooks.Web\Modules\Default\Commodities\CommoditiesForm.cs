using Serenity.ComponentModel;

namespace ReconnBooks.Default.Forms;

[FormScript("Default.Commodities")]
[BasedOnRow(typeof(CommoditiesRow), CheckNames = true)]
public class CommoditiesForm
{
    [Category("Product/Service Details")]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Commodity Type")]
    public int CommodityTypeId { get; set; }

    [DisplayName("Product Code"), LabelWidth(120)]
    public string CommodityCode { get; set; }

    [FullWidth(UntilNext = true)]
    [DisplayName("Product Name")]
    public string CommodityName { get; set; }
    
    [TextAreaEditor(Rows = 3)]
    [DisplayName("Description")]
    public string CommodityDescription { get; set; }

    [HalfWidth(UntilNext = true)]
    [DisplayName("Unit of Measure")]
    public int UnitId { get; set; }

    [ReadOnly(true), LabelWidth(120)]
    public string UQCQuantityName { get; set; }


    //---------------------------------------------
    [Category("HSN/SAC Details"), Collapsible]
    [DisplayName("HSN/SAC Code")]
    public string HSNSACCodeId { get; set; }

    [DisplayName("GST Rate"), LabelWidth(120)]
    public int GSTRateId { get; set; }

    [FullWidth, TextAreaEditor(Rows = 4)]
    [DisplayName("HSN/SAC Description")]
    public string HSNSACDescription { get; set; }

    [Hidden]
    [DisplayName("HSN/SAC Group")]
    public string HSNSACGroup { get; set; }

    [Category("Price Details"), Collapsible]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Sales Price")]
    public decimal SalesPrice { get; set; }

    [DisplayName("Purchase Price"), LabelWidth(120)]
    public decimal PurchasePrice { get; set; }

    [DisplayName("List Price")]
    public decimal ListPrice { get; set; }

    [DisplayName("MRP"), LabelWidth(120)]
    public decimal MRP { get; set; }
    
    [Hidden]
    [FullWidth]
    public string Remarks { get; set; }

    [Category("Other Details"), Collapsible(Collapsed = true)]
    [HalfWidth(UntilNext = true)]
    [DisplayName("Product Category")]
    public int ProductCategoryId { get; set; }

    [DisplayName("Product Group")]
    public int ProductGroupId { get; set; }

    [DisplayName("Product Type")]
    public int ProductTypeId { get; set; }

    [DisplayName("Product Make")]
    public int ProductMakeId { get; set; }

    [DisplayName("Product Weight")]
    public string ProductWeight { get; set; }

    [DisplayName("Product/Service Status"), LabelWidth(180)]
    public bool CommodityStatus { get; set; }

    [Category("Upload Product Image"), Collapsible(Collapsed = true)]
    [FullWidth(UntilNext = true)]
    [DisplayName("Product Image")]
    [MultipleFileUploadEditor]
    public string ProductImage { get; set; }

    //public int ClientId { get; set; }
}