﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { ConsultantBankAccountsRow } from "./ConsultantBankAccountsRow";

export interface ConsultantsRow {
    RowNumber?: number;
    ConsultantId?: number;
    ConsultantName?: string;
    ConsultantCode?: string;
    Address?: string;
    CityId?: number;
    PINCode?: string;
    PhoneNo?: string;
    FaxNo?: string;
    HomePage?: string;
    Logo?: string;
    TitleId?: number;
    ContactPerson?: string;
    DesignationId?: number;
    MobileNo?: string;
    AlternateNo?: string;
    EMail?: string;
    GSTIN?: string;
    PlaceOfSupplyId?: number;
    NatureOfSupplyId?: number;
    SupplyTypeId?: number;
    PAN?: string;
    IECNo?: string;
    UdyamNo?: string;
    CINNo?: string;
    TANNo?: string;
    ConsultantBankAccountsList?: ConsultantBankAccountsRow[];
    TagLine?: string;
    ConsultantDSC?: string;
    BusinessTypeId?: number;
    BusinessGroupId?: number;
    BusinessCategoryId?: number;
    InvoiceNoFormat?: string;
    Disclaimer?: string;
    CityName?: string;
    TitleOfRespect?: string;
    Designation?: string;
    PlaceOfSupplyStateName?: string;
    NatureOfSupply?: string;
    SupplyType?: string;
    BusinessType?: string;
    BusinessGroup?: string;
    BusinessCategory?: string;
}

export abstract class ConsultantsRow {
    static readonly idProperty = 'ConsultantId';
    static readonly nameProperty = 'ConsultantName';
    static readonly localTextPrefix = 'Default.Consultants';
    static readonly lookupKey = 'Default.Consultants';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<ConsultantsRow>('Default.Consultants') }
    static async getLookupAsync() { return getLookupAsync<ConsultantsRow>('Default.Consultants') }

    static readonly deletePermission = 'Administration:Consultants:Admin';
    static readonly insertPermission = 'Administration:Consultants:Admin';
    static readonly readPermission = 'Administration:Consultants:Admin';
    static readonly updatePermission = 'Administration:Consultants:Admin';

    static readonly Fields = fieldsProxy<ConsultantsRow>();
}