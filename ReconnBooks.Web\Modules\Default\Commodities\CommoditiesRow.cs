using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.RowBehaviors;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("Commodities")]
[DisplayName("Products/Services"), InstanceName("Products/Services"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript(LookupType = typeof(MultiClientRowLookup<>))]
[UniqueConstraint(new[] { "CommodityTypeId", "CommodityName", "CommodityCode", "CommodityDescription" })]
public sealed partial class CommoditiesRow : Row<CommoditiesRow.RowFields>, IIdRow, INameRow, IMultiClientRow, IRowNumberedRow
{
    const string jCommodityType = nameof(jCommodityType);
    const string jUnit = nameof(jUnit);
    const string jHSNSACCode = nameof(jHSNSACCode);
    const string jGSTRate = nameof(jGSTRate);
    const string jProductCategory = nameof(jProductCategory);
    const string jProductGroup = nameof(jProductGroup);
    const string jProductType = nameof(jProductType);
    const string jProductMake = nameof(jProductMake);
    const string jclient = nameof(jclient);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Commodity Id"), Identity, IdProperty, QuickSearch]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType)), LookupEditor(typeof(CommodityTypesRow), Async = true), LookupInclude]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Product/Service Name"), Size(500), NotNull, QuickSearch, NameProperty, LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

    [DisplayName("Product/Service Code"), Size(50), QuickSearch, LookupInclude]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }

    [DisplayName("Product/Service Description"), LookupInclude]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    [DisplayName("Unit of Measure"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jUnit), TextualField(nameof(UnitName))]
    [LookupEditor(typeof(UnitsRow),InplaceAdd =true, Async = true), LookupInclude]
    public int? UnitId { get => fields.UnitId[this]; set => fields.UnitId[this] = value; }

    [DisplayName("GST Rate"), Column("GSTRateId"), NotNull, ForeignKey(typeof(GstRatesRow)), LeftJoin(jGSTRate)]
    [TextualField(nameof(GstRateRemarks)), LookupEditor(typeof(GstRatesRow), Async = true), LookupInclude]
    public int? GSTRateId { get => fields.GSTRateId[this]; set => fields.GSTRateId[this] = value; }

    [DisplayName("UQC Name"), Origin(jUnit, nameof(UnitsRow.UQCQuantityName)), LookupInclude]
    public string UQCQuantityName { get => fields.UQCQuantityName[this]; set => fields.UQCQuantityName[this] = value; }

    //--HSN/SAC Code-----------------------------------------------------
    [DisplayName("HSN/SAC Code"), ForeignKey(typeof(HsnsacCodesRow)), LeftJoin(jHSNSACCode)]
    [TextualField(nameof(HSNSACDescription)), LookupInclude]
    [ServiceLookupEditor(typeof(HsnsacCodesRow), InplaceAdd = true,
        Service = "Default/HsnsacCodes/List", IdField = nameof(HsnsacCodesRow.HSNSACCodeId),
        TextField = nameof(HsnsacCodesRow.HSNSACCode))]
    public long? HSNSACCodeId { get => fields.HSNSACCodeId[this]; set => fields.HSNSACCodeId[this] = value; }

    [DisplayName("HSN/SAC Code"), QuickSearch]
    [Origin(jHSNSACCode, nameof(HsnsacCodesRow.HSNSACCode)), LookupInclude]
    public string HSNSACCode { get => fields.HSNSACCode[this]; set => fields.HSNSACCode[this] = value; }

    [DisplayName("HSN/SAC Description")]
    [Origin(jHSNSACCode, nameof(HsnsacCodesRow.HSNSACDescription))]
    public string HSNSACDescription { get => fields.HSNSACDescription[this]; set => fields.HSNSACDescription[this] = value; }

    [DisplayName("HSN/SAC Group")]
    [Origin(jHSNSACCode, nameof(HsnsacCodesRow.HSNSACGroup)), LookupInclude]
    public string HSNSACGroup { get => fields.HSNSACGroup[this]; set => fields.HSNSACGroup[this] = value; }
    //---------------------------------------

    [DisplayName("Purchase/Cost Price"), Size(18), Scale(2)]
    public decimal? PurchasePrice { get => fields.PurchasePrice[this]; set => fields.PurchasePrice[this] = value; }

    [DisplayName("Sales/Billing Price"), Size(18), Scale(2), LookupInclude]
    public decimal? SalesPrice { get => fields.SalesPrice[this]; set => fields.SalesPrice[this] = value; }

    [DisplayName("List Price"), Size(18), Scale(2)]
    public decimal? ListPrice { get => fields.ListPrice[this]; set => fields.ListPrice[this] = value; }

    [DisplayName("Purchase/Cost Price"), Size(18), Scale(2)]
    public decimal? MRP { get => fields.MRP[this]; set => fields.MRP[this] = value; }

    [DisplayName("Product Category"), ForeignKey(typeof(ProductCategoriesRow)), LeftJoin(jProductCategory)]
    [TextualField(nameof(ProductCategoryCategoryName))]
    [ServiceLookupEditor(typeof(ProductCategoriesRow), InplaceAdd =true, Service = "Default/ProductCategories/List")]
    public int? ProductCategoryId { get => fields.ProductCategoryId[this]; set => fields.ProductCategoryId[this] = value; }

    [DisplayName("Product Group"), ForeignKey(typeof(ProductGroupsRow)), LeftJoin(jProductGroup), TextualField(nameof(ProductGroup))]
    [ServiceLookupEditor(typeof(ProductGroupsRow), InplaceAdd =true, Service = "Default/ProductGroups/List")]
    public int? ProductGroupId { get => fields.ProductGroupId[this]; set => fields.ProductGroupId[this] = value; }

    [DisplayName("Product Type"), ForeignKey(typeof(ProductTypesRow)), LeftJoin(jProductType), TextualField(nameof(ProductType))]
    [ServiceLookupEditor(typeof(ProductTypesRow), InplaceAdd =true, Service = "Default/ProductTypes/List")]
    public int? ProductTypeId { get => fields.ProductTypeId[this]; set => fields.ProductTypeId[this] = value; }

    [DisplayName("Product Make"), ForeignKey(typeof(ProductMakeRow)), LeftJoin(jProductMake), TextualField(nameof(ProductMake))]
    [ServiceLookupEditor(typeof(ProductMakeRow), InplaceAdd =true, Service = "Default/ProductMake/List")]
    public int? ProductMakeId { get => fields.ProductMakeId[this]; set => fields.ProductMakeId[this] = value; }

    [DisplayName("Product Weight"), Size(50)]
    public string ProductWeight { get => fields.ProductWeight[this]; set => fields.ProductWeight[this] = value; }

    [DisplayName("Product Image")]
    public string ProductImage { get => fields.ProductImage[this]; set => fields.ProductImage[this] = value; }

    [DisplayName("Status"), NotNull, DefaultValue(false)]
    public bool? CommodityStatus { get => fields.CommodityStatus[this]; set => fields.CommodityStatus[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("Client"), NotNull]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Commodity Type"), QuickSearch, Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType))]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Unit of Measure"), Origin(jUnit, nameof(UnitsRow.UnitName))]
    public string UnitName { get => fields.UnitName[this]; set => fields.UnitName[this] = value; }

    [DisplayName("GST Rate Remarks"), Origin(jGSTRate, nameof(GstRatesRow.Remarks))]
    public string GstRateRemarks { get => fields.GstRateRemarks[this]; set => fields.GstRateRemarks[this] = value; }

    [DisplayName("Product Category"), Origin(jProductCategory, nameof(ProductCategoriesRow.CategoryName))]
    public string ProductCategoryCategoryName { get => fields.ProductCategoryCategoryName[this]; set => fields.ProductCategoryCategoryName[this] = value; }

    [DisplayName("Product Group"), Origin(jProductGroup, nameof(ProductGroupsRow.ProductGroup))]
    public string ProductGroup { get => fields.ProductGroup[this]; set => fields.ProductGroup[this] = value; }

    [DisplayName("Product Type"), Origin(jProductType, nameof(ProductTypesRow.ProductType))]
    public string ProductType { get => fields.ProductType[this]; set => fields.ProductType[this] = value; }

    [DisplayName("Product Make"), Origin(jProductMake, nameof(ProductMakeRow.ProductMake))]
    public string ProductMake { get => fields.ProductMake[this]; set => fields.ProductMake[this] = value; }

    public Int32Field ClientIdField => fields.ClientId;//add for MultiTenancy 
}