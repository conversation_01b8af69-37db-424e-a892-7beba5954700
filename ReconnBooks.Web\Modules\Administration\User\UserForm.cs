using Serenity.Pro.Extensions;

namespace ReconnBooks.Administration.Forms;
[<PERSON><PERSON>("Administration.User")]
[BasedOnRow(typeof(UserRow), CheckNames = true)]
public class UserForm
{
    [HalfWidth]
    public Int32 UserTypeId { get; set; }


    [DisplayName("Client")]
    public int? ClientId { get; set; }

    //[DisplayName("Client Name")]
    //public int? ClientName { get; set; }

    [DisplayName("Consultant")]
    public int? ConsultantId { get; set; }

    //[DisplayName("Consultant Name")]
    //public int? ConsultantName { get; set; }


    public Int32 EmployeeId { get; set; }

    [HalfWidth(UntilNext = true)]
    //[LabelWidth(200, UntilNext = true)]
    public string Username { get; set; }
    public string DisplayName { get; set; }

    [PasswordEditor, Required(true)]
    public string Password { get; set; }

    [PasswordEditor, Required(true)]
    public string PasswordConfirm { get; set; }


    [FullWidth(UntilNext = true)]
    public List<int> ClientList { get; set; }

    [LookupEditor(typeof(RoleRow), Multiple = true)]
    public List<int> Roles { get; set; }

    //[HalfWidth(UntilNext = true)]
    //[DisplayName("2-Factor Authentication")]
    //public TwoFactorAuthType TwoFactorAuth { get; set; }
  
    [HalfWidth(UntilNext = true)]
    public string MobilePhoneNumber { get; set; }

    [EmailAddressEditor]
    public string Email { get; set; }
    //public bool MobilePhoneVerified { get; set; }

    [FullWidth]
    public string UserImage { get; set; }

    [HalfWidth(UntilNext = true)]
    [OneWay]
    public string Source { get; set; }

}