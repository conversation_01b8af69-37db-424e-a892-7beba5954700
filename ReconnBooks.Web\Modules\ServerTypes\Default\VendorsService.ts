﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { VendorsRow } from "./VendorsRow";

export namespace VendorsService {
    export const baseUrl = 'Default/Vendors';

    export declare function Create(request: SaveRequest<VendorsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<VendorsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<VendorsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<VendorsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<VendorsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<VendorsRow>>;

    export const Methods = {
        Create: "Default/Vendors/Create",
        Update: "Default/Vendors/Update",
        Delete: "Default/Vendors/Delete",
        Retrieve: "Default/Vendors/Retrieve",
        List: "Default/Vendors/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>VendorsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}