﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.ClientBankAccountsRow>;
using MyRow = ReconnBooks.Default.ClientBankAccountsRow;

namespace ReconnBooks.Default;

public interface IClientBankAccountsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class ClientBankAccountsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IClientBankAccountsListHandler
{
    public ClientBankAccountsListHandler(IRequestContext context)
            : base(context)
    {
    }
}