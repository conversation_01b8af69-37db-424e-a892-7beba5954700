﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface GstRatesRow {
    RowNumber?: number;
    GSTRateId?: number;
    IGSTPercent?: number;
    IGSTCessPercent?: number;
    CGSTPercent?: number;
    CGSTCessPercent?: number;
    SGSTPercent?: number;
    SGSTCessPercent?: number;
    WefDate?: string;
    Current?: boolean;
    Remarks?: string;
}

export abstract class GstRatesRow {
    static readonly idProperty = 'GSTRateId';
    static readonly nameProperty = 'Remarks';
    static readonly localTextPrefix = 'Default.GstRates';
    static readonly lookupKey = 'Default.GstRates';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<GstRatesRow>('Default.GstRates') }
    static async getLookupAsync() { return getLookupAsync<GstRatesRow>('Default.GstRates') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GstRatesRow>();
}