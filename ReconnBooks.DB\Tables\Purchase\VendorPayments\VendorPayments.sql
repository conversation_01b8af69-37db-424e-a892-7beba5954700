﻿CREATE TABLE [dbo].[VendorPayments]
(
    [VendorPaymentId]    INT             IDENTITY (1, 1) NOT NULL,
    [PaymentVoucherNo]   NVARCHAR (50)   NOT NULL,
    [PaymentVoucherDate] DATETIME        NOT NULL,
    [VendorId]           INT             NOT NULL,
    
    [TotalPayable]       DECIMAL (18, 2) NULL,
    [OnAccount]          DECIMAL (18, 2) NULL,
    
    [TDSRateId]          INT             NULL,
    [TDSAmount]          DECIMAL (18, 2) NULL,
    [TCSRateId]          INT             NULL,
    [TCSAmount]          DECIMAL (18, 2) NULL,
    
    [NetPayable]         DECIMAL (18, 2) NULL,
    [AmountPaid]         DECIMAL (18, 2) NOT NULL,
    [Narration]          NVARCHAR (MAX)  NULL,
    
    [FinancialYearId]    INT             NOT NULL,
    [ModeOfPaymentId]    INT             NOT NULL,
    [ChequeDDNo]         NVARCHAR (100)  NULL,
    [ChequeDDDate]       DATETIME        NULL,
    [BankBranchName]     NVARCHAR (250)  NULL,
    [PaymentRefNo]       NVARCHAR (250)  NULL,
    
    [Remarks]            NVARCHAR (MAX)  NULL,
    [ClientId]           INT             CONSTRAINT [DF_VendorPayments_ClientId] DEFAULT ((0)) NOT NULL,
    
    [PreparedByUserId]   INT             NULL,
    [PreparedDate]       DATETIME        NULL,
    [VerifiedByUserId]   INT             NULL,
    [VerifiedDate]       DATETIME        NULL,
    [AuthorizedByUserId] INT             NULL,
    [AuthorizedDate]     DATETIME        NULL,
    [ModifiedByUserId]   INT             NULL,
    [ModifiedDate]       DATETIME        NULL,
    [CancelledByUserId]  INT             NULL,
    [CancelledDate]      DATETIME        NULL,
    [AuthorizedStatus]   BIT             CONSTRAINT [DF_VendorPayments_AuthorizedStatus] DEFAULT ((0)) NOT NULL,
    
    CONSTRAINT [PK_VendorPayments] PRIMARY KEY CLUSTERED ([VendorPaymentId] ASC),
    CONSTRAINT [FK_VendorPayments_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_VendorPayments_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorPayments_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorPayments_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorPayments_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorPayments_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_VendorPayments_Vendors] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendors] ([VendorId]),
    CONSTRAINT [FK_VendorPayments_TDSRates] FOREIGN KEY ([TDSRateId]) REFERENCES [dbo].[TDSRates] ([TDSRateId]),
    CONSTRAINT [FK_VendorPayments_TCSRates] FOREIGN KEY ([TCSRateId]) REFERENCES [dbo].[TCSRates] ([TCSRateId]),
    CONSTRAINT [FK_VendorPayments_ModeOfPayments] FOREIGN KEY ([ModeOfPaymentId]) REFERENCES [dbo].[ModeOfPayments] ([ModeOfPaymentId]),
    CONSTRAINT [FK_VendorPayments_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId])
);


GO
CREATE NONCLUSTERED INDEX [FinancialYearId]
    ON [dbo].[VendorPayments]([FinancialYearId] ASC);


GO
CREATE NONCLUSTERED INDEX [Vendors]
    ON [dbo].[VendorPayments]([VendorId] ASC);


GO
CREATE NONCLUSTERED INDEX [ModeOfPaymentId]
    ON [dbo].[VendorPayments]([ModeOfPaymentId] ASC);

