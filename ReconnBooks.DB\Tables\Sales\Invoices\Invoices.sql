﻿CREATE TABLE [dbo].[Invoices] 
(
    [InvoiceId]             INT             NOT NULL    IDENTITY (1,1),
    [InvoiceNo]             NVARCHAR (50)   NOT NULL,
    [InvoiceDate]           DATETIME            NULL,
    [CustomerId]            INT             NOT NULL,

  --[NatureOfSupplyId]      INT             NOT NULL,     --NatureOfSupply merged with SupplyTypeId
    [SupplyTypeId]          INT             NOT NULL,
    [FinancialYearId]       INT             NOT NULL,
    
    [SalesOrderId]          INT                 NULL,
    [ProformaInvoiceId]     INT                 NULL,
    [OrderRefNo]            NVARCHAR (50)       NULL,   --Customer Order reference No.
    [OrderRefDate]          SMALLDATETIME       NULL,   --Customer Order reference Date.
    
	[InvoiceAmount]         DECIMAL (18, 2)     NULL,   --Bring it from InvoiceDetails table
	[RoundingOff]           DECIMAL (18, 2)     NULL,
	[GrandTotal]            DECIMAL (18, 2)     NULL,

    [eWayBillNo]            NVARCHAR (100)      NULL,
	[eWayBillDate]          DATETIME            NULL,
    [DeliveryNoteId]        INT                 NULL,
    [PaymentTermsId]        INT                 NULL,
    [PaymentDueDate]        DATETIME            NULL,

    [DeliveryAt]            NVARCHAR (300)      NULL,
    [DeliveryCityId]        INT                 NULL,
    [DeliveryPinCode]       INT                 NULL,
    [ShippedVia]            NVARCHAR (250)      NULL,
    [ShippingDocketNo]      NVARCHAR (50)       NULL,
    [VehicleNo]             NVARCHAR (20)       NULL,
    [Inspection]            NVARCHAR (100)      NULL,
    [SalesPersonId]         INT                 NULL,
    [UploadFiles]           NVARCHAR (500)      NULL,

    [Remarks]               NVARCHAR (MAX)      NULL,
    [TransactionStatus]     SMALLINT            NULL    DEFAULT ((0)),
    [ClientId]              INT	            NOT NULL    CONSTRAINT [DF_Invoices_ClientId]	DEFAULT ((0)),
    -------------------Authorization Details-------------
    [PreparedByUserId]      INT	                NULL,
    [PreparedDate]          DATETIME            NULL,
    [VerifiedByUserId]      INT                 NULL,
    [VerifiedDate]          DATETIME            NULL,
    [AuthorizedByUserId]    INT                 NULL,
    [AuthorizedDate]        DATETIME            NULL,
    [ModifiedByUserId]      INT                 NULL,
    [ModifiedDate]          DATETIME            NULL,
    [CancelledByUserId]     INT                 NULL,
    [CancelledDate]			DATETIME            NULL,
    [CancelReason]         NVARCHAR (MAX) NULL,
    [AuthorizedStatus]      BIT             NOT NULL    DEFAULT ((0)),
   CONSTRAINT [PK_Invoices] PRIMARY KEY CLUSTERED ([InvoiceId] ASC),
    CONSTRAINT [FK_Invoices_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Invoices_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Invoices_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Invoices_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Invoices_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_Invoices_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_Invoices_SupplySections] FOREIGN KEY ([SupplyTypeId]) REFERENCES [dbo].[SupplyTypes] ([SupplyTypeId]),
    CONSTRAINT [FK_Invoices_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_Invoices_PaymentTerms] FOREIGN KEY ([PaymentTermsId]) REFERENCES [dbo].[PaymentTerms] ([PaymentTermsId]),
    CONSTRAINT [FK_Invoices_SalesPersons] FOREIGN KEY ([SalesPersonId]) REFERENCES [dbo].[Employees] ([EmployeeId]),
    CONSTRAINT [FK_Invoices_DeliveryCities] FOREIGN KEY ([DeliveryCityId]) REFERENCES [dbo].[Cities] ([CityId]),
    CONSTRAINT [FK_Invoices_DeliveryNotes] FOREIGN KEY ([DeliveryNoteId]) REFERENCES [dbo].[DeliveryNotes] ([DeliveryNoteId]),
    CONSTRAINT [FK_Invoices_Customers] FOREIGN KEY ([CustomerId]) REFERENCES [dbo].[Customers] ([CustomerId])    
);
GO
CREATE NONCLUSTERED INDEX [SupplyTypes]
    ON [dbo].[Invoices]([SupplyTypeId] ASC);
GO
CREATE NONCLUSTERED INDEX [Customers]
    ON [dbo].[Invoices]([CustomerId] ASC);
GO
CREATE NONCLUSTERED INDEX [ProformaInvoices]
    ON [dbo].[Invoices]([ProformaInvoiceId] ASC);
    GO
CREATE NONCLUSTERED INDEX [SalesOrders]
    ON [dbo].[Invoices]([SalesOrderId] ASC);
GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[Invoices]([ClientId] ASC);
