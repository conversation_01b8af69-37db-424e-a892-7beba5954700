﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { PurchaseOrderDetailsRow } from "./PurchaseOrderDetailsRow";

export namespace PurchaseOrderDetailsService {
    export const baseUrl = 'Default/PurchaseOrderDetails';

    export declare function Create(request: SaveRequest<PurchaseOrderDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PurchaseOrderDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PurchaseOrderDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PurchaseOrderDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PurchaseOrderDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PurchaseOrderDetailsRow>>;

    export const Methods = {
        Create: "Default/PurchaseOrderDetails/Create",
        Update: "Default/PurchaseOrderDetails/Update",
        Delete: "Default/PurchaseOrderDetails/Delete",
        Retrieve: "Default/PurchaseOrderDetails/Retrieve",
        List: "Default/PurchaseOrderDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>PurchaseOrderDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}