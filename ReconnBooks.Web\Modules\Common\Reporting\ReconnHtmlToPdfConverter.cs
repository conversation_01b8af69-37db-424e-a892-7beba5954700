using Microsoft.AspNetCore.Hosting;
using Serenity.Reporting;
using System.Diagnostics;

namespace ReconnBooks.Modules.Common.Reporting;

public class ReconnHtmlToPdfConverter(IOptions<PuppeteerHtmlToPdfSettings> options = null,
    IWebHostEnvironment webHostEnvironment = null, IFileSystem fileSystem = null) : IHtmlToPdfConverter
{
    private readonly IFileSystem fileSystem = fileSystem ?? new PhysicalFileSystem();

    protected virtual PuppeteerSharp.SupportedBrowser GetBrowserType()
    {
        var s = options?.Value?.Product;
        if (!string.IsNullOrEmpty(s) && !Enum.TryParse<PuppeteerSharp.SupportedBrowser>(s, out var p))
            return p;

        return PuppeteerSharp.SupportedBrowser.Chrome;
    }

    protected virtual string GetFetcherDownloadPath()
    {
        var path = options?.Value?.DownloadPath;
        if (!string.IsNullOrEmpty(path))
            return path;

        if (!string.IsNullOrEmpty(webHostEnvironment?.ContentRootPath))
            return fileSystem.Combine(webHostEnvironment?.ContentRootPath, "App_Data",
                GetBrowserType() == PuppeteerSharp.SupportedBrowser.Chrome ? "chrome" : "firefox");

        return null;
    }

    protected virtual void SetConverterOptions(PuppeteerHtmlToPdf converter)
    {
        var downloadPath = GetFetcherDownloadPath();
        if (!string.IsNullOrEmpty(downloadPath))
            converter.FetcherOptions.Path = downloadPath;
    }

    public byte[] Convert(IHtmlToPdfOptions options)
    {
        var converter = new ReconnHtmlToPdfLauncher(options, webHostEnvironment);
        SetConverterOptions(converter);
        return converter.Execute();
    }
}
