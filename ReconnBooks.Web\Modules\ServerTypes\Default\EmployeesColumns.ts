﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { EmployeesRow } from "./EmployeesRow";

export interface EmployeesColumns {
    RowNumber: Column<EmployeesRow>;
    EmployeeName: Column<EmployeesRow>;
    ConsultantName: Column<EmployeesRow>;
    ClientName: Column<EmployeesRow>;
    Address: Column<EmployeesRow>;
    CityName: Column<EmployeesRow>;
    PostalCode: Column<EmployeesRow>;
    DateOfBirth: Column<EmployeesRow>;
    HireDate: Column<EmployeesRow>;
    EmailId: Column<EmployeesRow>;
    MobileNo: Column<EmployeesRow>;
    AlternateNo: Column<EmployeesRow>;
    PhoneNo: Column<EmployeesRow>;
    Notes: Column<EmployeesRow>;
    UploadDocuments: Column<EmployeesRow>;
    EmployeeId: Column<EmployeesRow>;
}

export class EmployeesColumns extends ColumnsBase<EmployeesRow> {
    static readonly columnsKey = 'Default.Employees';
    static readonly Fields = fieldsProxy<EmployeesColumns>();
}