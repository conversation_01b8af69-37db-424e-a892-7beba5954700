﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { ReceiptDetailsRow } from "./ReceiptDetailsRow";

export interface ReceiptDetailsColumns {
    RowNumber: Column<ReceiptDetailsRow>;
    ReceiptNo: Column<ReceiptDetailsRow>;
    InvoiceNo: Column<ReceiptDetailsRow>;
    InvoiceDate: Column<ReceiptDetailsRow>;
    TaxableAmount: Column<ReceiptDetailsRow>;
    InvoiceGrandTotal: Column<ReceiptDetailsRow>;
    BalanceReceivable: Column<ReceiptDetailsRow>;
    AmountReceived: Column<ReceiptDetailsRow>;
    TDSRate: Column<ReceiptDetailsRow>;
    TDSAmount: Column<ReceiptDetailsRow>;
    IsTDSPaid: Column<ReceiptDetailsRow>;
    TCSRateId: Column<ReceiptDetailsRow>;
    TCSAmount: Column<ReceiptDetailsRow>;
    IsTCSPaid: Column<ReceiptDetailsRow>;
    ReceiptDetailId: Column<ReceiptDetailsRow>;
}

export class ReceiptDetailsColumns extends ColumnsBase<ReceiptDetailsRow> {
    static readonly columnsKey = 'Default.ReceiptDetails';
    static readonly Fields = fieldsProxy<ReceiptDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types