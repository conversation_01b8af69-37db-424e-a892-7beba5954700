﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { NatureOfSupplyRow } from "./NatureOfSupplyRow";

export interface NatureOfSupplyColumns {
    RowNumber: Column<NatureOfSupplyRow>;
    NatureOfSupplyId: Column<NatureOfSupplyRow>;
    NatureOfSupply: Column<NatureOfSupplyRow>;
    Description: Column<NatureOfSupplyRow>;
}

export class NatureOfSupplyColumns extends ColumnsBase<NatureOfSupplyRow> {
    static readonly columnsKey = 'Default.NatureOfSupply';
    static readonly Fields = fieldsProxy<NatureOfSupplyColumns>();
}