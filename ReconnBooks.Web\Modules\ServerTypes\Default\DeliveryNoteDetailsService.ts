﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { DeliveryNoteDetailsRow } from "./DeliveryNoteDetailsRow";

export namespace DeliveryNoteDetailsService {
    export const baseUrl = 'Default/DeliveryNoteDetails';

    export declare function Create(request: SaveRequest<DeliveryNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<DeliveryNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<DeliveryNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<DeliveryNoteDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<DeliveryNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<DeliveryNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/DeliveryNoteDetails/Create",
        Update: "Default/DeliveryNoteDetails/Update",
        Delete: "Default/DeliveryNoteDetails/Delete",
        Retrieve: "Default/DeliveryNoteDetails/Retrieve",
        List: "Default/DeliveryNoteDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>DeliveryNoteDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}