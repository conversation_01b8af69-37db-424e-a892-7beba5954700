﻿import { StringEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { StatesDialog } from "../../Default/States/StatesDialog";

export interface DistrictsForm {
    District: StringEditor;
    DistrictCode: StringEditor;
    Headquarters: StringEditor;
    StateId: ServiceLookupEditor;
}

export class DistrictsForm extends PrefixedContext {
    static readonly formKey = 'Default.Districts';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DistrictsForm.init)  {
            DistrictsForm.init = true;

            var w0 = StringEditor;
            var w1 = ServiceLookupEditor;

            initFormType(DistrictsForm, [
                'District', w0,
                'DistrictCode', w0,
                'Headquarters', w0,
                'StateId', w1
            ]);
        }
    }
}

queueMicrotask(() => [StatesDialog]); // referenced dialogs