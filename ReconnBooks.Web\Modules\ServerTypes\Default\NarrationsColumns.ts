﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { NarrationsRow } from "./NarrationsRow";

export interface NarrationsColumns {
    RowNumber: Column<NarrationsRow>;
    NarrationId: Column<NarrationsRow>;
    NarrationText: Column<NarrationsRow>;
    Remarks: Column<NarrationsRow>;
    ClientName: Column<NarrationsRow>;
}

export class NarrationsColumns extends ColumnsBase<NarrationsRow> {
    static readonly columnsKey = 'Default.Narrations';
    static readonly Fields = fieldsProxy<NarrationsColumns>();
}