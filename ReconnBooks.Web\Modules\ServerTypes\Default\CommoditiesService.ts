﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { ExcelImportRequest, ExcelImportResponse } from "@serenity-is/extensions";
import { CommoditiesRow } from "./CommoditiesRow";

export namespace CommoditiesService {
    export const baseUrl = 'Default/Commodities';

    export declare function Create(request: SaveRequest<CommoditiesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CommoditiesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CommoditiesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CommoditiesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CommoditiesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CommoditiesRow>>;
    export declare function ExcelImport(request: ExcelImportRequest, onSuccess?: (response: ExcelImportResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ExcelImportResponse>;

    export const Methods = {
        Create: "Default/Commodities/Create",
        Update: "Default/Commodities/Update",
        Delete: "Default/Commodities/Delete",
        Retrieve: "Default/Commodities/Retrieve",
        List: "Default/Commodities/List",
        ExcelImport: "Default/Commodities/ExcelImport"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'ExcelImport'
    ].forEach(x => {
        (<any>CommoditiesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}