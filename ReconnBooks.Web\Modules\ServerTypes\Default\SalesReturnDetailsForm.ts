﻿import { ServiceLookupEditor, TextAreaEditor, DecimalEditor, StringEditor, LookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { RejectionReasonsDialog } from "../../Default/RejectionReasons/RejectionReasonsDialog";
import { ReplacementMethodsDialog } from "../../Default/ReplacementMethods/ReplacementMethodsDialog";

export interface SalesReturnDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    InvoiceQuantity: DecimalEditor;
    InvoiceUnitId: ServiceLookupEditor;
    NetPricePerUnit: DecimalEditor;
    RejectedQuantity: DecimalEditor;
    RejectedUnitId: ServiceLookupEditor;
    NetAmount: DecimalEditor;
    RejectedItemSerialNo: StringEditor;
    RejectionReasonId: ServiceLookupEditor;
    AssessmentRemarks: StringEditor;
    ReplacementMethodId: ServiceLookupEditor;
    Remarks: StringEditor;
    DeliveryNoteDetailId: ServiceLookupEditor;
    InvoiceDetailId: LookupEditor;
}

export class SalesReturnDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.SalesReturnDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SalesReturnDetailsForm.init)  {
            SalesReturnDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = DecimalEditor;
            var w4 = StringEditor;
            var w5 = LookupEditor;

            initFormType(SalesReturnDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'InvoiceQuantity', w3,
                'InvoiceUnitId', w0,
                'NetPricePerUnit', w3,
                'RejectedQuantity', w3,
                'RejectedUnitId', w0,
                'NetAmount', w3,
                'RejectedItemSerialNo', w4,
                'RejectionReasonId', w0,
                'AssessmentRemarks', w4,
                'ReplacementMethodId', w0,
                'Remarks', w4,
                'DeliveryNoteDetailId', w0,
                'InvoiceDetailId', w5
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, RejectionReasonsDialog, ReplacementMethodsDialog]); // referenced dialogs