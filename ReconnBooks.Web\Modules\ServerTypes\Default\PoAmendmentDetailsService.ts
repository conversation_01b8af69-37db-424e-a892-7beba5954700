﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { PoAmendmentDetailsRow } from "./PoAmendmentDetailsRow";

export namespace PoAmendmentDetailsService {
    export const baseUrl = 'Default/PoAmendmentDetails';

    export declare function Create(request: SaveRequest<PoAmendmentDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PoAmendmentDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PoAmendmentDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PoAmendmentDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PoAmendmentDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PoAmendmentDetailsRow>>;

    export const Methods = {
        Create: "Default/PoAmendmentDetails/Create",
        Update: "Default/PoAmendmentDetails/Update",
        Delete: "Default/PoAmendmentDetails/Delete",
        Retrieve: "Default/PoAmendmentDetails/Retrieve",
        List: "Default/PoAmendmentDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>PoAmendmentDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}