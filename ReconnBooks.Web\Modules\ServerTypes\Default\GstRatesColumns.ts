﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { GstRatesRow } from "./GstRatesRow";

export interface GstRatesColumns {
    RowNumber: Column<GstRatesRow>;
    GSTRateId: Column<GstRatesRow>;
    IGSTPercent: Column<GstRatesRow>;
    IGSTCessPercent: Column<GstRatesRow>;
    CGSTPercent: Column<GstRatesRow>;
    CGSTCessPercent: Column<GstRatesRow>;
    SGSTPercent: Column<GstRatesRow>;
    SGSTCessPercent: Column<GstRatesRow>;
    WefDate: Column<GstRatesRow>;
    Current: Column<GstRatesRow>;
    Remarks: Column<GstRatesRow>;
}

export class GstRatesColumns extends ColumnsBase<GstRatesRow> {
    static readonly columnsKey = 'Default.GstRates';
    static readonly Fields = fieldsProxy<GstRatesColumns>();
}