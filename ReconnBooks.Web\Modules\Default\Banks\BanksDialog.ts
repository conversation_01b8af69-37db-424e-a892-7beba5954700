import { BanksForm, BanksRow, BanksService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.BanksDialog')
export class BanksDialog extends PendingChangesConfirmDialog<BanksRow> {
    protected getFormKey() { return BanksForm.formKey; }
    protected getRowDefinition() { return BanksRow; }
    protected getService() { return BanksService.baseUrl; }

    protected form = new BanksForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}