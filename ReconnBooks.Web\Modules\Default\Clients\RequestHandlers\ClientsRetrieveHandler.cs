﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.ClientsRow>;
using MyRow = ReconnBooks.Default.ClientsRow;

namespace ReconnBooks.Default;

public interface IClientsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IClientsRetrieveHandler
{
    public ClientsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}