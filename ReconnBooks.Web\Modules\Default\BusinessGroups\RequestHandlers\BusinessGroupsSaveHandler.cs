﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.BusinessGroupsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.BusinessGroupsRow;

namespace ReconnBooks.Default;

public interface IBusinessGroupsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class BusinessGroupsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IBusinessGroupsSaveHandler
{
    public BusinessGroupsSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}