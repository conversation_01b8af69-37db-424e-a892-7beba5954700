﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { SalesOrderDetailsRow } from "./SalesOrderDetailsRow";
import { SalesOrdersRow } from "./SalesOrdersRow";

export namespace SalesOrdersService {
    export const baseUrl = 'Default/SalesOrders';

    export declare function Create(request: SaveRequest<SalesOrdersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<SalesOrdersRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<SalesOrdersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<SalesOrdersRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<SalesOrdersRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesOrdersRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromQuotationDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<SalesOrderDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<SalesOrderDetailsRow>>;
    export declare function EmailSalesOrder(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/SalesOrders/Create",
        Update: "Default/SalesOrders/Update",
        Delete: "Default/SalesOrders/Delete",
        Retrieve: "Default/SalesOrders/Retrieve",
        List: "Default/SalesOrders/List",
        GetNextNumber: "Default/SalesOrders/GetNextNumber",
        GetFromQuotationDetails: "Default/SalesOrders/GetFromQuotationDetails",
        EmailSalesOrder: "Default/SalesOrders/EmailSalesOrder"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromQuotationDetails', 
        'EmailSalesOrder'
    ].forEach(x => {
        (<any>SalesOrdersService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}