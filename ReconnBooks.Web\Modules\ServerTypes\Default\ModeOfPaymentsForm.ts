﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface ModeOfPaymentsForm {
    ModeOfPayment: StringEditor;
    Description: StringEditor;
}

export class ModeOfPaymentsForm extends PrefixedContext {
    static readonly formKey = 'Default.ModeOfPayments';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ModeOfPaymentsForm.init)  {
            ModeOfPaymentsForm.init = true;

            var w0 = StringEditor;

            initFormType(ModeOfPaymentsForm, [
                'ModeOfPayment', w0,
                'Description', w0
            ]);
        }
    }
}