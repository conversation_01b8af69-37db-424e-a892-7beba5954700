﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DateTimeEditor, DecimalEditor, ImageUploadEditor, MultipleImageUploadEditor, EnumEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { DeliveryNotesDialog } from "../../Default/DeliveryNotes/DeliveryNotesDialog";
import { InvoiceDetailsGridEditor } from "../../Default/InvoiceDetails/InvoiceDetailsGridEditor";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { ProformaInvoicesDialog } from "../../Default/ProformaInvoices/ProformaInvoicesDialog";
import { SalesOrdersDialog } from "../../Default/SalesOrders/SalesOrdersDialog";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";
import { TransactionStatus } from "../Modules/Common.Helpers.TransactionStatus";

export interface InvoicesForm {
    InvoiceNo: StringEditor;
    InvoiceDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    ShipToCustomerId: ServiceLookupEditor;
    ShippingAddress: StringEditor;
    ShippingCityName: StringEditor;
    ShippingPinCode: StringEditor;
    ShippingGSTIN: StringEditor;
    ShippingPlaceOfSupplyStateName: StringEditor;
    OrderRefNo: StringEditor;
    OrderRefDate: DateEditor;
    ProformaInvoiceId: ServiceLookupEditor;
    SalesOrderId: ServiceLookupEditor;
    EWayBillNo: StringEditor;
    EWayBillDate: DateTimeEditor;
    InvoiceDetailsList: InvoiceDetailsGridEditor;
    DeliveryNoteId: ServiceLookupEditor;
    InvoiceAmount: DecimalEditor;
    PaymentTermsId: ServiceLookupEditor;
    RoundingOff: DecimalEditor;
    PaymentDueDate: DateEditor;
    GrandTotal: DecimalEditor;
    ShippedVia: StringEditor;
    ShippingDocketNo: StringEditor;
    VehicleNo: StringEditor;
    Inspection: StringEditor;
    SalesPersonId: ServiceLookupEditor;
    Remarks: StringEditor;
    EInvoiceQRCode: ImageUploadEditor;
    IRN: StringEditor;
    AcknowledgementNo: StringEditor;
    AcknowledgementDate: DateEditor;
    UploadFiles: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelReason: StringEditor;
    CancelledDate: DateEditor;
    TransactionStatus: EnumEditor;
    AuthorizedStatus: BooleanEditor;
}

export class InvoicesForm extends PrefixedContext {
    static readonly formKey = 'Default.Invoices';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!InvoicesForm.init)  {
            InvoicesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = DateTimeEditor;
            var w5 = InvoiceDetailsGridEditor;
            var w6 = DecimalEditor;
            var w7 = ImageUploadEditor;
            var w8 = MultipleImageUploadEditor;
            var w9 = EnumEditor;
            var w10 = BooleanEditor;

            initFormType(InvoicesForm, [
                'InvoiceNo', w0,
                'InvoiceDate', w1,
                'CustomerId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'ShipToCustomerId', w2,
                'ShippingAddress', w0,
                'ShippingCityName', w0,
                'ShippingPinCode', w0,
                'ShippingGSTIN', w0,
                'ShippingPlaceOfSupplyStateName', w0,
                'OrderRefNo', w0,
                'OrderRefDate', w1,
                'ProformaInvoiceId', w2,
                'SalesOrderId', w2,
                'EWayBillNo', w0,
                'EWayBillDate', w4,
                'InvoiceDetailsList', w5,
                'DeliveryNoteId', w2,
                'InvoiceAmount', w6,
                'PaymentTermsId', w2,
                'RoundingOff', w6,
                'PaymentDueDate', w1,
                'GrandTotal', w6,
                'ShippedVia', w0,
                'ShippingDocketNo', w0,
                'VehicleNo', w0,
                'Inspection', w0,
                'SalesPersonId', w2,
                'Remarks', w0,
                'EInvoiceQRCode', w7,
                'IRN', w0,
                'AcknowledgementNo', w0,
                'AcknowledgementDate', w1,
                'UploadFiles', w8,
                'PreparedByUserId', w3,
                'PreparedDate', w4,
                'VerifiedByUserId', w3,
                'VerifiedDate', w4,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w4,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelReason', w0,
                'CancelledDate', w1,
                'TransactionStatus', w9,
                'AuthorizedStatus', w10
            ]);
        }
    }
}

[TransactionStatus]; // referenced types
queueMicrotask(() => [CustomersDialog, SupplyTypesDialog, ProformaInvoicesDialog, SalesOrdersDialog, DeliveryNotesDialog, PaymentTermsDialog]); // referenced dialogs