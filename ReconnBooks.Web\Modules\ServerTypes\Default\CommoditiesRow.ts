﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface CommoditiesRow {
    RowNumber?: number;
    CommodityId?: number;
    CommodityTypeId?: number;
    CommodityName?: string;
    CommodityCode?: string;
    CommodityDescription?: string;
    UnitId?: number;
    GSTRateId?: number;
    UQCQuantityName?: string;
    HSNSACCodeId?: number;
    HSNSACCode?: string;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    PurchasePrice?: number;
    SalesPrice?: number;
    ListPrice?: number;
    MRP?: number;
    ProductCategoryId?: number;
    ProductGroupId?: number;
    ProductTypeId?: number;
    ProductMakeId?: number;
    ProductWeight?: string;
    ProductImage?: string;
    CommodityStatus?: boolean;
    Remarks?: string;
    ClientId?: number;
    CommodityType?: string;
    UnitName?: string;
    GstRateRemarks?: string;
    ProductCategoryCategoryName?: string;
    ProductGroup?: string;
    ProductType?: string;
    ProductMake?: string;
}

export abstract class CommoditiesRow {
    static readonly idProperty = 'CommodityId';
    static readonly nameProperty = 'CommodityName';
    static readonly localTextPrefix = 'Default.Commodities';
    static readonly lookupKey = 'Default.Commodities';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<CommoditiesRow>('Default.Commodities') }
    static async getLookupAsync() { return getLookupAsync<CommoditiesRow>('Default.Commodities') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<CommoditiesRow>();
}