﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface BusinessGroupsForm {
    BusinessGroup: StringEditor;
    Description: StringEditor;
}

export class BusinessGroupsForm extends PrefixedContext {
    static readonly formKey = 'Default.BusinessGroups';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!BusinessGroupsForm.init)  {
            BusinessGroupsForm.init = true;

            var w0 = StringEditor;

            initFormType(BusinessGroupsForm, [
                'BusinessGroup', w0,
                'Description', w0
            ]);
        }
    }
}