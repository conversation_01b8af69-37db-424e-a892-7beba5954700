﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { DesignationsRow } from "./DesignationsRow";

export interface DesignationsColumns {
    RowNumber: Column<DesignationsRow>;
    DesignationId: Column<DesignationsRow>;
    Designation: Column<DesignationsRow>;
    Description: Column<DesignationsRow>;
}

export class DesignationsColumns extends ColumnsBase<DesignationsRow> {
    static readonly columnsKey = 'Default.Designations';
    static readonly Fields = fieldsProxy<DesignationsColumns>();
}