﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface StatesRow {
    RowNumber?: number;
    StateId?: number;
    StateName?: string;
    StateCode?: string;
    StateCodeNo?: string;
    CountryId?: number;
    CountryName?: string;
    StateNameWithStateCodeAndNo?: string;
}

export abstract class StatesRow {
    static readonly idProperty = 'StateId';
    static readonly nameProperty = 'StateName';
    static readonly localTextPrefix = 'Default.States';
    static readonly lookupKey = 'Default.States';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<StatesRow>('Default.States') }
    static async getLookupAsync() { return getLookupAsync<StatesRow>('Default.States') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<StatesRow>();
}