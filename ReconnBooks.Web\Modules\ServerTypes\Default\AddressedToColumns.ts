﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { AddressedToRow } from "./AddressedToRow";

export interface AddressedToColumns {
    RowNumber: Column<AddressedToRow>;
    AddressedToId: Column<AddressedToRow>;
    AddressedTo: Column<AddressedToRow>;
}

export class AddressedToColumns extends ColumnsBase<AddressedToRow> {
    static readonly columnsKey = 'Default.AddressedTo';
    static readonly Fields = fieldsProxy<AddressedToColumns>();
}