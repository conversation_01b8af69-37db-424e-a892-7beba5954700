﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface VendorContactsRow {
    RowNumber?: number;
    VendorContactId?: number;
    VendorId?: number;
    TitleId?: number;
    ContactName?: string;
    DesignationId?: number;
    DepartmentId?: number;
    OfficePhoneNo?: string;
    ExtensionNo?: string;
    MobileNo?: string;
    AlternateNo?: string;
    Email?: string;
    Status?: boolean;
    VendorName?: string;
    TitleOfRespect?: string;
    Designation?: string;
    DepartmentName?: string;
}

export abstract class VendorContactsRow {
    static readonly idProperty = 'VendorContactId';
    static readonly nameProperty = 'ContactName';
    static readonly localTextPrefix = 'Default.VendorContacts';
    static readonly lookupKey = 'Default.VendorContacts';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<VendorContactsRow>('Default.VendorContacts') }
    static async getLookupAsync() { return getLookupAsync<VendorContactsRow>('Default.VendorContacts') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<VendorContactsRow>();
}