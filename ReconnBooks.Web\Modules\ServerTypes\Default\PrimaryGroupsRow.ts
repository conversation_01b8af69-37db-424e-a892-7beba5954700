﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface PrimaryGroupsRow {
    RowNumber?: number;
    PrimaryGroupId?: number;
    PrimaryGroupCode?: number;
    PrimaryGroupName?: string;
    Remarks?: string;
}

export abstract class PrimaryGroupsRow {
    static readonly idProperty = 'PrimaryGroupId';
    static readonly nameProperty = 'PrimaryGroupName';
    static readonly localTextPrefix = 'Default.PrimaryGroups';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PrimaryGroupsRow>();
}