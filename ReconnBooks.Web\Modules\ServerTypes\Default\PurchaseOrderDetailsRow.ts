﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface PurchaseOrderDetailsRow {
    RowNumber?: number;
    PurchaseOrderDetailId?: number;
    PurchaseOrderId?: number;
    PurchaseOrderNo?: string;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    HSNSACCodeId?: number;
    HSNSACDescription?: string;
    HSNSACGroup?: string;
    HSNSACCode?: string;
    Quantity?: number;
    UnitId?: number;
    UnitName?: string;
    UnitPrice?: number;
    UnitAmount?: number;
    AmendedQuantity?: number;
    AmendedUnitId?: number;
    AmendedPrice?: number;
    PendingQuantity?: number;
    TaxableAmountPerUnit?: number;
    NetTaxableAmount?: number;
    DiscountPercent?: number;
    DiscountAmountPerUnit?: number;
    NetDiscountAmount?: number;
    GSTRateId?: number;
    GSTRateRemarks?: string;
    IGSTRate?: number;
    IGSTAmountPerUnit?: number;
    NetIGSTAmount?: number;
    CGSTRate?: number;
    CGSTAmountPerUnit?: number;
    NetCGSTAmount?: number;
    SGSTRate?: number;
    SGSTAmountPerUnit?: number;
    NetSGSTAmount?: number;
    DummyField?: string;
    NetPricePerUnit?: number;
    NetAmount?: number;
}

export abstract class PurchaseOrderDetailsRow {
    static readonly idProperty = 'PurchaseOrderDetailId';
    static readonly nameProperty = 'PurchaseOrderDetailId';
    static readonly localTextPrefix = 'Default.PurchaseOrderDetails';
    static readonly lookupKey = 'Default.PurchaseOrderDetails';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PurchaseOrderDetailsRow>('Default.PurchaseOrderDetails') }
    static async getLookupAsync() { return getLookupAsync<PurchaseOrderDetailsRow>('Default.PurchaseOrderDetails') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PurchaseOrderDetailsRow>();
}