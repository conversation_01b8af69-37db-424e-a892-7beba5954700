using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("SalesReturnDetails")]
[DisplayName("Sales Return Details"), InstanceName("Sales Return Details"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class SalesReturnDetailsRow : Row<SalesReturnDetailsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow //to add MultiTanancy
{
    const string jSalesReturn = nameof(jSalesReturn);
    const string jDeliveryNoteDetail = nameof(jDeliveryNoteDetail);
    const string jCommodityType = nameof(jCommodityType);
    const string jInvoiceDetail = nameof(jInvoiceDetail);
    const string jCommodity = nameof(jCommodity);
    const string jUnit = nameof(jUnit);
    const string jRejectionReason = nameof(jRejectionReason);
    const string jReplacementMethod = nameof(jReplacementMethod);
    const string jGSTRate = nameof(jGSTRate);
    const string jInvoiceUnit = nameof(jInvoiceUnit);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Sales Return Detail Id"), Identity, IdProperty, NameProperty]
    public int? SalesReturnDetailId { get => fields.SalesReturnDetailId[this]; set => fields.SalesReturnDetailId[this] = value; }

    [DisplayName("Sales Return"), NotNull, ForeignKey(typeof(SalesReturnsRow)), LeftJoin(jSalesReturn), TextualField(nameof(SalesReturnNo))]
    [ServiceLookupEditor(typeof(SalesReturnsRow), Service = "Default/SalesReturns/List")]
    public int? SalesReturnId { get => fields.SalesReturnId[this]; set => fields.SalesReturnId[this] = value; }

    [DisplayName("Delivery Note Detail"), ForeignKey(typeof(DeliveryNoteDetailsRow)), LeftJoin(jDeliveryNoteDetail)]
    [TextualField(nameof(DeliveryNoteDetailProductDescription))]
    [ServiceLookupEditor(typeof(DeliveryNoteDetailsRow), Service = "Default/DeliveryNoteDetails/List")]
    public int? DeliveryNoteDetailId { get => fields.DeliveryNoteDetailId[this]; set => fields.DeliveryNoteDetailId[this] = value; }

    [DisplayName("Invoice Detail"), ForeignKey(typeof(InvoiceDetailsRow)), LeftJoin(jInvoiceDetail)]
    [TextualField(nameof(InvoiceDetailCommodityDescription)), LookupEditor(typeof(InvoiceDetailsRow), Async = true)]
    public int? InvoiceDetailId { get => fields.InvoiceDetailId[this]; set => fields.InvoiceDetailId[this] = value; }

    [DisplayName("Commodity Type"), NotNull, ForeignKey(typeof(CommodityTypesRow)), LeftJoin(jCommodityType)]
    [TextualField(nameof(CommodityType))]
    [ServiceLookupEditor(typeof(CommodityTypesRow), Service = "Default/CommodityTypes/List")]
    public int? CommodityTypeId { get => fields.CommodityTypeId[this]; set => fields.CommodityTypeId[this] = value; }

    [DisplayName("Commodity"), NotNull, ForeignKey(typeof(CommoditiesRow)), LeftJoin(jCommodity), TextualField(nameof(CommodityName))]
    [ServiceLookupEditor(typeof(CommoditiesRow), InplaceAdd = true, Service = "Default/Commodities/List",
        CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public long? CommodityId { get => fields.CommodityId[this]; set => fields.CommodityId[this] = value; }

    //---- Fetching Product Code from Products Master -------------
    [DisplayName("Product Code"), LabelWidth(100)]
    [Origin(jCommodity, nameof(CommoditiesRow.CommodityCode)), TextualField(nameof(CommodityCode)), LookupInclude]
    [CommodityCodeEditor(CascadeFrom = "CommodityTypeId", CascadeField = "CommodityTypeId")]
    public string CommodityCode { get => fields.CommodityCode[this]; set => fields.CommodityCode[this] = value; }
    //--------------------------

    [DisplayName("Commodity Description"), QuickSearch]
    public string CommodityDescription { get => fields.CommodityDescription[this]; set => fields.CommodityDescription[this] = value; }

    //-----------------------------------------------

    [DisplayName("Rejected Quantity"), Size(18), Scale(2), NotNull]
    public decimal? RejectedQuantity { get => fields.RejectedQuantity[this]; set => fields.RejectedQuantity[this] = value; }


    [DisplayName("Unit of Measure"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jUnit), TextualField(nameof(UnitName))]
    [ServiceLookupEditor(typeof(UnitsRow), Service = "Default/Units/List")]
    public int? RejectedUnitId { get => fields.RejectedUnitId[this]; set => fields.RejectedUnitId[this] = value; }

    [DisplayName("Rejected Serial Nos.")]
    public string RejectedItemSerialNo { get => fields.RejectedItemSerialNo[this]; set => fields.RejectedItemSerialNo[this] = value; }

    [DisplayName("Rejection Reason"), ForeignKey(typeof(RejectionReasonsRow)), LeftJoin(jRejectionReason)]
    [TextualField(nameof(RejectionReason))]
    [ServiceLookupEditor(typeof(RejectionReasonsRow), InplaceAdd =true, Service = "Default/RejectionReasons/List")]
    public int? RejectionReasonId { get => fields.RejectionReasonId[this]; set => fields.RejectionReasonId[this] = value; }

    [DisplayName("Assessment Remarks")]
    public string AssessmentRemarks { get => fields.AssessmentRemarks[this]; set => fields.AssessmentRemarks[this] = value; }

    [DisplayName("Replacement Method"), ForeignKey(typeof(ReplacementMethodsRow)), LeftJoin(jReplacementMethod)]
    [TextualField(nameof(ReplacementMethod))]
    [ServiceLookupEditor(typeof(ReplacementMethodsRow), InplaceAdd = true, Service = "Default/ReplacementMethods/List")]
    public int? ReplacementMethodId { get => fields.ReplacementMethodId[this]; set => fields.ReplacementMethodId[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("Sales Return No."), Origin(jSalesReturn, nameof(SalesReturnsRow.SalesReturnNo))]
    public string SalesReturnNo { get => fields.SalesReturnNo[this]; set => fields.SalesReturnNo[this] = value; }

    [DisplayName("Product Description")]
    [Origin(jDeliveryNoteDetail, nameof(DeliveryNoteDetailsRow.CommodityDescription))]
    public string DeliveryNoteDetailProductDescription { get => fields.DeliveryNoteDetailProductDescription[this]; set => fields.DeliveryNoteDetailProductDescription[this] = value; }

    [DisplayName("Commodity Description"), Origin(jInvoiceDetail, nameof(InvoiceDetailsRow.CommodityDescription))]
    public string InvoiceDetailCommodityDescription { get => fields.InvoiceDetailCommodityDescription[this]; set => fields.InvoiceDetailCommodityDescription[this] = value; }

    [DisplayName("Commodity Name"), Origin(jCommodity, nameof(CommoditiesRow.CommodityName)), LookupInclude]
    public string CommodityName { get => fields.CommodityName[this]; set => fields.CommodityName[this] = value; }

    [DisplayName("Commodity Type"), Origin(jCommodityType, nameof(CommodityTypesRow.CommodityType)), LookupInclude]
    public string CommodityType { get => fields.CommodityType[this]; set => fields.CommodityType[this] = value; }

    [DisplayName("Unit"), Origin(jUnit, nameof(UnitsRow.UnitName)), LookupInclude]
    public string UnitName { get => fields.UnitName[this]; set => fields.UnitName[this] = value; }

    [DisplayName("Rejection Reason"), Origin(jRejectionReason, nameof(RejectionReasonsRow.RejectionReason)), LookupInclude]
    public string RejectionReason { get => fields.RejectionReason[this]; set => fields.RejectionReason[this] = value; }

    [DisplayName("Replacement Method"), Origin(jReplacementMethod, nameof(ReplacementMethodsRow.ReplacementMethod)), LookupInclude]
    public string ReplacementMethod { get => fields.ReplacementMethod[this]; set => fields.ReplacementMethod[this] = value; }

    //-----------Added for Invoices-----------

    [DisplayName(" Invoice Quantity"), Size(18), Scale(2), NotNull]
    public decimal? InvoiceQuantity { get => fields.InvoiceQuantity[this]; set => fields.InvoiceQuantity[this] = value; }

    [DisplayName("Unit of Measure"), NotNull, ForeignKey(typeof(UnitsRow)), LeftJoin(jInvoiceUnit), TextualField(nameof(UnitName))]
    [ServiceLookupEditor(typeof(UnitsRow), Service = "Default/Units/List")]
    public int? InvoiceUnitId { get => fields.InvoiceUnitId[this]; set => fields.InvoiceUnitId[this] = value; }

    [DisplayName("Unit Price"), Size(18), Scale(2), NotNull]
    public decimal? UnitPrice { get => fields.UnitPrice[this]; set => fields.UnitPrice[this] = value; }

    [DisplayName("Unit Amount"), Size(18), Scale(2), NotMapped]
    public decimal? NetUnitAmount { get => fields.NetUnitAmount[this]; set => fields.NetUnitAmount[this] = value; }

    //[DisplayName("Discount %"), Size(18), Scale(2)]
    //public decimal? DiscountPercent { get => fields.DiscountPercent[this]; set => fields.DiscountPercent[this] = value; }

    //[DisplayName("Discount Amt./Unit"), Size(18), Scale(2)]
    //public decimal? DiscountAmountPerUnit { get => fields.DiscountAmountPerUnit[this]; set => fields.DiscountAmountPerUnit[this] = value; }

    //[DisplayName("Net Discount Amt."), Size(18), Scale(2)]
    //public decimal? NetDiscountAmount { get => fields.NetDiscountAmount[this]; set => fields.NetDiscountAmount[this] = value; }

    //[DisplayName("Basic Amt./Unit"), Size(18), Scale(2), NotNull, NotMapped]
    //public decimal? BasicAmountPerUnit { get => fields.BasicAmountPerUnit[this]; set => fields.BasicAmountPerUnit[this] = value; }

    //[DisplayName("Net Basic Amt."), Size(18), Scale(2), NotNull, NotMapped]
    //public decimal? NetBasicAmount { get => fields.NetBasicAmount[this]; set => fields.NetBasicAmount[this] = value; }

    //[DisplayName("GST Rate"), Column("GSTRateId"), NotNull, ForeignKey(typeof(GstRatesRow)), LeftJoin(jGstRate)]
    //[TextualField(nameof(GstRateRemarks)), ServiceLookupEditor(typeof(GstRatesRow), Service = "Default/GstRates/List")]
    //public int? GstRateId { get => fields.GstRateId[this]; set => fields.GstRateId[this] = value; }

    //[DisplayName("GST Rate"), Origin(jGstRate, nameof(GstRatesRow.Remarks)), LookupInclude]
    //public string GstRateRemarks { get => fields.GstRateRemarks[this]; set => fields.GstRateRemarks[this] = value; }

    //[DisplayName("IGST Rate(%)"), Column("IGSTRate"), Size(18), Scale(2), NotNull]
    //public decimal? IgstRate { get => fields.IgstRate[this]; set => fields.IgstRate[this] = value; }

    //[DisplayName("IGST Amt./Unit"), Column("IGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    //public decimal? IgstAmountPerUnit { get => fields.IgstAmountPerUnit[this]; set => fields.IgstAmountPerUnit[this] = value; }

    //[DisplayName("Net IGST Amt."), Column("NetIGSTAmount"), Size(18), Scale(2), NotMapped]
    //public decimal? NetIgstAmount { get => fields.NetIgstAmount[this]; set => fields.NetIgstAmount[this] = value; }

    //[DisplayName("CGST Rate(%)"), Column("CGSTRate"), Size(18), Scale(2), NotNull]
    //public decimal? CgstRate { get => fields.CgstRate[this]; set => fields.CgstRate[this] = value; }

    //[DisplayName("CGST Amt./Unit"), Column("CGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    //public decimal? CgstAmountPerUnit { get => fields.CgstAmountPerUnit[this]; set => fields.CgstAmountPerUnit[this] = value; }

    //[DisplayName("Net CGST Amt."), Column("NetCGSTAmount"), Size(18), Scale(2), NotMapped]
    //public decimal? NetCgstAmount { get => fields.NetCgstAmount[this]; set => fields.NetCgstAmount[this] = value; }

    //[DisplayName("SGST Rate(%)"), Column("SGSTRate"), Size(18), Scale(2), NotNull]
    //public decimal? SgstRate { get => fields.SgstRate[this]; set => fields.SgstRate[this] = value; }

    //[DisplayName("SGST Amt./Unit"), Column("SGSTAmountPerUnit"), Size(18), Scale(2), NotMapped]
    //public decimal? SgstAmountPerUnit { get => fields.SgstAmountPerUnit[this]; set => fields.SgstAmountPerUnit[this] = value; }

    //[DisplayName("Net SGST Amt."), Column("NetSGSTAmount"), Size(18), Scale(2), NotMapped]
    //public decimal? NetSgstAmount { get => fields.NetSgstAmount[this]; set => fields.NetSgstAmount[this] = value; }

    //[DisplayName("Dummy Field"), Size(200)]
    //public string DummyField { get => fields.DummyField[this]; set => fields.DummyField[this] = value; }

    [DisplayName("Net Price/Unit"), Size(18), Scale(2)]
    public decimal? NetPricePerUnit { get => fields.NetPricePerUnit[this]; set => fields.NetPricePerUnit[this] = value; }

    [DisplayName("Net Amount"), Size(18), Scale(2)]
    public decimal? NetAmount { get => fields.NetAmount[this]; set => fields.NetAmount[this] = value; }
}