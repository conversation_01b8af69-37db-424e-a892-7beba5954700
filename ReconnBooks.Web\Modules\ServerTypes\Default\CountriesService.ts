﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { CountriesRow } from "./CountriesRow";

export namespace CountriesService {
    export const baseUrl = 'Default/Countries';

    export declare function Create(request: SaveRequest<CountriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<CountriesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<CountriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<CountriesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<CountriesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<CountriesRow>>;

    export const Methods = {
        Create: "Default/Countries/Create",
        Update: "Default/Countries/Update",
        Delete: "Default/Countries/Delete",
        Retrieve: "Default/Countries/Retrieve",
        List: "Default/Countries/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>CountriesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}