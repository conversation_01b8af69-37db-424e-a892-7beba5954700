﻿CREATE TABLE [dbo].[ProductGroups] 
(
    [ProductGroupId] INT            NOT NULL    IDENTITY (1, 1),
    [ProductGroup]   NVARCHAR (50)  NOT NULL,
    [ClientId]       INT            NOT NULL    CONSTRAINT [DF_ItemGroups_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_ProductGroups] PRIMARY KEY CLUSTERED ([ProductGroupId] ASC),
    CONSTRAINT [FK_ProductGroups_Clients]   FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId])
);
GO
CREATE NONCLUSTERED INDEX [ProductGroups]
    ON [dbo].[ProductGroups]([ProductGroup] ASC);
