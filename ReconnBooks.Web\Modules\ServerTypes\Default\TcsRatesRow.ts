﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";

export interface TcsRatesRow {
    RowNumber?: number;
    TCSRateId?: number;
    Section?: string;
    TCSRate?: number;
    TCSRateWithSection?: string;
    NatureOfTransaction?: string;
    Collector?: string;
    Collectee?: string;
    WefDate?: string;
    FinancialYearId?: number;
    IsDefault?: boolean;
    Description?: string;
    FinancialYearName?: string;
}

export abstract class TcsRatesRow {
    static readonly idProperty = 'TCSRateId';
    static readonly nameProperty = 'Section';
    static readonly localTextPrefix = 'Default.TcsRates';
    static readonly lookupKey = 'Default.TcsRates';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<TcsRatesRow>('Default.TcsRates') }
    static async getLookupAsync() { return getLookupAsync<TcsRatesRow>('Default.TcsRates') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<TcsRatesRow>();
}