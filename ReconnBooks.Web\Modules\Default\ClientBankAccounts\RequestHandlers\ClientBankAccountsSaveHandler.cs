﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.ClientBankAccountsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.ClientBankAccountsRow;

namespace ReconnBooks.Default;

public interface IClientBankAccountsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class ClientBankAccountsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IClientBankAccountsSaveHandler
{
    public ClientBankAccountsSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}