﻿import { fieldsProxy } from "@serenity-is/corelib";
import { DataAuditLogType } from "./DataAuditLogType";

export interface DataAuditLogRow {
    LogId?: number;
    LogType?: DataAuditLogType;
    LogDate?: string;
    UserId?: number;
    ClientId?: number;
    ClientName?: string;
    Tablename?: string;
    RecordId?: string;
    FieldName?: string;
    OldValue?: string;
    NewValue?: string;
    Username?: string;
    UserDisplayName?: string;
}

export abstract class DataAuditLogRow {
    static readonly idProperty = 'LogId';
    static readonly localTextPrefix = 'Default.DataAuditLog';
    static readonly deletePermission = 'Administration:Security';
    static readonly insertPermission = 'Administration:Security';
    static readonly readPermission = 'Administration:Security';
    static readonly updatePermission = 'Administration:Security';

    static readonly Fields = fieldsProxy<DataAuditLogRow>();
}