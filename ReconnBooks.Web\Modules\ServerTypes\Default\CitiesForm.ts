﻿import { StringEditor, IntegerEditor, ServiceLookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { DistrictsDialog } from "../../Default/Districts/DistrictsDialog";
import { StatesDialog } from "../../Default/States/StatesDialog";

export interface CitiesForm {
    CityName: StringEditor;
    PINCode: IntegerEditor;
    DistrictId: ServiceLookupEditor;
    StateId: ServiceLookupEditor;
}

export class CitiesForm extends PrefixedContext {
    static readonly formKey = 'Default.Cities';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CitiesForm.init)  {
            CitiesForm.init = true;

            var w0 = StringEditor;
            var w1 = IntegerEditor;
            var w2 = ServiceLookupEditor;

            initFormType(CitiesForm, [
                'CityName', w0,
                'PINCode', w1,
                'DistrictId', w2,
                'StateId', w2
            ]);
        }
    }
}

queueMicrotask(() => [DistrictsDialog, StatesDialog]); // referenced dialogs