﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { QuotationDetailsRow } from "./QuotationDetailsRow";

export interface QuotationDetailsColumns {
    RowNumber: Column<QuotationDetailsRow>;
    CommodityId: Column<QuotationDetailsRow>;
    CommodityName: Column<QuotationDetailsRow>;
    CommodityCode: Column<QuotationDetailsRow>;
    CommodityType: Column<QuotationDetailsRow>;
    Quantity: Column<QuotationDetailsRow>;
    RevisedQuantity: Column<QuotationDetailsRow>;
    UnitName: Column<QuotationDetailsRow>;
    UnitPrice: Column<QuotationDetailsRow>;
    Amount: Column<QuotationDetailsRow>;
    DiscountPercent: Column<QuotationDetailsRow>;
    DiscountAmountPerUnit: Column<QuotationDetailsRow>;
    DiscountAmount: Column<QuotationDetailsRow>;
    TaxableAmountPerUnit: Column<QuotationDetailsRow>;
    NetTaxableAmount: Column<QuotationDetailsRow>;
    GSTRateRemarks: Column<QuotationDetailsRow>;
    IGSTRate: Column<QuotationDetailsRow>;
    PerUnitIGSTAmount: Column<QuotationDetailsRow>;
    NetIGSTAmount: Column<QuotationDetailsRow>;
    CGSTRate: Column<QuotationDetailsRow>;
    PerUnitCGSTAmount: Column<QuotationDetailsRow>;
    NetCGSTAmount: Column<QuotationDetailsRow>;
    SGSTRate: Column<QuotationDetailsRow>;
    PerUnitSGSTAmount: Column<QuotationDetailsRow>;
    NetSGSTAmount: Column<QuotationDetailsRow>;
    NetAmount: Column<QuotationDetailsRow>;
    PerUnitPrice: Column<QuotationDetailsRow>;
    CommodityDescription: Column<QuotationDetailsRow>;
    QuotationNo: Column<QuotationDetailsRow>;
    QuotationDetailId: Column<QuotationDetailsRow>;
}

export class QuotationDetailsColumns extends ColumnsBase<QuotationDetailsRow> {
    static readonly columnsKey = 'Default.QuotationDetails';
    static readonly Fields = fieldsProxy<QuotationDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types