﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { PurchaseReturnDetailsRow } from "./PurchaseReturnDetailsRow";

export namespace PurchaseReturnDetailsService {
    export const baseUrl = 'Default/PurchaseReturnDetails';

    export declare function Create(request: SaveRequest<PurchaseReturnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<PurchaseReturnDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<PurchaseReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<PurchaseReturnDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<PurchaseReturnDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<PurchaseReturnDetailsRow>>;

    export const Methods = {
        Create: "Default/PurchaseReturnDetails/Create",
        Update: "Default/PurchaseReturnDetails/Update",
        Delete: "Default/PurchaseReturnDetails/Delete",
        Retrieve: "Default/PurchaseReturnDetails/Retrieve",
        List: "Default/PurchaseReturnDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>PurchaseReturnDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}