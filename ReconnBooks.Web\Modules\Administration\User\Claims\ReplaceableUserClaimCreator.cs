using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Serenity;
using System.Security.Claims;
using System.Security.Principal;
using System.Threading.Tasks;

namespace ReconnBooks.Modules.Administration.User.Authentication.Claims;

public class ReplaceableUserClaimCreator : DefaultUserClaimCreator, IReplaceableUserClaimCreator
{
    private readonly IUserRetrieveService userRetrieveService;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public ReplaceableUserClaimCreator(IUserRetrieveService userRetrieveService, IHttpContextAccessor httpContextAccessor)
        : base(userRetrieveService)
    {
        this.userRetrieveService = userRetrieveService ?? throw new ArgumentNullException(nameof(userRetrieveService));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
    }


    public override ClaimsPrincipal CreatePrincipal(string username, string authType)
    {
        if (username is null)
            throw new ArgumentNullException(nameof(username));

        var user = (UserDefinition)userRetrieveService.ByUsername(username) ??
            throw new ArgumentOutOfRangeException(nameof(username));

        if (authType == null)
            throw new ArgumentNullException(nameof(authType));

        var identity = new GenericIdentity(username, authType);
        identity.AddClaim(new Claim(ClaimTypes.NameIdentifier, user.Id));

        string clientIdValue = user.ClientId?.ToString() ?? string.Empty;
        identity.AddClaim(new Claim("ClientId", clientIdValue));

        string consultantIdValue = user.ConsultantId?.ToString() ?? string.Empty;
        identity.AddClaim(new Claim("ConsultantId", consultantIdValue));
        //identity.AddClaim(new Claim("ConsultantId", user.ConsultantId.Value.ToInvariant()));


        return new ClaimsPrincipal(identity);

    }

    public async Task ReplaceClaimAsync(string claimType, string newClaimValue)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        var user = httpContext?.User;

        if (user?.Identity is ClaimsIdentity identity)
        {
            // Remove the existing claim
            var existingClaim = identity.FindFirst(claimType);
            if (existingClaim != null)
            {
                identity.RemoveClaim(existingClaim);
            }

            // Add the new claim
            identity.AddClaim(new Claim(claimType, newClaimValue));

            // Re-sign the user in with the updated claims
            var newPrincipal = new ClaimsPrincipal(identity);
            await httpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, newPrincipal);
        }
    }
}
