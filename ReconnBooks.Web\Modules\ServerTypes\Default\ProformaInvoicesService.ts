﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { ProformaInvoiceDetailsRow } from "./ProformaInvoiceDetailsRow";
import { ProformaInvoicesRow } from "./ProformaInvoicesRow";

export namespace ProformaInvoicesService {
    export const baseUrl = 'Default/ProformaInvoices';

    export declare function Create(request: SaveRequest<ProformaInvoicesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<ProformaInvoicesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<ProformaInvoicesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<ProformaInvoicesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<ProformaInvoicesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProformaInvoicesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromSalesOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<ProformaInvoiceDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<ProformaInvoiceDetailsRow>>;
    export declare function EmailProformaInvoice(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/ProformaInvoices/Create",
        Update: "Default/ProformaInvoices/Update",
        Delete: "Default/ProformaInvoices/Delete",
        Retrieve: "Default/ProformaInvoices/Retrieve",
        List: "Default/ProformaInvoices/List",
        GetNextNumber: "Default/ProformaInvoices/GetNextNumber",
        GetFromSalesOrderDetails: "Default/ProformaInvoices/GetFromSalesOrderDetails",
        EmailProformaInvoice: "Default/ProformaInvoices/EmailProformaInvoice"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromSalesOrderDetails', 
        'EmailProformaInvoice'
    ].forEach(x => {
        (<any>ProformaInvoicesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}