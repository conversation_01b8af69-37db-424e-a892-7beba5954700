﻿CREATE TABLE [dbo].[CreditNoteDetails]
(
    [CreditNoteDetailId]    INT				NOT NULL	IDENTITY (1, 1),
    [CreditNoteId]			INT				NOT NULL,

    [InvoiceDetailId]       INT                 NULL,
    [SalesReturnDetailId]   INT                 NULL,

    [CommodityTypeId]       INT			    NOT NULL,
    [CommodityId]           BIGINT			NOT NULL,
	[CommodityDescription]  NVARCHAR (MAX)      NULL,

    --Fetch Invoice Item and Sales Return Items 
    [InvoiceQuantity]       DECIMAL (18, 2) NOT NULL,
    [InvoiceUnitId]         INT             NOT NULL,

    [ReturnedQuantity]		DECIMAL (18,2)	NOT NULL,   --SalesReturnDetails Quantity
    [ReturnedUnitId]	    INT				NOT NULL,   --SalesReturnDetails Unit
    [SerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader. Multiple Sl.Nos are possible

    [UnitPrice]             DECIMAL (18, 2) NOT NULL,   
    [DiscountPercent]       DECIMAL (18, 2)     NULL,   
    [DiscountAmountPerUnit] DECIMAL (18, 2)     NULL,
    [NetDiscountAmount]     DECIMAL (18, 2)     NULL,
    
    [GSTRateId]             INT                 NULL,
    [IGSTRate]              DECIMAL (18, 2)     NULL,      
    [CGSTRate]              DECIMAL (18, 2)     NULL,      
    [SGSTRate]              DECIMAL (18, 2)     NULL,      

    [NetPricePerUnit]       DECIMAL (18, 2)     NULL,   -- Per Unit Price = Net Amount/Quantity
    [NetAmount]             DECIMAL (18, 2)     NULL,   -- Net Amount = Net Price x Quantity

    --Fetch from SalesReturnDetails
    [RejectionReasonId]		INT	                NULL,
    [AssesmentRemarks]		NVARCHAR (MAX)	    NULL,
    [ReplacementMethodId]	INT                 NULL,  --Replace with New Product, Replace with Re-work, Regret Supply Qty, Amend Invoice/DC
    [Remarks]		        NVARCHAR (MAX)	    NULL,

	CONSTRAINT [PK_CreditNoteDetails]		PRIMARY KEY	CLUSTERED   ([CreditNoteDetailId] ASC),
    CONSTRAINT [FK_CreditNoteDetails_CreditNotes]	    FOREIGN KEY ([CreditNoteId])            REFERENCES [dbo].[CreditNotes] ([CreditNoteId]),
    CONSTRAINT [FK_CreditNoteDetails_InvoiceDetails]	FOREIGN KEY ([InvoiceDetailId])         REFERENCES [dbo].[InvoiceDetails] ([InvoiceDetailId]),
    CONSTRAINT [FK_CreditNoteDetails_SalesReturnDetails] FOREIGN KEY ([SalesReturnDetailId])    REFERENCES [dbo].[SalesReturnDetails] ([SalesReturnDetailId]),

    CONSTRAINT [FK_CreditNoteDetails_Commodities]	    FOREIGN KEY ([CommodityId])	            REFERENCES [dbo].[Commodities] ([CommodityId]),
    CONSTRAINT [FK_CreditNoteDetails_InvoiceUnits]	    FOREIGN KEY ([InvoiceUnitId])	        REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_CreditNoteDetails_ReturnedUnits]	    FOREIGN KEY ([ReturnedUnitId])	        REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_CreditNoteDetails_GSTRates]	        FOREIGN KEY ([GSTRateId])	            REFERENCES [dbo].[GSTRates] ([GSTRateId]),
    CONSTRAINT [FK_CreditNoteDetails_RejectionReasons]	FOREIGN KEY ([RejectionReasonId])	    REFERENCES [dbo].[RejectionReasons] ([RejectionReasonId]),
    CONSTRAINT [FK_CreditNoteDetails_ReplacementMethods] FOREIGN KEY ([ReplacementMethodId])	REFERENCES [dbo].[ReplacementMethods] ([ReplacementMethodId]),
);
GO
CREATE NONCLUSTERED INDEX [CreditNotes]
    ON [dbo].[CreditNoteDetails]([CreditNoteId] ASC);
GO
CREATE NONCLUSTERED INDEX [SalesReturnDetails]
    ON [dbo].[CreditNoteDetails]([SalesReturnDetailId] ASC);
GO
CREATE NONCLUSTERED INDEX [InvoiceDetails]
    ON [dbo].[CreditNoteDetails]([InvoiceDetailId] ASC);
    
GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[CreditNoteDetails]([CommodityId] ASC);