﻿import { StringEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface BusinessCategoriesForm {
    BusinessCategory: StringEditor;
    Description: StringEditor;
}

export class BusinessCategoriesForm extends PrefixedContext {
    static readonly formKey = 'Default.BusinessCategories';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!BusinessCategoriesForm.init)  {
            BusinessCategoriesForm.init = true;

            var w0 = StringEditor;

            initFormType(BusinessCategoriesForm, [
                'BusinessCategory', w0,
                'Description', w0
            ]);
        }
    }
}