﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { UnitsDialog } from "../../Default/Units/UnitsDialog";

export interface ProformaInvoiceDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    Quantity: DecimalEditor;
    UnitId: ServiceLookupEditor;
    UnitPrice: DecimalEditor;
    DiscountPercent: DecimalEditor;
    DiscountAmountPerUnit: DecimalEditor;
    NetDiscountAmount: DecimalEditor;
    UnitAmount: DecimalEditor;
    GSTRateId: ServiceLookupEditor;
    TaxableAmountPerUnit: DecimalEditor;
    NetTaxableAmount: DecimalEditor;
    IGSTRate: DecimalEditor;
    IGSTAmountPerUnit: DecimalEditor;
    NetIGSTAmount: DecimalEditor;
    CGSTRate: DecimalEditor;
    CGSTAmountPerUnit: DecimalEditor;
    NetCGSTAmount: DecimalEditor;
    SGSTRate: DecimalEditor;
    SGSTAmountPerUnit: DecimalEditor;
    NetSGSTAmount: DecimalEditor;
    DummyField: StringEditor;
    NetPricePerUnit: DecimalEditor;
    NetAmount: DecimalEditor;
}

export class ProformaInvoiceDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.ProformaInvoiceDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!ProformaInvoiceDetailsForm.init)  {
            ProformaInvoiceDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;

            initFormType(ProformaInvoiceDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'Quantity', w4,
                'UnitId', w0,
                'UnitPrice', w4,
                'DiscountPercent', w4,
                'DiscountAmountPerUnit', w4,
                'NetDiscountAmount', w4,
                'UnitAmount', w4,
                'GSTRateId', w0,
                'TaxableAmountPerUnit', w4,
                'NetTaxableAmount', w4,
                'IGSTRate', w4,
                'IGSTAmountPerUnit', w4,
                'NetIGSTAmount', w4,
                'CGSTRate', w4,
                'CGSTAmountPerUnit', w4,
                'NetCGSTAmount', w4,
                'SGSTRate', w4,
                'SGSTAmountPerUnit', w4,
                'NetSGSTAmount', w4,
                'DummyField', w3,
                'NetPricePerUnit', w4,
                'NetAmount', w4
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, UnitsDialog]); // referenced dialogs