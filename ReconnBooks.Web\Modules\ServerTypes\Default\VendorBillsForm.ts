﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, MultipleImageUploadEditor, DecimalEditor, TextAreaEditor, DateTimeEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";
import { VendorBillDetailsGridEditor } from "../../Default/VendorBillDetails/VendorBillDetailsGridEditor";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface VendorBillsForm {
    VendorBillNo: StringEditor;
    VendorBillDate: DateEditor;
    VendorId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    VendorBillUpload: MultipleImageUploadEditor;
    PurchaseOrderId: ServiceLookupEditor;
    VendorBillAmount: DecimalEditor;
    VendorBillDetailsList: VendorBillDetailsGridEditor;
    TDSRate: ServiceLookupEditor;
    TDSAmount: DecimalEditor;
    RoundingOff: DecimalEditor;
    TCSRate: ServiceLookupEditor;
    TCSAmount: DecimalEditor;
    GrandTotal: DecimalEditor;
    PaymentTermsId: ServiceLookupEditor;
    PaymentDueDate: DateEditor;
    SupplyDueDate: DateEditor;
    DeliveryDate: DateEditor;
    ShippingThru: StringEditor;
    ShippingDocketNo: StringEditor;
    Remarks: TextAreaEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    VendorBillStatus: BooleanEditor;
    AuthorizedStatus: BooleanEditor;
}

export class VendorBillsForm extends PrefixedContext {
    static readonly formKey = 'Default.VendorBills';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!VendorBillsForm.init)  {
            VendorBillsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = MultipleImageUploadEditor;
            var w5 = DecimalEditor;
            var w6 = VendorBillDetailsGridEditor;
            var w7 = TextAreaEditor;
            var w8 = DateTimeEditor;
            var w9 = BooleanEditor;

            initFormType(VendorBillsForm, [
                'VendorBillNo', w0,
                'VendorBillDate', w1,
                'VendorId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'VendorBillUpload', w4,
                'PurchaseOrderId', w2,
                'VendorBillAmount', w5,
                'VendorBillDetailsList', w6,
                'TDSRate', w2,
                'TDSAmount', w5,
                'RoundingOff', w5,
                'TCSRate', w2,
                'TCSAmount', w5,
                'GrandTotal', w5,
                'PaymentTermsId', w2,
                'PaymentDueDate', w1,
                'SupplyDueDate', w1,
                'DeliveryDate', w1,
                'ShippingThru', w0,
                'ShippingDocketNo', w0,
                'Remarks', w7,
                'PreparedByUserId', w3,
                'PreparedDate', w8,
                'VerifiedByUserId', w3,
                'VerifiedDate', w8,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w8,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1,
                'VendorBillStatus', w9,
                'AuthorizedStatus', w9
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, SupplyTypesDialog, PurchaseOrdersDialog, PaymentTermsDialog]); // referenced dialogs