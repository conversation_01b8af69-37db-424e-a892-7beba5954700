﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, DateTimeEditor, BooleanEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { PoAmendmentDetailsGridEditor } from "../../Default/PoAmendmentDetails/PoAmendmentDetailsGridEditor";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface PoAmendmentsForm {
    POAmendmentNo: StringEditor;
    POAmendmentDate: DateEditor;
    VendorId: ServiceLookupEditor;
    PurchaseOrderId: ServiceLookupEditor;
    PurchaseOrderDetailId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    PoAmendmentDetailsList: PoAmendmentDetailsGridEditor;
    FinancialYearId: LookupEditor;
    POAmendmentAmount: DecimalEditor;
    TDSRateId: ServiceLookupEditor;
    RoundingOff: DecimalEditor;
    TCSRateId: ServiceLookupEditor;
    GrandTotal: DecimalEditor;
    Remarks: TextAreaEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    AuthorizedStatus: BooleanEditor;
}

export class PoAmendmentsForm extends PrefixedContext {
    static readonly formKey = 'Default.PoAmendments';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PoAmendmentsForm.init)  {
            PoAmendmentsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = PoAmendmentDetailsGridEditor;
            var w4 = LookupEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = DateTimeEditor;
            var w8 = BooleanEditor;

            initFormType(PoAmendmentsForm, [
                'POAmendmentNo', w0,
                'POAmendmentDate', w1,
                'VendorId', w2,
                'PurchaseOrderId', w2,
                'PurchaseOrderDetailId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'PoAmendmentDetailsList', w3,
                'FinancialYearId', w4,
                'POAmendmentAmount', w5,
                'TDSRateId', w2,
                'RoundingOff', w5,
                'TCSRateId', w2,
                'GrandTotal', w5,
                'Remarks', w6,
                'PreparedByUserId', w4,
                'PreparedDate', w7,
                'VerifiedByUserId', w4,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w4,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w4,
                'ModifiedDate', w1,
                'CancelledByUserId', w4,
                'CancelledDate', w1,
                'AuthorizedStatus', w8
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, PurchaseOrdersDialog]); // referenced dialogs