using MyRow = ReconnBooks.Administration.UserPermissionRow;

namespace ReconnBooks.Administration.Endpoints;
[Route("Services/Administration/UserPermission/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class UserPermissionEndpoint : ServiceEndpoint
{
    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public UserPermissionUpdateResponse Update(IUnitOfWork uow, UserPermissionUpdateRequest request,
        [FromServices] IUserPermissionUpdateHandler handler)
    {
        return handler.Update(uow, request);
    }

    public UserPermissionListResponse List(IDbConnection connection, UserPermissionListRequest request,
        [FromServices] IUserPermissionListHandler handler)
    {
        return handler.List(connection, request);
    }


    //public ListResponse<string> ListRolePermissions(IDbConnection connection, UserPermissionListRequest request,
    //[FromServices] ITypeSource typeSource,
    //[FromServices] ISqlConnections sqlConnections)
    //{
    //    return new MyRepository(Context, typeSource, sqlConnections).ListRolePermissions(connection, request);
    //}

    public ListResponse<string> ListPermissionKeys(
        [FromServices] IPermissionKeyLister permissionKeyLister)
    {
        return new ListResponse<string>
        {
            Entities = permissionKeyLister.ListPermissionKeys(includeRoles: false).ToList()
        };
    }
}