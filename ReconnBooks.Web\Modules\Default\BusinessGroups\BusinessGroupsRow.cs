using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("BusinessGroups")]
[DisplayName("Business Groups"), InstanceName("Business Groups"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class BusinessGroupsRow : Row<BusinessGroupsRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }
    [DisplayName("Business Group Id"), Identity, IdProperty,Hidden]
    public int? BusinessGroupId { get => fields.BusinessGroupId[this]; set => fields.BusinessGroupId[this] = value; }

    [DisplayName("Business Group"), Size(150), NotNull, QuickSearch, NameProperty]
    public string BusinessGroup { get => fields.BusinessGroup[this]; set => fields.BusinessGroup[this] = value; }

    [DisplayName("Description"), Size(500)]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }
}