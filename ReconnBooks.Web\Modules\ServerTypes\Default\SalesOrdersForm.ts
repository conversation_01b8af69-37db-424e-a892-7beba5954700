﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, DecimalEditor, TextAreaEditor, BooleanEditor, MultipleImageUploadEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { PaymentTermsDialog } from "../../Default/PaymentTerms/PaymentTermsDialog";
import { QuotationsDialog } from "../../Default/Quotations/QuotationsDialog";
import { SalesOrderDetailsGridEditor } from "../../Default/SalesOrderDetails/SalesOrderDetailsGridEditor";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";

export interface SalesOrdersForm {
    SalesOrderNo: StringEditor;
    SalesOrderDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    ShipToCustomerId: ServiceLookupEditor;
    ShippingAddress: StringEditor;
    ShippingCityName: StringEditor;
    ShippingPinCode: StringEditor;
    ShippingGSTIN: StringEditor;
    ShippingPlaceOfSupplyStateName: StringEditor;
    OrderRefNo: StringEditor;
    OrderRefDate: DateEditor;
    QuotationId: ServiceLookupEditor;
    DeliveryDueDate: DateEditor;
    SalesOrderDetailsList: SalesOrderDetailsGridEditor;
    PaymentTermsId: ServiceLookupEditor;
    GrandTotal: DecimalEditor;
    Inspection: StringEditor;
    Remarks: TextAreaEditor;
    AuthorizedStatus: BooleanEditor;
    UploadOrderCopy: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateTimeEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class SalesOrdersForm extends PrefixedContext {
    static readonly formKey = 'Default.SalesOrders';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SalesOrdersForm.init)  {
            SalesOrdersForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = SalesOrderDetailsGridEditor;
            var w5 = DecimalEditor;
            var w6 = TextAreaEditor;
            var w7 = BooleanEditor;
            var w8 = MultipleImageUploadEditor;
            var w9 = DateTimeEditor;

            initFormType(SalesOrdersForm, [
                'SalesOrderNo', w0,
                'SalesOrderDate', w1,
                'CustomerId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'ShipToCustomerId', w2,
                'ShippingAddress', w0,
                'ShippingCityName', w0,
                'ShippingPinCode', w0,
                'ShippingGSTIN', w0,
                'ShippingPlaceOfSupplyStateName', w0,
                'OrderRefNo', w0,
                'OrderRefDate', w1,
                'QuotationId', w2,
                'DeliveryDueDate', w1,
                'SalesOrderDetailsList', w4,
                'PaymentTermsId', w2,
                'GrandTotal', w5,
                'Inspection', w0,
                'Remarks', w6,
                'AuthorizedStatus', w7,
                'UploadOrderCopy', w8,
                'PreparedByUserId', w3,
                'PreparedDate', w9,
                'VerifiedByUserId', w3,
                'VerifiedDate', w9,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w9,
                'ModifiedByUserId', w3,
                'ModifiedDate', w9,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, SupplyTypesDialog, QuotationsDialog, PaymentTermsDialog]); // referenced dialogs