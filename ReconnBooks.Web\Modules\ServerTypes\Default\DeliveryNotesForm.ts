﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, TextAreaEditor, BooleanEditor, MultipleImageUploadEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { DeliveryNoteDetailsGridEditor } from "../../Default/DeliveryNoteDetails/DeliveryNoteDetailsGridEditor";
import { QuotationsDialog } from "../../Default/Quotations/QuotationsDialog";
import { SalesOrdersDialog } from "../../Default/SalesOrders/SalesOrdersDialog";
import { SupplyTypesDialog } from "../../Default/SupplyTypes/SupplyTypesDialog";

export interface DeliveryNotesForm {
    DeliveryNoteNo: StringEditor;
    DeliveryNoteDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PlaceOfSupplyStateName: StringEditor;
    SupplyTypeId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    EWayBillNo: StringEditor;
    EWayBillNoDate: DateEditor;
    BillingAddress: StringEditor;
    BillingCityCityName: StringEditor;
    BillingPinCode: StringEditor;
    ShipToCustomerId: ServiceLookupEditor;
    ShippingAddress: StringEditor;
    ShippingCityName: StringEditor;
    ShippingPinCode: StringEditor;
    ShippingGSTIN: StringEditor;
    ShippingPlaceOfSupplyStateName: StringEditor;
    OrderRefNo: StringEditor;
    OrderRefDate: DateEditor;
    SalesOrderId: ServiceLookupEditor;
    QuotationId: ServiceLookupEditor;
    ReasonToTransport: StringEditor;
    DeliveryNoteDetailsList: DeliveryNoteDetailsGridEditor;
    ShippedVia: StringEditor;
    ShippingDocketNo: StringEditor;
    VehicleNo: StringEditor;
    Inspection: StringEditor;
    Remarks: TextAreaEditor;
    AuthorizedStatus: BooleanEditor;
    UploadDocketCopy: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class DeliveryNotesForm extends PrefixedContext {
    static readonly formKey = 'Default.DeliveryNotes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!DeliveryNotesForm.init)  {
            DeliveryNotesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = DeliveryNoteDetailsGridEditor;
            var w5 = TextAreaEditor;
            var w6 = BooleanEditor;
            var w7 = MultipleImageUploadEditor;
            var w8 = DateTimeEditor;

            initFormType(DeliveryNotesForm, [
                'DeliveryNoteNo', w0,
                'DeliveryNoteDate', w1,
                'CustomerId', w2,
                'GSTIN', w0,
                'PlaceOfSupplyStateName', w0,
                'SupplyTypeId', w2,
                'FinancialYearId', w3,
                'EWayBillNo', w0,
                'EWayBillNoDate', w1,
                'BillingAddress', w0,
                'BillingCityCityName', w0,
                'BillingPinCode', w0,
                'ShipToCustomerId', w2,
                'ShippingAddress', w0,
                'ShippingCityName', w0,
                'ShippingPinCode', w0,
                'ShippingGSTIN', w0,
                'ShippingPlaceOfSupplyStateName', w0,
                'OrderRefNo', w0,
                'OrderRefDate', w1,
                'SalesOrderId', w2,
                'QuotationId', w2,
                'ReasonToTransport', w0,
                'DeliveryNoteDetailsList', w4,
                'ShippedVia', w0,
                'ShippingDocketNo', w0,
                'VehicleNo', w0,
                'Inspection', w0,
                'Remarks', w5,
                'AuthorizedStatus', w6,
                'UploadDocketCopy', w7,
                'PreparedByUserId', w3,
                'PreparedDate', w8,
                'VerifiedByUserId', w3,
                'VerifiedDate', w8,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w8,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, SupplyTypesDialog, SalesOrdersDialog, QuotationsDialog]); // referenced dialogs