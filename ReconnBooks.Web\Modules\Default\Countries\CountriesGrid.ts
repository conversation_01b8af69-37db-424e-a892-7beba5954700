import { CountriesColumns, CountriesRow, CountriesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { CountriesDialog } from './CountriesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.CountriesGrid')
export class CountriesGrid extends EntityGridDialog<CountriesRow, any> {
    protected getColumnsKey() { return CountriesColumns.columnsKey; }
    protected getDialogType() { return CountriesDialog; }
    protected getRowDefinition() { return CountriesRow; }
    protected getService() { return CountriesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}