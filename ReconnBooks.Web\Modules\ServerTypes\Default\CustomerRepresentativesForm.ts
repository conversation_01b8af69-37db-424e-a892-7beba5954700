﻿import { IntegerEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface CustomerRepresentativesForm {
    CustomerId: IntegerEditor;
    EmployeeId: IntegerEditor;
}

export class CustomerRepresentativesForm extends PrefixedContext {
    static readonly formKey = 'Default.CustomerRepresentatives';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!CustomerRepresentativesForm.init)  {
            CustomerRepresentativesForm.init = true;

            var w0 = IntegerEditor;

            initFormType(CustomerRepresentativesForm, [
                'CustomerId', w0,
                'EmployeeId', w0
            ]);
        }
    }
}