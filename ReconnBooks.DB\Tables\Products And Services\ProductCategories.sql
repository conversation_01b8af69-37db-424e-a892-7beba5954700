﻿CREATE TABLE [dbo].[ProductCategories] 
(
    [ProductCategoryId] INT             NOT NULL    IDENTITY (1, 1),
    [CategoryName]      NVARCHAR (50)   NOT NULL,
    [Description]       NTEXT               NULL,
    [ClientId]          INT             NOT NULL    CONSTRAINT [DF_Categories_ClientId] DEFAULT ((0)),

    CONSTRAINT [PK_Categories] PRIMARY KEY  CLUSTERED   ([ProductCategoryId] ASC),
    CONSTRAINT [FK_Categories_Clients]      FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId])
);
GO
CREATE NONCLUSTERED INDEX [CategoryName]
    ON [dbo].[ProductCategories]([CategoryName] ASC);